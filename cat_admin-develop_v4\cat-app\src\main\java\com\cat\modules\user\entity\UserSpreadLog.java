package com.cat.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class UserSpreadLog extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId("id")
    @TableField("id")
    private String id;

    

    @TableField("parent_user_id")
    private String parentUserId;

    

    @TableField("parent_user_ids")
    private String parentUserIds;

    
    @TableField("level")
    private Integer level;

    

    @TableField("status")
    private Integer status;

    

    @TableField("source_type")
    private Integer sourceType;

    
    @TableField("mobile")
    private String mobile;

    
    @TableField("user_id")
    private String userId;


    

    @TableField("depict")
    private String depict;

    

    @TableField("update_date")
    private LocalDateTime updateDate;

    

    @TableField("remark")
    private String remark;



}
