package com.cat.job;


import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;


@Slf4j
@Component("userSignClearJob")
public class UserSignClearJob {

    @Autowired
    private RedisUtil redisUtil;

    private String signKey = "we_chat:sign:*";


    
    
    public void execude(){
        Set<String> keys = redisUtil.keys(signKey);
        keys.forEach(item ->{
            redisUtil.del(item);
        });
        log.info("=======清理redis签到数据完成=======");
    }


}
