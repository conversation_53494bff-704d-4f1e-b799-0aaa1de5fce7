package com.jeefast.cat.controller;

import java.util.*;

import cn.hutool.core.util.ObjectUtil;
import com.cat.modules.product.entity.ProductGroupKey;
import com.cat.modules.product.entity.ProductGroupVal;
import com.cat.modules.product.entity.ProductSku;
import com.cat.modules.product.req.ProductSkuAddExtReq;
import com.cat.modules.product.req.ProductSkuGroupAddReq;
import com.cat.modules.product.req.ProductSkuGroupListReq;
import com.cat.modules.product.service.IProductGroupKeyService;
import com.cat.modules.product.service.IProductGroupValService;
import com.cat.modules.product.service.IProductSkuService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 商品skuController
 * 
 * <AUTHOR>
 * @date 2020-12-19
 */
@Controller
@RequestMapping("/cat/productSku")
public class ProductSkuController extends BaseController {
    private String prefix = "cat/productSku";

    @Autowired
    private IProductSkuService productSkuService;
    @Autowired
    private IProductGroupKeyService groupKeyService;
    @Autowired
    private IProductGroupValService productGroupValService;


    @RequiresPermissions("cat:productSku:view")
    @GetMapping()
    public String productSku() {
        return prefix + "/productSku";
    }

    /**
     * 查询商品sku列表
     */
    @RequiresPermissions("cat:productSku:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProductSku req) {
        QueryWrapper<ProductSku> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
        if(StringUtils.isNotEmpty(req.getProductId())) {
            queryWrapper.eq("product_id", req.getProductId());
        }
        if(StringUtils.isNotEmpty(req.getGroupKeyId())) {
            queryWrapper.eq("group_key_id", req.getGroupKeyId());
        }
        if(StringUtils.isNotEmpty(req.getGroupValId())) {
            queryWrapper.eq("group_val_id", req.getGroupValId());
        }
        if(ObjectUtil.isNotNull(req.getPrice())) {
    		queryWrapper.gt("price", req.getPrice());
    	}
    	if(ObjectUtil.isNotNull(req.getStatus())) {
    		queryWrapper.eq("status", req.getStatus());
    	}
        if(StringUtils.isNotEmpty(req.getShopId())) {
            queryWrapper.eq("shop_id", req.getShopId());
        }
		// 特殊查询时条件需要进行单独组装
        Map<String, Object> params = req.getParams();
        if (StringUtils.isNotEmpty(params)) {
            queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
            queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endCreateDate")), "create_date", params.get("endCreateDate"));
        }
        startPage();
        return getDataTable(productSkuService.list(queryWrapper));
    }

    /**
     * 导出商品sku列表
     */
    @RequiresPermissions("cat:productSku:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ProductSku productSku) {
        List<ProductSku> list = productSkuService.list(new QueryWrapper<>());
        ExcelUtil<ProductSku> util = new ExcelUtil<ProductSku>(ProductSku.class);
        return util.exportExcel(list, "productSku");
    }

    /**
     * 新增商品sku
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存商品sku
     */
    @RequiresPermissions("cat:productSku:add")
    @Log(title = "商品sku", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ProductSku productSku) {
        return toAjax(productSkuService.save(productSku));
    }

    /**
     * 修改商品sku
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        ProductSku productSku = productSkuService.getById(id);
        mmap.put("productSku", productSku);
        return prefix + "/edit";
    }

    /**
     * 修改保存商品sku
     */
    @RequiresPermissions("cat:productSku:edit")
    @Log(title = "商品sku", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ProductSku productSku) {
        return toAjax(productSkuService.updateById(productSku));
    }

    /**
     * 删除商品sku
     */
    @RequiresPermissions("cat:productSku:remove")
    @Log(title = "商品sku", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(productSkuService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }


    @PostMapping("/getByProductId")
    @ResponseBody
    public AjaxResult getByProductId(@RequestBody ProductSkuGroupListReq req){
        List<ProductSku> skuList = productSkuService.list(new QueryWrapper<ProductSku>().eq("is_delete","0").eq("product_id",req.getProductId()));
        return AjaxResult.success(skuList);
    }


    /**
     * vue新增保存商品sku
     */
    @PostMapping("/addExt")
    @ResponseBody
    public AjaxResult addExt(@RequestBody ProductSkuAddExtReq req) {
        //添加产品id
        req.getSkuList().forEach(item->{
            item.setProductId(req.getProductId());
        });
        //批量插入
        return toAjax(productSkuService.saveBatch(req.getSkuList()));
    }

    /**
     * vue新增保存商品sku
     */
    @PostMapping("/updExt")
    @ResponseBody
    public AjaxResult updExt(@RequestBody ProductSkuAddExtReq productSku) {
        return toAjax(productSkuService.updExt(productSku));
    }


    /***
     * sku分组列表
     * @return
     */
    @PostMapping("/groupList")
    @ResponseBody
    public AjaxResult groupList(@RequestBody ProductSkuGroupListReq req){
        List<ProductGroupKey> groupKeyList = groupKeyService.list();
        List<ProductSkuGroupAddReq> resp = new LinkedList<>();
        groupKeyList.forEach(item->{
            ProductSkuGroupAddReq groupKey = new ProductSkuGroupAddReq();
            groupKey.setValue(item.getName());
            groupKey.setId(item.getId());
            groupKey.setLeaf(productGroupValService.list(new QueryWrapper<ProductGroupVal>()
                    .eq("key_id",groupKey.getId())
                    .orderByAsc("sort_on"))
            );
            resp.add(groupKey);
        });
        return AjaxResult.success(resp);
    }


}
