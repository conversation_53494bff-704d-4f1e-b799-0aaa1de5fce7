package com.cat.modules.sys.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.sys.entity.SysLog;
import com.cat.modules.sys.mapper.SysLogMapper;
import com.cat.modules.sys.service.ISysLogService;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service

public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLog> implements ISysLogService {

    @Autowired
    private SysLogMapper sysLogMapper;



}
