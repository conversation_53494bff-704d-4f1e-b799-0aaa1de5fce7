package com.cat.modules.shop.service.impl;

import com.cat.modules.shop.entity.ShopPhotos;
import com.cat.modules.shop.mapper.ShopPhotosMapper;
import com.cat.modules.shop.service.IShopPhotosService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import org.springframework.beans.factory.annotation.*;
import org.springframework.transaction.annotation.*;

import lombok.extern.slf4j.Slf4j;
import java.util.*;

@Slf4j
@Service
public class ShopPhotosServiceImpl extends ServiceImpl<ShopPhotosMapper, ShopPhotos> implements IShopPhotosService {
    @Autowired
    private ShopPhotosMapper shopPhotosMapper;


}
