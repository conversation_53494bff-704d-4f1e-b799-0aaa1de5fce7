package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.cat.modules.complain.entity.ComplainInfo;
import com.cat.modules.complain.service.IComplainInfoService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 用户投诉Controller
 * 
 * <AUTHOR>
 * @date 2021-01-14
 */
@Controller
@RequestMapping("/cat/complainInfo")
public class ComplainInfoController extends BaseController {
    private String prefix = "cat/complainInfo";

    @Autowired
    private IComplainInfoService complainInfoService;

    @RequiresPermissions("cat:complainInfo:view")
    @GetMapping()
    public String complainInfo() {
        return prefix + "/complainInfo";
    }

    /**
     * 查询用户投诉列表
     */
    @RequiresPermissions("cat:complainInfo:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ComplainInfo req) {
        QueryWrapper<ComplainInfo> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getBusinessType())) {
    		queryWrapper.eq("business_type", req.getBusinessType());
    	} 
    	if(StringUtils.isNotEmpty(req.getType())) {
    		queryWrapper.eq("type", req.getType());
    	}
		// 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endCreateDate")), "create_date", params.get("endCreateDate"));
		}
        queryWrapper.orderByDesc("create_date");
        startPage();
        return getDataTable(complainInfoService.list(queryWrapper));
    }

    /**
     * 导出用户投诉列表
     */
    @RequiresPermissions("cat:complainInfo:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ComplainInfo complainInfo) {
        List<ComplainInfo> list = complainInfoService.list(new QueryWrapper<>());
        ExcelUtil<ComplainInfo> util = new ExcelUtil<ComplainInfo>(ComplainInfo.class);
        return util.exportExcel(list, "complainInfo");
    }

    /**
     * 新增用户投诉
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存用户投诉
     */
    @RequiresPermissions("cat:complainInfo:add")
    @Log(title = "用户投诉", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ComplainInfo complainInfo) {
        return toAjax(complainInfoService.save(complainInfo));
    }

    /**
     * 修改用户投诉
     */
    @GetMapping("/edit/{complainId}")
    public String edit(@PathVariable("complainId") String complainId, ModelMap mmap) {
        ComplainInfo complainInfo = complainInfoService.getById(complainId);
        mmap.put("complainInfo", complainInfo);
        return prefix + "/edit";
    }

    /**
     * 修改保存用户投诉
     */
    @RequiresPermissions("cat:complainInfo:edit")
    @Log(title = "用户投诉", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ComplainInfo complainInfo) {
        return toAjax(complainInfoService.updateById(complainInfo));
    }

    /**
     * 删除用户投诉
     */
    @RequiresPermissions("cat:complainInfo:remove")
    @Log(title = "用户投诉", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(complainInfoService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
