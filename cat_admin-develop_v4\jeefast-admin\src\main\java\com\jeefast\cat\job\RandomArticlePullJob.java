package com.jeefast.cat.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cat.modules.dynamic.entity.DynamicInfo;
import com.cat.modules.dynamic.mapper.DynamicInfoMapper;
import com.cat.modules.dynamic.req.DynamicRecommendReqVo;
import com.cat.modules.dynamic.resp.DynamicInfoRespVo;
import com.cat.modules.user.entity.User;
import com.cat.modules.user.mapper.UserMapper;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component("randomArticlePullJob")
public class RandomArticlePullJob {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private DynamicInfoMapper dynamicInfoMapper;

    @Autowired
    private UserMapper userMapper;

    private final static String RANDOM_ARTICLE_CONTENT = "random.article.content";

    private static final String TARGET_ADDR = "sys_config:sys.random.path";

    public void pull() {
        log.info("定时任务调用开始。。。。");
        Date now = new Date();
        long sevenDaysInMillis = 7 * 24 * 60 * 60 * 1000L;

        Date sevenDaysAgo = new Date(now.getTime() - sevenDaysInMillis);
        List<DynamicInfo> dynamicInfos = dynamicInfoMapper.selectList(new LambdaQueryWrapper<DynamicInfo>()
                .eq(DynamicInfo::getIsDelete, 0)
                .ne(DynamicInfo::getType, 3)); ///视频
        List<DynamicInfo> wechatList = dynamicInfos.stream()
                .filter(dynamicInfo -> "wechat".equals(dynamicInfo.getArticleType())
                        && dynamicInfo.getCreateDate() != null
                        && dynamicInfo.getCreateDate().after(sevenDaysAgo))
                .collect(Collectors.toList());
        List<DynamicInfo> tempList = dynamicInfos.stream()
                .filter(i -> !i.getArticleType().equals("wechat") && !i.getArticleType().equals("morning") && !i.getArticleType().equals("noon")).collect(Collectors.toList());
        tempList.addAll(wechatList);
        List<String> userIdList = tempList.stream().map(DynamicInfo::getUserId).collect(Collectors.toList());
        List<User> userList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(userIdList)) {
            userList = userMapper.selectList(new LambdaQueryWrapper<User>().in(User::getUserId, userIdList));
        }
        List<DynamicInfoRespVo> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(tempList)) {
            for (DynamicInfo dynamicInfo : tempList) {
                DynamicInfoRespVo respVo = new DynamicInfoRespVo();
                if (StringUtils.isEmpty(dynamicInfo.getTitle())) {
                    continue;
                }
                respVo.setDynamicId(dynamicInfo.getDynamicId());
                respVo.setSource(dynamicInfo.getSource());
                respVo.setTitle(dynamicInfo.getTitle());
                respVo.setContent(dynamicInfo.getContent());
                respVo.setCoverImage(dynamicInfo.getCoverImage());
                respVo.setArticleType(dynamicInfo.getArticleType());
                userList.stream().filter(i -> dynamicInfo.getUserId().equals(i.getUserId())).findFirst()
                        .ifPresent(user -> respVo.setUserName(user.getUserName()));

                List<DynamicRecommendReqVo> recommendReqVoList = new ArrayList<>();
                DynamicRecommendReqVo recommendReqVo = new DynamicRecommendReqVo();
                recommendReqVo.setRole("assistant");
                recommendReqVo.setContent(dynamicInfo.getTitle());
                recommendReqVoList.add(recommendReqVo);
                JSONObject reqJsonObject = new JSONObject();
                reqJsonObject.put("messages", JSONArray.parseArray(JSONObject.toJSONString(recommendReqVoList)));
                String url = redisUtil.get(TARGET_ADDR) + "/ocr/post_recommend_question";
                log.info("========请求url:{}=========",url);
//                HttpResponse response = HttpRequest.post(url)
//                        .header("content-type", "application/json")
//                        .body(reqJsonObject.toJSONString())
//                        .execute();
//                log.info("请求响应内容:{}",response.body());
                List<String> questionList = new ArrayList<>();
//                JSONObject jsonObject = JSON.parseObject(response.body());

//                JSONObject jsonObject = JSON.parseObject("{\"code\":200,\"msg\":\"OK\",\"data\":{\"question_list\":[\"茶叶的分类有哪些？\",\"如何正确冲泡绿茶？\",\"喝茶对健康有哪些益处？\"]}}");

//                if (jsonObject.getInteger("code") == 200) {
//                    JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("question_list");
//                    for (int i = 0; i < jsonArray.size(); i++) {
//                        questionList.add(jsonArray.getString(i));
//                    }
//                    respVo.setQuestionList(questionList);
//                }
                list.add(respVo);
            }
        } else {
            log.info("查询发现列表异常。。。");
        }

        //放置redis中
        redisUtil.del(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT);
        redisUtil.set(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT,JSON.toJSONString(list));
        String result = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + RANDOM_ARTICLE_CONTENT);
        log.info("=====更新后随机文章内容=====:{}",result);
        log.info("定时任务调用结束。。。。");
    }
}
