package com.cat.modules.stats.mapper;

import cn.hutool.core.date.DateTime;
import com.cat.modules.stats.entity.StatsAppVisit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cat.util.DataRow;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


public interface StatsAppVisitMapper extends BaseMapper<StatsAppVisit> {

    
    StatsAppVisit nowVisit();

    
    DataRow realActiveBetweenDay(@Param("event") String event,@Param("start") String start,@Param("end") String end);

    
    List<DataRow> platformPieChart(@Param("start") Date start,@Param("end") Date end);

    
    List<DataRow> versionPieChart(@Param("start") Date start,@Param("end") Date end);

    
    List<DataRow> brandPieChart(@Param("start") Date start,@Param("end") Date end);

    
    List<DataRow> channel<PERSON>ie<PERSON><PERSON>(@Param("start") Date start,@Param("end") Date end);

    
    List<DataRow> userTrendChart(@Param("start") Date start,@Param("end") Date end);

    List<DataRow> registerUser(@Param("start") Date start,@Param("end") Date end);
}
