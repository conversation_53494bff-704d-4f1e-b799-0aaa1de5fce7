package com.cat.modules.sys.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.sys.entity.SysCity;
import com.cat.modules.sys.mapper.SysCityMapper;
import com.cat.modules.sys.service.ISysCityService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import org.springframework.beans.factory.annotation.*;
import org.springframework.transaction.annotation.*;

import lombok.extern.slf4j.Slf4j;
import java.util.*;

@Slf4j
@Service
public class SysCityServiceImpl extends ServiceImpl<SysCityMapper, SysCity> implements ISysCityService {
    @Autowired
    private SysCityMapper sysCityMapper;


}
