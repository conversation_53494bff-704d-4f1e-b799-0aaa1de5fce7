<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.attention.mapper.AttentionInfoMapper">

    <!-- 通用查询映射结果  配置了autoMapping=true,其他的无需配置，将会自动映射-->
    <resultMap id="BaseResultMap" type="com.cat.modules.attention.entity.AttentionInfo" autoMapping="true" />

    <update id="updateToUserNewDynamicNum">
        update attention_info set to_user_new_dynamic_num=(to_user_new_dynamic_num + #{diffNum}),to_user_last_dynamic_date = now()
        where to_user_id=#{toUserId} and to_type='1'
        <if test="dynamicDate != null">
            and user_last_view_date &lt; #{dynamicDate}
        </if>
    </update>

</mapper>
