package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.cat.modules.product.entity.ProductGroupKey;
import com.cat.modules.product.service.IProductGroupKeyService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 商品sku_keyController
 * 
 * <AUTHOR>
 * @date 2020-12-19
 */
@Controller
@RequestMapping("/cat/productGroupKey")
public class ProductGroupKeyController extends BaseController {
    private String prefix = "cat/productGroupKey";

    @Autowired
    private IProductGroupKeyService productGroupKeyService;

    @RequiresPermissions("cat:productGroupKey:view")
    @GetMapping()
    public String productGroupKey() {
        return prefix + "/productGroupKey";
    }

    /**
     * 查询商品sku_key列表
     */
    @RequiresPermissions("cat:productGroupKey:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProductGroupKey req) {
        QueryWrapper<ProductGroupKey> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getProductId())) {
    		queryWrapper.eq("product_id", req.getProductId());
    	} 
    	if(StringUtils.isNotEmpty(req.getName())) {
    		queryWrapper.like("name", req.getName());
    	}
        if(StringUtils.isNotEmpty(req.getShopId())) {
            queryWrapper.eq("shop_id", req.getShopId());
        }
		// 特殊查询时条件需要进行单独组装
        Map<String, Object> params = req.getParams();
        if (StringUtils.isNotEmpty(params)) {
            queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
            queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endCreateDate")), "create_date", params.get("endCreateDate"));
        }
        startPage();
        return getDataTable(productGroupKeyService.list(queryWrapper));
    }

    /**
     * 导出商品sku_key列表
     */
    @RequiresPermissions("cat:productGroupKey:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ProductGroupKey productGroupKey) {
        List<ProductGroupKey> list = productGroupKeyService.list(new QueryWrapper<>());
        ExcelUtil<ProductGroupKey> util = new ExcelUtil<ProductGroupKey>(ProductGroupKey.class);
        return util.exportExcel(list, "productGroupKey");
    }

    /**
     * 新增商品sku_key
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存商品sku_key
     */
    @RequiresPermissions("cat:productGroupKey:add")
    @Log(title = "商品sku_key", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ProductGroupKey productGroupKey) {
        productGroupKey.setUpdateDate(new Date());
        return toAjax(productGroupKeyService.save(productGroupKey));
    }

    /**
     * 修改商品sku_key
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        ProductGroupKey productGroupKey = productGroupKeyService.getById(id);
        mmap.put("productGroupKey", productGroupKey);
        return prefix + "/edit";
    }

    /**
     * 修改保存商品sku_key
     */
    @RequiresPermissions("cat:productGroupKey:edit")
    @Log(title = "商品sku_key", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ProductGroupKey productGroupKey) {
        productGroupKey.setUpdateDate(new Date());
        return toAjax(productGroupKeyService.updateById(productGroupKey));
    }

    /**
     * 删除商品sku_key
     */
    @RequiresPermissions("cat:productGroupKey:remove")
    @Log(title = "商品sku_key", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(productGroupKeyService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
