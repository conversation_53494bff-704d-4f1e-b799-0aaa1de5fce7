package com.jeefast.web.controller.common;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.img.Img;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.cat.modules.common.entity.MediaInfo;
import com.cat.util.FFMpegUtil;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.SpringContextHolder;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.jeefast.common.config.Global;
import com.jeefast.common.config.ServerConfig;
import com.jeefast.common.constant.Constants;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.file.FileUploadUtils;
import com.jeefast.common.utils.file.FileUtils;

import java.awt.image.BufferedImage;
import java.io.File;
import java.util.LinkedList;
import java.util.List;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@Controller
public class CommonController {
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private FFMpegUtil ffMpegUtil;

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete   是否删除
     */
    @GetMapping("common/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        try {
            if (!FileUtils.isValidFilename(fileName)) {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            String filePath = Global.getDownloadPath() + fileName;

            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, realFileName));
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete) {
                FileUtils.deleteFile(filePath);
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求
     */
    @PostMapping("/common/upload")
    @ResponseBody
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            //String filePath = Global.getUploadPath();
            // 上传并返回新文件名称
            String fileName = ""; //FileUploadUtils.upload(filePath, file);
            //String url = serverConfig.getUrl() + fileName;

            String url = FileUploadUtils.uploadExt(file);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", fileName);
            ajax.put("url", url);
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }


    /**
     * 通用多文件上传请求
     */
    @PostMapping("/common/uploadMany")
    @ResponseBody
    public AjaxResult uploadFileMany(@RequestParam("file_data") MultipartFile[] files, String fileType, String fileSource) throws Exception {
        try {
            if (StrUtil.isEmpty(fileType)) {
                return AjaxResult.error("请先选择文件类型");
            }
            List<MediaInfo> fileUrlList = new LinkedList<MediaInfo>();
            for (MultipartFile file : files) {
                MediaInfo mediaInfo = new MediaInfo();
                //2：图片、3：视频
                if (!"2".equals(fileType) && !("3").equals(fileType) && !("4").equals(fileType)) {
                    throw new Exception("当前动态类型不为图片和视频，请修改后重新上传");
                }
                if ("2".equals(fileType) || "4".equals(fileType)) {
                    BufferedImage image = ImageIO.read(file.getInputStream());
                    mediaInfo.setWidth(image.getWidth());
                    mediaInfo.setHeight(image.getHeight());
                    String fileName = IdUtil.fastSimpleUUID() + "." + FileUtil.extName(file.getOriginalFilename());
                    RedisUtil redisUtil = SpringContextHolder.getBean(RedisUtil.class);
                    String fileTempPath = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + "sys.upload.path");
                    File tempFile = new File(fileTempPath + fileName);
                    Img.from(file.getInputStream()).scale((float) 0.6).setQuality(0.8).write(tempFile);
                    String coverUrl = FileUploadUtils.uploadExt(tempFile);
                    mediaInfo.setSmall(coverUrl);
                } else if ("3".equals(fileType)) {
                    //视频通过截图封面图获取视频高宽并上传封面图
                    String fileName = IdUtil.fastSimpleUUID() + "." + FileUtil.extName(file.getOriginalFilename());
                    RedisUtil redisUtil = SpringContextHolder.getBean(RedisUtil.class);
                    String fileTempPath = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + "sys.upload.path");
                    File tempFile = new File(fileTempPath + fileName);
                    org.apache.commons.io.FileUtils.copyInputStreamToFile(file.getInputStream(), tempFile);
                    String coverImage = ffMpegUtil.processImg(tempFile.getPath(), 1);
                    File coverFile = FileUtil.file(coverImage);
                    String coverUrl = FileUploadUtils.uploadExt(coverFile);
                    //获取封面图高宽
                    BufferedImage image = ImageIO.read(coverFile);
                    mediaInfo.setWidth(image.getWidth());
                    mediaInfo.setHeight(image.getHeight());
                    mediaInfo.setSmall(coverUrl);
                }
                String url = FileUploadUtils.uploadExt(file);
                mediaInfo.setUrl(url);
                mediaInfo.setSize(file.getSize());
                fileUrlList.add(mediaInfo);
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileUrlList", fileUrlList);
            return ajax;
        } catch (Exception e) {
            log.error("", e);
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/common/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response)
            throws Exception {
        // 本地资源路径
        String localPath = Global.getProfile();
        // 数据库资源地址
        String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
        // 下载名称
        String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-data");
        response.setHeader("Content-Disposition",
                "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, downloadName));
        FileUtils.writeBytes(downloadPath, response.getOutputStream());
    }
}
