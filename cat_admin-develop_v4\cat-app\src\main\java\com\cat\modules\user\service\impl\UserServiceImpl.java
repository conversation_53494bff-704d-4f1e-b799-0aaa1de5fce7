package com.cat.modules.user.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.user.entity.User;
import com.cat.modules.user.mapper.UserMapper;
import com.cat.modules.user.service.UserService;
import com.cat.util.DataRow;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service

public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserMapper userMapper;


    @Override
    public String getUserId() {
        return userMapper.getUserId();
    }

    @Override
    public List<String> randUserAvatar(int i) {
        return userMapper.randUserAvatar(i);
    }

    @Override
    public String getRandPetUserId() {
        return userMapper.getRandPetUserId();
    }

    @Override
    public String getByOpenId(String toUserId) {
        return userMapper.getByOpenId(toUserId);
    }

    @Override
    public DataRow getByIdPushInfo(String userId) {
        return userMapper.getByIdPushInfo(userId);
    }
    @Override
    public Integer updateFamilyUserNewDynamicNum(String familyUserId, Integer diffNum, Date dynamicDate) {
        return userMapper.updateFamilyUserNewDynamicNum(familyUserId, diffNum,dynamicDate);
    }

}
