package com.jeefast.cat.controller;

import java.io.IOException;
import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.cat.modules.sys.entity.AppUpdateMessage;
import com.cat.modules.sys.service.IAppUpdateMessageService;
import com.jeefast.common.utils.file.FileUploadUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * APP更新Controller
 * 
 * <AUTHOR>
 * @date 2020-11-17
 */
@Controller
@RequestMapping("/cat/appUpdateMessageBackstage")
public class AppUpdateMessageBackstageController extends BaseController {
    private String prefix = "cat/appUpdateMessageBackstage";

    @Autowired
    private IAppUpdateMessageService appUpdateMessageService;

    @RequiresPermissions("cat:appUpdateMessageBackstage:view")
    @GetMapping()
    public String appUpdateMessageBackstage() {
        return prefix + "/appUpdateMessageBackstage";
    }

    /**
     * 查询APP更新列表
     */
    @RequiresPermissions("cat:appUpdateMessageBackstage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AppUpdateMessage req) {
        QueryWrapper<AppUpdateMessage> queryWrapper = new QueryWrapper<>();
        // 需要根据页面查询条件进行组装
        if (ObjectUtil.isNotNull(req.getForceUpdate())) {
            queryWrapper.eq("force_update", req.getForceUpdate());
        }
        if (ObjectUtil.isNotNull(req.getClient())) {
            queryWrapper.eq("client", req.getClient());
        }
        if (ObjectUtil.isNotNull(req.getUpdateType())) {
            queryWrapper.eq("update_type", req.getUpdateType());
        }
        if (StrUtil.isNotEmpty(req.getVersionName())) {
            queryWrapper.likeRight("version_name", req.getVersionName());
        }
        // 特殊查询时条件需要进行单独组装
        Map<String, Object> params = req.getParams();
        if (StringUtils.isNotEmpty(params)) {
            queryWrapper.ge(StringUtils.isNotEmpty((String) params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
            queryWrapper.le(StringUtils.isNotEmpty((String) params.get("endCreateDate")), "create_date", params.get("endCreateDate"));
        }
        startPage();
        queryWrapper.orderByDesc("create_date");
        return getDataTable(appUpdateMessageService.list(queryWrapper));
    }

    /**
     * 导出APP更新列表
     */
    @RequiresPermissions("cat:appUpdateMessageBackstage:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(AppUpdateMessage appUpdateMessageBackstage) {
        List<AppUpdateMessage> list = appUpdateMessageService.list(new QueryWrapper<>());
        ExcelUtil<AppUpdateMessage> util = new ExcelUtil<AppUpdateMessage>(AppUpdateMessage.class);
        return util.exportExcel(list, "appUpdateMessageBackstage");
    }

    /**
     * 新增APP更新
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存APP更新
     */
    @RequiresPermissions("cat:appUpdateMessageBackstage:add")
    @Log(title = "APP更新", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestParam("file") MultipartFile multipartFile,@RequestParam("apkEntireFile") MultipartFile apkEntireFile, AppUpdateMessage appUpdateMessageBackstage) throws IOException {

        /* 热更新包 */
        if (multipartFile != null && StrUtil.isNotEmpty(multipartFile.getOriginalFilename())) {
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            appUpdateMessageBackstage.setApkUrl(cloudPath);
            appUpdateMessageBackstage.setFileSize(getNetFileSize(multipartFile.getSize()));
        }
        /* 整包文件 */
        if (apkEntireFile != null && StrUtil.isNotEmpty(apkEntireFile.getOriginalFilename())) {
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(apkEntireFile);
            appUpdateMessageBackstage.setApkEntireUrl(cloudPath);
        }
        String versionName = appUpdateMessageBackstage.getVersionName();
//        String[] versions = versionName.split("\\.");
//        int version = Integer.valueOf(versions[0] + versions[1] + versions[2]);
        appUpdateMessageBackstage.setVersion(Integer.valueOf(versionName));
        //最新版本号
        String minVersionName = appUpdateMessageBackstage.getMinVersionName();
//        String[] minVersion = minVersionName.split("\\.");
//        int minvVersion = Integer.valueOf(minVersion[0] + minVersion[1] + minVersion[2]);
        appUpdateMessageBackstage.setMinVersion(Integer.valueOf(minVersionName));

        boolean falg = appUpdateMessageService.save(appUpdateMessageBackstage);
        //更新redis最大版本号
        if (falg) {
            appUpdateMessageService.refreshVersionCache();
        }
        return toAjax(falg);
    }

    /**
     * 修改APP更新
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        AppUpdateMessage appUpdateMessageBackstage = appUpdateMessageService.getById(id);
        mmap.put("appUpdateMessageBackstage", appUpdateMessageBackstage);
        return prefix + "/edit";
    }

    /**
     * 修改保存APP更新
     */
    @RequiresPermissions("cat:appUpdateMessageBackstage:edit")
    @Log(title = "APP更新", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestParam("file") MultipartFile multipartFile,@RequestParam("apkEntireFile") MultipartFile apkEntireFile, AppUpdateMessage appUpdateMessageBackstage) throws IOException {
        if (multipartFile != null && StrUtil.isNotEmpty(multipartFile.getOriginalFilename())) {
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            multipartFile.getSize();
            appUpdateMessageBackstage.setApkUrl(cloudPath);
            appUpdateMessageBackstage.setFileSize(getNetFileSize(multipartFile.getSize()));
        }
        /* 整包文件 */
        if (apkEntireFile != null && StrUtil.isNotEmpty(apkEntireFile.getOriginalFilename())) {
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(apkEntireFile);
            appUpdateMessageBackstage.setApkEntireUrl(cloudPath);
        }

        String versionName = appUpdateMessageBackstage.getVersionName();
//        String[] versions = versionName.split("\\.");
//        int version = Integer.valueOf(versions[0] + versions[1] + versions[2]);
        appUpdateMessageBackstage.setVersion(Integer.valueOf(versionName));
        boolean falg = appUpdateMessageService.updateById(appUpdateMessageBackstage);
        //更新redis最大版本号
        if (falg) {
            appUpdateMessageService.refreshVersionCache();
        }
        return toAjax(falg);
    }

    /**
     * 删除APP更新
     */
    @RequiresPermissions("cat:appUpdateMessageBackstage:remove")
    @Log(title = "APP更新", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(appUpdateMessageService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }


    public static String getNetFileSize(long size) {
        StringBuffer bytes = new StringBuffer();
        DecimalFormat format = new DecimalFormat("###.0");
        if (size >= 1024 * 1024 * 1024) {
            double i = (size / (1024.0 * 1024.0 * 1024.0));
            bytes.append(format.format(i)).append("GB");
        } else if (size >= 1024 * 1024) {
            double i = (size / (1024.0 * 1024.0));
            bytes.append(format.format(i)).append("MB");
        } else if (size >= 1024) {
            double i = (size / (1024.0));
            bytes.append(format.format(i)).append("KB");
        } else if (size < 1024) {
            if (size <= 0) {
                bytes.append("0B");
            } else {
                bytes.append((int) size).append("B");
            }
        }
        return bytes.toString();

    }
}