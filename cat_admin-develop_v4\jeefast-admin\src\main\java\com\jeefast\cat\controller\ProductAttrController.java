package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.cat.modules.product.entity.ProductAttr;
import com.cat.modules.product.service.IProductAttrService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 商品spuController
 * 
 * <AUTHOR>
 * @date 2020-12-19
 */
@Controller
@RequestMapping("/cat/productAttr")
public class ProductAttrController extends BaseController {
    private String prefix = "cat/productAttr";

    @Autowired
    private IProductAttrService productAttrService;

    @RequiresPermissions("cat:productAttr:view")
    @GetMapping()
    public String productAttr() {
        return prefix + "/productAttr";
    }

    /**
     * 查询商品spu列表
     */
    @RequiresPermissions("cat:productAttr:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProductAttr req) {
        QueryWrapper<ProductAttr> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getProductId())) {
    		queryWrapper.eq("product_id", req.getProductId());
    	} 
    	if(StringUtils.isNotEmpty(req.getName())) {
    		queryWrapper.like("name", req.getName());
    	}
        if(StringUtils.isNotEmpty(req.getValue())) {
            queryWrapper.like("value", req.getValue());
        }
        if(StringUtils.isNotEmpty(req.getShopId())) {
            queryWrapper.like("shop_id", req.getShopId());
        }
		// 特殊查询时条件需要进行单独组装
        Map<String, Object> params = req.getParams();
        if (StringUtils.isNotEmpty(params)) {
            queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
            queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endCreateDate")), "create_date", params.get("endCreateDate"));
        }
        startPage();
        return getDataTable(productAttrService.list(queryWrapper));
    }

    /**
     * 导出商品spu列表
     */
    @RequiresPermissions("cat:productAttr:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ProductAttr productAttr) {
        List<ProductAttr> list = productAttrService.list(new QueryWrapper<>());
        ExcelUtil<ProductAttr> util = new ExcelUtil<ProductAttr>(ProductAttr.class);
        return util.exportExcel(list, "productAttr");
    }

    /**
     * 新增商品spu
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存商品spu
     */
    @RequiresPermissions("cat:productAttr:add")
    @Log(title = "商品spu", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ProductAttr productAttr) {
        return toAjax(productAttrService.save(productAttr));
    }

    /**
     * 修改商品spu
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        ProductAttr productAttr = productAttrService.getById(id);
        mmap.put("productAttr", productAttr);
        return prefix + "/edit";
    }

    /**
     * 修改保存商品spu
     */
    @RequiresPermissions("cat:productAttr:edit")
    @Log(title = "商品spu", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ProductAttr productAttr) {
        return toAjax(productAttrService.updateById(productAttr));
    }

    /**
     * 删除商品spu
     */
    @RequiresPermissions("cat:productAttr:remove")
    @Log(title = "商品spu", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(productAttrService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
