package com.jeefast.cat.domain;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.jeefast.common.core.entity.CatBaseEntity;

/**
 * 动态内容表 dynamic_info
 *
 * <AUTHOR>
 * @date 2020-08-08
 */


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("nurse_bill_ele")
public class NurseBillModel extends CatBaseEntity implements Serializable {

    /** id */
    @TableId(value = "ele_id")
    private BigInteger eleId;

    /**
     * 主元素，对应Python模型中的ele_master
     */
    private String eleMaster;

    /**
     * 元素名称
     */
    private String eleName;

    /**
     * 元素类型
     */
    private String eleType;

    /**
     * 元素备注
     */
    private String eleRemarks;

    /**
     * 是否启用
     */
    private String isStart;

    /**
     * 标准单位，对应Python模型中的base_unit
     */
    private String baseUnit;

    /**
     * 变体单位列表
     */
    private String variantUnitList;

    /**
     * 参考值
     */
    private String baseReference;

    /**
     * 参考值 - 展示，
     */
    private String baseReferenceShow;

    /**
     * 图标URL
     */
    private String imageUrl;

    /**
     * 人工审核
     */
    @TableField(value = "`check`")
    private Boolean check;

    /**
     * 报告名称
     */
    private String projectLabel;

    @TableField(exist = false)
    private String notIdList;

    @TableField(exist = false)
    private String idList;


    @TableField(exist = false)
    private String aliasNameList;

    @TableField(exist = false)
    private String projectAlias;

    /**
     * 是否重点指标
     */
    private String isImportance;
    /**
     * 分类：检查 or 检验
     */
    private String reportType;
    /**
     * 关联异常描述
     */
    private String exceptionDesc;
    /**
     * 偏离程度-偏高
     */
    private String highSide;
    /**
     * 偏离程度-偏低
     */
    private String lowSide;
    /**
     * 潜在健康风险（严重程度）
     */
    private String healthRisk;
    /**
     * 复查期限
     */
    private String reviewDate;
    /**
     * 关联系统名称
     */
    private String systemNames;
    @TableField(exist = false)
    List<String> systemNameList;
}
