package com.jeefast.cat.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.StrUtil;
import com.jeefast.common.utils.file.FileUploadUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.ExchangeGoodsBackstage;
import com.jeefast.cat.service.IExchangeGoodsBackstageService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 可兑换商品Controller
 * 
 * <AUTHOR>
 * @date 2020-08-09
 */
@Controller
@RequestMapping("/cat/exchangeGoodsBackstage")
public class ExchangeGoodsBackstageController extends BaseController {
    private String prefix = "cat/exchangeGoodsBackstage";

    @Autowired
    private IExchangeGoodsBackstageService exchangeGoodsBackstageService;

    @RequiresPermissions("cat:exchangeGoodsBackstage:view")
    @GetMapping()
    public String exchangeGoodsBackstage() {
        return prefix + "/exchangeGoodsBackstage";
    }

    /**
     * 查询可兑换商品列表
     */
    @RequiresPermissions("cat:exchangeGoodsBackstage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ExchangeGoodsBackstage exchangeGoodsBackstage) {
        QueryWrapper<ExchangeGoodsBackstage> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(exchangeGoodsBackstage.getGoodsName())) {
    		queryWrapper.like("goods_name", exchangeGoodsBackstage.getGoodsName());
    	} 
		// 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        return getDataTable(exchangeGoodsBackstageService.list(queryWrapper));
    }

    /**
     * 导出可兑换商品列表
     */
    @RequiresPermissions("cat:exchangeGoodsBackstage:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ExchangeGoodsBackstage exchangeGoodsBackstage) {
        List<ExchangeGoodsBackstage> list = exchangeGoodsBackstageService.list(new QueryWrapper<>());
        ExcelUtil<ExchangeGoodsBackstage> util = new ExcelUtil<ExchangeGoodsBackstage>(ExchangeGoodsBackstage.class);
        return util.exportExcel(list, "exchangeGoodsBackstage");
    }

    /**
     * 新增可兑换商品
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存可兑换商品
     */
    @RequiresPermissions("cat:exchangeGoodsBackstage:add")
    @Log(title = "可兑换商品", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ExchangeGoodsBackstage exchangeGoodsBackstage,@RequestParam("file") MultipartFile file) throws IOException {
        if(file!=null&& StrUtil.isNotEmpty(file.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(file);
            exchangeGoodsBackstage.setImage(cloudPath);
        }
        return toAjax(exchangeGoodsBackstageService.save(exchangeGoodsBackstage));
    }

    /**
     * 修改可兑换商品
     */
    @GetMapping("/edit/{exchangeGoodsId}")
    public String edit(@PathVariable("exchangeGoodsId") String exchangeGoodsId, ModelMap mmap) {
        ExchangeGoodsBackstage exchangeGoodsBackstage = exchangeGoodsBackstageService.getById(exchangeGoodsId);
        mmap.put("exchangeGoodsBackstage", exchangeGoodsBackstage);
        return prefix + "/edit";
    }

    /**
     * 修改保存可兑换商品
     */
    @RequiresPermissions("cat:exchangeGoodsBackstage:edit")
    @Log(title = "可兑换商品", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ExchangeGoodsBackstage exchangeGoodsBackstage,@RequestParam("file") MultipartFile file) throws IOException {
        if(file!=null&& StrUtil.isNotEmpty(file.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(file);
            exchangeGoodsBackstage.setImage(cloudPath);
        }
        return toAjax(exchangeGoodsBackstageService.updateById(exchangeGoodsBackstage));
    }

    /**
     * 删除可兑换商品
     */
    @RequiresPermissions("cat:exchangeGoodsBackstage:remove")
    @Log(title = "可兑换商品", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(exchangeGoodsBackstageService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
