package com.jeefast.cat.req;

import com.jeefast.cat.domain.DynamicInfoBackstage;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 详述: 批量修改动态分类
 * 开发人员：jingwei.huang
 * 创建时间：2021/2/25 下午8:14
 */
@Data
public class BatchEditDynamicCategoryReq {

    @NotEmpty(message = "动态ID不能为空")
    private List<String> dynamicIdList;
    @NotBlank(message = "分类ID不能为空")
    private String categoryId;

}
