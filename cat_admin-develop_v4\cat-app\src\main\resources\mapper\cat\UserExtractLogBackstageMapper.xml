<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeefast.cat.mapper.UserExtractLogBackstageMapper">
    <!--
    <resultMap type="UserExtractLogBackstage" id="UserExtractLogBackstageResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="title"    column="title"    />
        <result property="extractForm"    column="extract_form"    />
        <result property="cardNum"    column="card_num"    />
        <result property="cardName"    column="card_name"    />
        <result property="cardUserName"    column="card_user_name"    />
        <result property="money"    column="money"    />
        <result property="status"    column="status"    />
        <result property="createDate"    column="create_date"    />
        <result property="remark"    column="remark"    />
    </resultMap>
    -->


    <select id="infoList" resultType="cn.hutool.core.map.CamelCaseMap">
        select t1.*,t2.user_name
        from user_extract_log t1
        left join user_info t2 on t1.user_id=t2.user_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>

