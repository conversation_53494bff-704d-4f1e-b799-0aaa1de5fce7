package com.cat.modules.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.order.entity.OrderInfo;
import com.cat.modules.order.entity.OrderLog;
import com.cat.modules.order.mapper.OrderLogMapper;
import com.cat.modules.order.service.IOrderLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class OrderLogServiceImpl extends ServiceImpl<OrderLogMapper, OrderLog> implements IOrderLogService {

    @Override
    public void saveByOrder(String userId, OrderInfo order) {
        OrderLog orderLog = new OrderLog();
        orderLog.setOrderId(order.getId());
        orderLog.setUserId(userId);
        orderLog.setBusinessId(order.getBusinessId());
        orderLog.setBusinessType(order.getBusinessType());
        orderLog.setStatus(order.getStatus());
        this.save(orderLog);
    }
}
