package com.jeefast.cat.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cat.modules.dynamic.entity.DynamicMedia;
import com.cat.modules.order.entity.OrderInfo;
import com.cat.modules.order.entity.OrderItem;
import com.cat.modules.order.entity.OrderRefund;
import com.cat.modules.order.entity.Shopping;
import com.cat.modules.order.service.IOrderInfoService;
import com.cat.modules.order.service.IOrderItemService;
import com.cat.modules.order.service.IOrderRefundService;
import com.cat.modules.order.service.IShoppingService;
import com.jeefast.common.exception.BusinessException;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 订单退款Controller
 * 
 * <AUTHOR>
 * @date 2021-05-26
 */
@Controller
@RequestMapping("/cat/OrderRefund")
public class OrderRefundController extends BaseController {
    private String prefix = "cat/OrderRefund";

    @Autowired
    private IOrderRefundService orderRefundService;

    @Autowired
    private IOrderInfoService orderInfoService;
    @Autowired
    private IShoppingService shoppingService;
    @Autowired
    private IOrderItemService orderItemService;

    @RequiresPermissions("cat:OrderRefund:view")
    @GetMapping()
    public String OrderRefund() {
        return prefix + "/OrderRefund";
    }

    /**
     * 查询订单退款列表
     */
    @RequiresPermissions("cat:OrderRefund:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(OrderRefund req) {
        QueryWrapper<OrderRefund> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("1","1");
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getOrderId())) {
    		queryWrapper.like("orderId", req.getOrderId());
    	} 
    	if(StringUtils.isNotEmpty(req.getOrderNo())) {
    		queryWrapper.like("order_no", req.getOrderNo());
    	}
        if(StringUtils.isNotEmpty(req.getRefundNo())) {
            queryWrapper.like("refund_no", req.getRefundNo());
        }
        if(StringUtils.isNotEmpty(req.getOutRefundNo())) {
            queryWrapper.like("out_refund_no", req.getOutRefundNo());
        }
        if(StringUtils.isNotEmpty(req.getUserId())) {
            queryWrapper.like("user_id", req.getUserId());
        }
        if(StringUtils.isNotEmpty(req.getLogisticsNum())) {
            queryWrapper.like("logistics_num", req.getLogisticsNum());
        }
        if(ObjectUtil.isNotNull(req.getRefundSts())) {
            queryWrapper.like("refund_sts", req.getRefundSts());
        }
		// 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginRefundDate")), "refund_date", params.get("beginRefundDate"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endRefundDate")), "refund_date", params.get("endRefundDate"));

            queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
            queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endCreateDate")), "create_date", params.get("endCreateDate"));
		}
        startPage();
		queryWrapper.orderByDesc("create_date");
        return getDataTable(orderRefundService.list(queryWrapper));
    }

    /**
     * 导出订单退款列表
     */
    @RequiresPermissions("cat:OrderRefund:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(OrderRefund orderRefund) {
        List<OrderRefund> list = orderRefundService.list(new QueryWrapper<>());
        ExcelUtil<OrderRefund> util = new ExcelUtil<OrderRefund>(OrderRefund.class);
        return util.exportExcel(list, "OrderRefund");
    }

    /**
     * 新增订单退款
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存订单退款
     */
    @RequiresPermissions("cat:OrderRefund:add")
    @Log(title = "订单退款", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(OrderRefund orderRefund) {
        return toAjax(orderRefundService.save(orderRefund));
    }

    /**
     * 修改订单退款
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        OrderRefund orderRefund = orderRefundService.getById(id);
        JSONArray jsonFileList =  JSONArray.parseArray(orderRefund.getPhotoFiles());
        List<String> mediaStrList = new ArrayList<>();
        for (int i = 0; i < jsonFileList.size(); i++) {
            mediaStrList.add(jsonFileList.getJSONObject(i).getString("mediaUrl"));
        }
        mmap.put("orderRefund", orderRefund);
        mmap.put("mediaStrList", JSONObject.toJSONString(mediaStrList));
        return prefix + "/edit";
    }

    /**
     * 修改保存订单退款
     */
    @RequiresPermissions("cat:OrderRefund:edit")
    @Log(title = "订单退款", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(OrderRefund saveOrderRefund) {
        String id = saveOrderRefund.getId();
        OrderRefund orderRefund = orderRefundService.getById(id);
        if(orderRefund==null){
            throw new BusinessException("退款单id不存在！");
        }
        String orderItemId = orderRefund.getOrderItemId();
        boolean flag = orderRefundService.updateById(saveOrderRefund);
        //修改子订单审核状态
        orderItemService.update(new UpdateWrapper<OrderItem>()
                .set("refund_sts",saveOrderRefund.getRefundSts())
                .set("seller_msg",saveOrderRefund.getSellerMsg())
                .eq("id",orderItemId)
        );
        return toAjax(flag);
    }

    /**
     * 删除订单退款
     */
    @RequiresPermissions("cat:OrderRefund:remove")
    @Log(title = "订单退款", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(orderRefundService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }


    /**
     * 退款页面
     */
    @RequiresPermissions("cat:OrderRefund:view")
    @GetMapping("/returnMoneyViwe")
    public String returnMoneyViwe(OrderRefund req, ModelMap mmap) {
        OrderRefund orderRefund = orderRefundService.getById(req.getId());
        String orderId = orderRefund.getOrderId();
        OrderInfo orderInfo = orderInfoService.getById(orderId);
        Shopping shopping = shoppingService.getOne(new QueryWrapper<Shopping>().eq("order_id",orderId));
        mmap.put("orderId",orderId);
        mmap.put("orderInfo",orderInfo);
        mmap.put("shopping",shopping);
        mmap.put("orderRefund",orderRefund);
        return prefix + "/returnMoney";
    }


    /**
     * 退款
     */
    @RequiresPermissions("cat:OrderRefund:returnMoney")
    @Log(title = "订单退款/退货", businessType = BusinessType.UPDATE)
    @PostMapping("/returnMoney")
    @ResponseBody
    public AjaxResult returnMoney(OrderRefund req, ModelMap mmap) {
        boolean flag = orderRefundService.returnMoney(req);
        return toAjax(flag);
    }
}
