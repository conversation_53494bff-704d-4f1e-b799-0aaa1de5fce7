package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                            import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 用户标签配置表 user_tag_config
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_tag_config")
public class UserTagConfigBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /** 标签名 */
    @Excel(name = "标签名")
    private String name;
    /** 排序值 */
    @Excel(name = "排序值")
    private Integer sortOn;
    /** 描述 */
    @Excel(name = "描述")
    private String depict;
    /** 创建时间 */
    /*@Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;*/
    /** 更新时间 */
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;
    /** 备注 */
    @Excel(name = "备注")
    private String remark;
}
