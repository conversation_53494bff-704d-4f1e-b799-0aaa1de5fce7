package com.cat.modules.product.service;

import cn.hutool.core.map.CamelCaseLinkedMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cat.modules.product.entity.ProductGroupVal;

import java.util.List;


public interface IProductGroupValService extends IService<ProductGroupVal> {

    List<CamelCaseLinkedMap> listExt(QueryWrapper<ProductGroupVal> qw);
}