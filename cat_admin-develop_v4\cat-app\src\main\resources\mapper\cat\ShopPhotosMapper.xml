<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.shop.mapper.ShopPhotosMapper">

    <!-- 通用查询映射结果  配置了autoMapping=true,其他的无需配置，将会自动映射-->
    <resultMap id="BaseResultMap" type="com.cat.modules.shop.entity.ShopPhotos" autoMapping="true" />

    <select id="listAndProductRec" resultMap="BaseResultMap">
        select *
        from (
            <!-- 只查相册 -->
            <if test="req.type!=null and req.type!=9 and req.type != -1">
                select  id,media_url,type,create_date
                from shop_photos
                where is_check='1' and shop_id=#{req.shopId}
                and type=#{req.type}
            </if>
            <!-- 只查推荐商品图 -->
            <if test="req.type!=null and req.type==9">
                select id,img_url as 'media_url','9' as 'type',create_date
                from shop_product_rec
                where is_check='1' and shop_id=#{req.shopId}
            </if>
            <!-- 全部查（相册、推荐商品图） -->
            <if test="req.type == null or req.type == -1">
                select  id,media_url,type,create_date
                from shop_photos
                where is_check='1' and shop_id=#{req.shopId}
                UNION ALL
                select id,img_url as 'media_url','9' as 'type',create_date
                from shop_product_rec
                where is_check='1' and shop_id=#{req.shopId}
            </if>
        ) t
        order by create_date desc
        ${req.limitSql}
    </select>

    <select id="listAndProductRecCount" resultType="int">
        select count(1)
        from (
            select  id,media_url,type,create_date
            from shop_photos
            where is_check='1' and shop_id=#{req.shopId}
            UNION ALL
            select id,img_url as 'media_url','9' as 'type',create_date
            from shop_product_rec
            where is_check='1' and shop_id=#{req.shopId}
        ) t
    </select>
</mapper>
