<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.user.mapper.UserMapper">


    <select id="getUserId" resultType="java.lang.String">
        SELECT user_id FROM user_info ORDER BY RAND() LIMIT 1;
    </select>

    <select id="randUserAvatar" resultType="java.lang.String">
        select user_id from user_info where avatar_url is not null AND ( mobile IS NULL AND open_id IS NULL AND union_id is null ) and dynamic_count>=1  order by rand() limit #{count}
    </select>

    <select id="getRandPetUserId" resultType="java.lang.String">
        SELECT user_id
        FROM user_info t1
        where 2>(select count(1) from pet_info t2 where t2.user_id=t1.user_id)
        and t1.open_id is null and t1.union_id is null and avatar_url is not null and user_id!='000000000'
        and t1.dynamic_count>=1
        ORDER BY RAND() LIMIT 1;
    </select>

    <select id="getByOpenId" resultType="java.lang.String">
        select open_id from user_info where user_id=#{toUserId}
    </select>

    <select id="getByIdPushInfo" resultType="com.cat.util.DataRow">
        select platform,push_id,open_id from user_info where user_id=#{userId}
    </select>

    <update id="updateFamilyUserNewDynamicNum">
        update family set family_user_new_dynamic_num=(family_user_new_dynamic_num + #{diffNum}),family_user_last_dynamic_date = now()
        where family_user_id=#{familyUserId}
        <if test="dynamicDate != null">
            and user_last_view_date &lt; #{dynamicDate}
        </if>
    </update>
</mapper>
