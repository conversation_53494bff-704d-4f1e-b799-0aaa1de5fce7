package com.jeefast.cat.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * 动态内容表 dynamic_info
 *
 * <AUTHOR>
 * @date 2020-08-08
 */


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("system_of_systems_nurse_bill")
public class SystemOfSystemsNurseBill extends CatBaseEntity implements Serializable {

    /** id */
    @TableId(value = "id",type = IdType.AUTO)
    private BigInteger id;

    /**
     * 系统名称
     */
    @TableField("system_name")
    private String systemName;

    /**
     * 元素类型
     */
    @TableField("ele_name")
    private String eleName;

    /**
     * 元素名称
     */
    @TableField("is_delete")
    private String isDelete;

    /**
     * 其他
     */
    private String other;


    /**
     * 创建人
     */
    @TableField("create_uid")
    private String createUid;


    @TableField("write_uid")
    private String writeUid;

    @TableField("write_date")
    private LocalDateTime writeDate;

}