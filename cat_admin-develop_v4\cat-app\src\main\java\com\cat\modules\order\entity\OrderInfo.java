package com.cat.modules.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("order_info")
public class OrderInfo extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id",type = IdType.UUID)
    @TableField("id")
    private String id;

    

    @TableField("user_id")
    private String userId;

    
    @TableField("order_subject")
    private String orderSubject;

    
    @TableField("order_no")
    private String orderNo;

    
    @TableField("out_trade_no")
    private String outTradeNo;


    

    @TableField("business_id")
    private String businessId;

    

    @TableField("business_type")
    private String businessType;

    

    @TableField("shopping_id")
    private String shoppingId;

    

    @TableField("payment")
    private BigDecimal payment;

    

    @TableField("payment_type")
    private Integer paymentType;

    
    @TableField("postage")
    private Integer postage;

    
    @TableField("tracking_no")
    private String trackingNo;

    
    @TableField("status")
    private Integer status;

    
    @TableField("total_quantity")
    Integer totalQuantity;;


    

    @TableField("payment_date")
    private Date paymentDate;

    

    @TableField("send_date")
    private Date sendDate;

    
    @TableField("end_date")
    private Date endDate;

    
    @TableField("close_date")
    private Date closeDate;

    
    @TableField("update_date")
    private Date updateDate;

    
    @TableField("logistics")
    private String logistics;
    
    @TableField("logistics_num")
    private String logisticsNum;

    
    @TableField("shop_id")
    private String shopId;



}
