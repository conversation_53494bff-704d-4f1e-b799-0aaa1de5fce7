package com.cat.modules.dynamic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DynamicInfo extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "dynamic_id",type = IdType.UUID)
    @TableField("dynamic_id")
    private String dynamicId;

    
    @TableField("pet_id")
    private String petId;

    
    @TableField("user_id")
    private String userId;

    @TableField("title")
    private String title;
    
    @TableField("content")
    private String content;

    
    @TableField("addr")
    private String addr;

    
    @TableField("longitude")
    private double longitude;

    
    @TableField("latitude")
    private double latitude;



    
    @TableField("praise_count")
    private Integer praiseCount;

    
    @TableField("comment_count")
    private Integer commentCount;

    
    @TableField("type")
    private String type="0";

    
    @TableField("source")
    private String source="0";

    
    @TableField("cover_image")
    private String coverImage;

    
    @TableField("media_count")
    private int mediaCount;

    
    @TableField("width")
    private int width;

    
    @TableField("height")
    private int height;

    @TableField("is_delete")
    private Boolean isDelete;

    @TableField("article_type")
    private String articleType;

    /** 浏览量 */
    private Integer viewCount;

    /** 虚拟浏览量 */
    private Integer visualViewCount;

}
