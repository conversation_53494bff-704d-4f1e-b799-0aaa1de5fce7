package com.jeefast.cat.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.jeefast.common.core.domain.Ztree;
import com.jeefast.common.core.domain.ZtreeExt;
import com.jeefast.common.utils.file.FileUploadUtils;
import com.jeefast.system.domain.SysDept;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.CategoryBackstage;
import com.jeefast.cat.service.ICategoryBackstageService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 商品类别Controller
 * 
 * <AUTHOR>
 * @date 2020-11-09
 */
@Controller
@RequestMapping("/cat/categoryBackstage")
public class CategoryBackstageController extends BaseController {
    private String prefix = "cat/categoryBackstage";

    @Autowired
    private ICategoryBackstageService categoryBackstageService;

    @RequiresPermissions("cat:categoryBackstage:view")
    @GetMapping()
    public String categoryBackstage() {
        return prefix + "/categoryBackstage";
    }

    /**
     * 查询商品类别列表
     */
    @RequiresPermissions("cat:categoryBackstage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(CategoryBackstage req) {
        QueryWrapper<CategoryBackstage> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getName())) {
    		queryWrapper.like("name", req.getName());
    	} 
    	if(ObjectUtil.isNotNull(req.getStatus())) {
    		queryWrapper.eq("status", req.getStatus());
    	}
        if(StringUtils.isNotEmpty(req.getParentId())) {
            queryWrapper.eq("parent_id", req.getParentId());
        }
		// 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("startTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}
		queryWrapper.orderByAsc("sort_on");
        startPage();
        return getDataTable(categoryBackstageService.list(queryWrapper));
    }

    /**
     * 导出商品类别列表
     */
    @RequiresPermissions("cat:categoryBackstage:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(CategoryBackstage categoryBackstage) {
        List<CategoryBackstage> list = categoryBackstageService.list(new QueryWrapper<>());
        ExcelUtil<CategoryBackstage> util = new ExcelUtil<CategoryBackstage>(CategoryBackstage.class);
        return util.exportExcel(list, "categoryBackstage");
    }

    /**
     * 新增商品类别
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存商品类别
     */
    @RequiresPermissions("cat:categoryBackstage:add")
    @Log(title = "商品类别", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestParam("file") MultipartFile multipartFile,CategoryBackstage categoryBackstage) throws IOException {
        if(multipartFile!=null&& StrUtil.isNotEmpty(multipartFile.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            categoryBackstage.setImage(cloudPath);
        }
        return toAjax(categoryBackstageService.save(categoryBackstage));
    }

    /**
     * 修改商品类别
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        CategoryBackstage categoryBackstage = categoryBackstageService.getById(id);
        mmap.put("categoryBackstage", categoryBackstage);
        return prefix + "/edit";
    }

    /**
     * 修改保存商品类别
     */
    @RequiresPermissions("cat:categoryBackstage:edit")
    @Log(title = "商品类别", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestParam("file") MultipartFile multipartFile, CategoryBackstage categoryBackstage) throws IOException {
        if(multipartFile!=null&& StrUtil.isNotEmpty(multipartFile.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            categoryBackstage.setImage(cloudPath);
        }
        return toAjax(categoryBackstageService.updateById(categoryBackstage));
    }

    /**
     * 删除商品类别
     */
    @RequiresPermissions("cat:categoryBackstage:remove")
    @Log(title = "商品类别", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(categoryBackstageService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }


    /**
     * 选择分类树
     */
    @GetMapping("/selectCategoryTree/{categoryId}")
    public String selectCategoryTree(@PathVariable("categoryId") String categoryId, ModelMap mmap)
    {
        //mmap.put("category", categoryBackstageService.selectByCategoryId(categoryId));
        return prefix + "/tree";
    }

    /**
     * 加载分类树
     */
    @GetMapping("/treeData")
    @ResponseBody
    public List<ZtreeExt> treeData()
    {
        List<ZtreeExt> ztrees = categoryBackstageService.selectDeptTree(new CategoryBackstage());
        return ztrees;
    }
}
