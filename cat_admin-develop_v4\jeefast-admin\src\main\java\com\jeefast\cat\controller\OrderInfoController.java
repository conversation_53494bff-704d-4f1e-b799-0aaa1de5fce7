package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;
import com.cat.modules.order.entity.OrderInfo;
import com.cat.modules.order.entity.Shopping;
import com.cat.modules.order.service.IOrderInfoService;
import com.cat.modules.order.service.IShoppingService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 订单Controller
 * 
 * <AUTHOR>
 * @date 2021-04-26
 */
@Controller
@RequestMapping("/cat/orderInfo")
public class OrderInfoController extends BaseController {
    private String prefix = "cat/orderInfo";

    @Autowired
    private IOrderInfoService orderInfoService;
    @Autowired
    private IShoppingService shoppingService;

    @RequiresPermissions("cat:orderInfo:view")
    @GetMapping()
    public String orderInfo() {
        return prefix + "/orderInfo";
    }

    /**
     * 查询订单列表
     */
    @RequiresPermissions("cat:orderInfo:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(OrderInfo req) {
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<OrderInfo>();
        queryWrapper.eq("1","1");
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getUserId())) {
    		queryWrapper.eq("t1.user_id", req.getUserId());
    	}
    	if(StringUtils.isNotEmpty(req.getOrderNo())) {
    		queryWrapper.eq("t1.order_no", req.getOrderNo());
    	}
        if(StringUtils.isNotEmpty(req.getOutTradeNo())) {
            queryWrapper.eq("t1.out_trade_no", req.getOutTradeNo());
        }
        if(StringUtils.isNotEmpty(req.getTrackingNo())) {
            queryWrapper.eq("t1.tracking_no", req.getOutTradeNo());
        }
        if(ObjectUtil.isNotNull(req.getStatus())) {
            queryWrapper.eq("t1.status", req.getStatus());
        }
		// 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginPaymentDate")), "t1.payment_date", params.get("beginPaymentDate"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endPaymentDate")), "t1.payment_date", params.get("endPaymentDate"));

            queryWrapper.le(StringUtils.isNotEmpty((String)params.get("beginDate")), "t1.create_date", params.get("beginDate"));
            queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endDate")), "t1.create_date", params.get("endDate"));
		}
        startPage();
		queryWrapper.orderByDesc("create_date");
        return getDataTable(orderInfoService.listExt(queryWrapper));
    }

    /**
     * 导出订单列表
     */
    @RequiresPermissions("cat:orderInfo:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(OrderInfo orderInfo) {
        List<OrderInfo> list = orderInfoService.list(new QueryWrapper<>());
        ExcelUtil<OrderInfo> util = new ExcelUtil<OrderInfo>(OrderInfo.class);
        return util.exportExcel(list, "orderInfo");
    }

    /**
     * 新增订单
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存订单
     */
    @RequiresPermissions("cat:orderInfo:add")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(OrderInfo orderInfo) {
        return toAjax(orderInfoService.save(orderInfo));
    }

    /**
     * 修改订单
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        OrderInfo orderInfo = orderInfoService.getById(id);
        mmap.put("orderInfo", orderInfo);
        return prefix + "/edit";
    }

    /**
     * 修改保存订单
     */
    @RequiresPermissions("cat:orderInfo:edit")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(OrderInfo orderInfo) {
        return toAjax(orderInfoService.updateById(orderInfo));
    }

    /**
     * 删除订单
     */
    @RequiresPermissions("cat:orderInfo:remove")
    @Log(title = "订单", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(orderInfoService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }


    /**
     * 修改保存订单
     */
    @RequiresPermissions("cat:orderInfo:ship")
    @Log(title = "订单发货", businessType = BusinessType.UPDATE)
    @PostMapping("/ship")
    @ResponseBody
    public AjaxResult ship(OrderInfo orderInfo){
        boolean flag = orderInfoService.ship(orderInfo);
        return toAjax(flag);
    }

    /**
     * 订单发货清单
     */
    @RequiresPermissions("cat:orderInfo:view")
    @GetMapping("/shipViwe")
    public String shipViwe(OrderInfo req,ModelMap mmap) {
        OrderInfo orderInfo = orderInfoService.getById(req.getId());
        Shopping shopping = shoppingService.getOne(new QueryWrapper<Shopping>().eq("order_id",req.getId()));
        mmap.put("orderId",req.getId());
        mmap.put("orderInfo",orderInfo);
        mmap.put("shopping",shopping);
        return prefix + "/orderShip";
    }
}
