package com.cat.job;

import com.cat.modules.order.service.IOrderInfoService;
import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

@Slf4j
@Component("orderStatusJob")
public class OrderStatusJob {
    @Autowired
    private IOrderInfoService orderInfoService;
    @Autowired
    private RedisUtil redisUtil;

    
    public void main(){
        orderInfoService.singTimeOut();
        orderInfoService.cancelTimeOut();
    }

    
    public static Date getDateByDay(int num) {
        Calendar cal = Calendar.getInstance();
        
        cal.setTime(new Date());
        
        cal.add(Calendar.DATE, num);
        return cal.getTime();
    }


}
