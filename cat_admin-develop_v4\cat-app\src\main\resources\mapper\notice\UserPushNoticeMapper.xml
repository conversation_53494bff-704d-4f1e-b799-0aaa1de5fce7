<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.notice.mapper.UserPushNoticeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cat.modules.notice.entity.UserPushNotice">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="to_user_id" property="toUserId"/>
        <result column="type" property="type"/>
        <result column="object_id" property="objectId"/>
        <result column="push_title" property="pushTitle"/>
        <result column="push_desc" property="pushDesc"/>
        <result column="extra_str" property="extraStr"/>
        <result column="res_json" property="resJson"/>
        <result column="is_send" property="isSend"/>
        <result column="send_time" property="sendTime"/>
        <result column="is_read" property="isRead"/>
        <result column="read_time" property="readTime"/>
        <result column="is_delete" property="isDelete"/>
        <result column="send_count" property="sendCount"/>
        <result column="create_date" property="createDate"/>
        <result column="write_date" property="writeDate"/>
        <result column="create_uid" property="createUid"/>
        <result column="write_uid" property="writeUid"/>
        <result column="other" property="other"/>
    </resultMap>


</mapper> 