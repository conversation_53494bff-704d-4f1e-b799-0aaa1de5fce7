package com.cat.modules.auth.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.auth.entity.AuthInfo;
import com.cat.modules.auth.mapper.AuthInfoMapper;
import com.cat.modules.auth.service.IAuthInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service

public class AuthInfoServiceImpl extends ServiceImpl<AuthInfoMapper, AuthInfo> implements IAuthInfoService {
	@Autowired
	private AuthInfoMapper mapper;
	@Override
	public List<AuthInfo> getList(Map<String, Object> condi) {
		return mapper.getList(condi);
	}
}