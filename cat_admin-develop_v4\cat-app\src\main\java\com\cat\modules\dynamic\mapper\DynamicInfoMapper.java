package com.cat.modules.dynamic.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cat.modules.dynamic.entity.DynamicInfo;
import com.cat.modules.dynamic.resp.DynamicViewRespVo;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;


public interface DynamicInfoMapper extends BaseMapper<DynamicInfo> {

    @Update("update dynamic_info t1  set t1.weight =  (2880 -TIMESTAMPDIFF(MINUTE, t1.create_date, now())+ t1.praise_count*10 + t1.comment_count*16),t1.weight=t1.weight*t1.scale where is_delete='0' and is_check='1' ")
    int calculateWeight();

    List<CamelCaseMap> recommendList();

    List<DynamicInfo> getTopTen();

    List<DynamicViewRespVo> viewCount(@Param("dynamicIds") List<String> dynamicIds, @Param("startTime") String startTime, @Param("endTime") String endTime);
}
