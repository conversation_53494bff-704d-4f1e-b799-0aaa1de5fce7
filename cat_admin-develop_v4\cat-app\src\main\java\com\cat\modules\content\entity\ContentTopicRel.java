package com.cat.modules.content.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.domain.BaseEntity;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ContentTopicRel extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "content_topic_id",type = IdType.UUID)
    @TableField("content_topic_id")
    private String contentTopicId;

    
    @TableField("topic_id")
    private String topicId;

    
    @TableField("topic_name")
    private String topicName;

    
    @TableField("business_id")
    private String businessId;

    
    @TableField("business_type")
    private String businessType;



}
