package com.cat.modules.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class UserAvatarPendent extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id",type = IdType.UUID)
    @TableField("id")
    private String id;

    
    @TableField("name")
    private String name;

    
    @TableField("icon_url")
    private String iconUrl;

    
    @TableField("thumnail")
    private String thumnail;

    
    @TableField("group_code")
    private String groupCode;

    
    @TableField("`describe`")
    private String describe;

    
    @TableField("status")
    private String status;

    
    @TableField("sort_on")
    private Integer sortOn;



}
