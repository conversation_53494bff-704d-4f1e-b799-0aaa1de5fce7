package com.cat.modules.pet.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PetRecord extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id",type = IdType.UUID)
    @TableField("id")
    private String id;

    
    @TableField("pet_id")
    private String petId;

    
    @TableField("user_id")
    private String userId;

    
    @TableField("content")
    private String content;

    
    @TableField("type")
    private String type;

    
    @TableField("tag")
    private String tag;

    
    @TableField("record_date")
    private LocalDateTime recordDate;

    
    @TableField("praise_count")
    private Integer praiseCount;

    
    @TableField("comment_count")
    private Integer commentCount;

    
    @TableField("media_type")
    private String mediaType;

    
    @TableField("cover_image")
    private String coverImage;

    
    @TableField("height")
    private Integer height;

    
    @TableField("width")
    private Integer width;

    
    @TableField("media_count")
    private Integer mediaCount;

    
    @TableField("data_ext")
    private String dataExt;

    
    @TableField("scope")
    private String scope;

    
    @TableField("is_delete")
    private String isDelete;



}
