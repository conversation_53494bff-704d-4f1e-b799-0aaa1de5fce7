package com.cat.modules.order.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cat.modules.order.entity.OrderInfo;
import com.cat.modules.order.thread.entity.OrderPrimary;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface OrderMapper extends BaseMapper<OrderInfo> {

    List<CamelCaseMap> listExt(@Param("ew") QueryWrapper<OrderInfo> ew);

    OrderPrimary selectByPrimaryKey(String orderId);
}
