package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.UserTagConfigBackstage;
import com.jeefast.cat.service.IUserTagConfigBackstageService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 用户标签配置Controller
 * 
 * <AUTHOR>
 * @date 2020-11-08
 */
@Controller
@RequestMapping("/cat/UserTagConfigBackstage")
public class UserTagConfigBackstageController extends BaseController {
    private String prefix = "cat/UserTagConfigBackstage";

    @Autowired
    private IUserTagConfigBackstageService userTagConfigBackstageService;

    @RequiresPermissions("cat:UserTagConfigBackstage:view")
    @GetMapping()
    public String UserTagConfigBackstage() {
        return prefix + "/UserTagConfigBackstage";
    }

    /**
     * 查询用户标签配置列表
     */
    @RequiresPermissions("cat:UserTagConfigBackstage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(UserTagConfigBackstage req) {
        QueryWrapper<UserTagConfigBackstage> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getName())) {
    		queryWrapper.like("name", req.getName());
    	} 
    	/*if(StringUtils.isNotEmpty(sysDemo.getUserName())) {
    		queryWrapper.like("user_name", sysDemo.getUserName());
    	}*/
		// 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("startTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}
        queryWrapper.orderByAsc("sort_on");
        startPage();
        return getDataTable(userTagConfigBackstageService.list(queryWrapper));
    }

    /**
     * 导出用户标签配置列表
     */
    @RequiresPermissions("cat:UserTagConfigBackstage:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(UserTagConfigBackstage userTagConfigBackstage) {
        List<UserTagConfigBackstage> list = userTagConfigBackstageService.list(new QueryWrapper<>());
        ExcelUtil<UserTagConfigBackstage> util = new ExcelUtil<UserTagConfigBackstage>(UserTagConfigBackstage.class);
        return util.exportExcel(list, "UserTagConfigBackstage");
    }

    /**
     * 新增用户标签配置
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存用户标签配置
     */
    @RequiresPermissions("cat:UserTagConfigBackstage:add")
    @Log(title = "用户标签配置", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(UserTagConfigBackstage userTagConfigBackstage) {
        userTagConfigBackstage.setUpdateDate(new Date());
        userTagConfigBackstage.setCreateDate(new Date());
        return toAjax(userTagConfigBackstageService.save(userTagConfigBackstage));
    }

    /**
     * 修改用户标签配置
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        UserTagConfigBackstage userTagConfigBackstage = userTagConfigBackstageService.getById(id);
        mmap.put("userTagConfigBackstage", userTagConfigBackstage);
        return prefix + "/edit";
    }

    /**
     * 修改保存用户标签配置
     */
    @RequiresPermissions("cat:UserTagConfigBackstage:edit")
    @Log(title = "用户标签配置", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(UserTagConfigBackstage userTagConfigBackstage) {
        return toAjax(userTagConfigBackstageService.updateById(userTagConfigBackstage));
    }

    /**
     * 删除用户标签配置
     */
    @RequiresPermissions("cat:UserTagConfigBackstage:remove")
    @Log(title = "用户标签配置", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(userTagConfigBackstageService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
