package com.jeefast.cat.job;

import com.jeefast.cat.service.IDynamicInfoBackstageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 动态虚拟浏览量自动增加任务
 */
@Slf4j
@Component
public class DynamicInfoVisualViewAutoAddJob {

    @Resource
    IDynamicInfoBackstageService dynamicInfoBackstageService;

    /**
     * 动态增加虚拟浏览量
     *
     * @param regionStart 随机数起始值
     * @param regionEnd   随机数最大值
     * @param recentDays   最近几天
     *
     */
    public void autoAdd(Integer regionStart, Integer regionEnd,Integer recentDays) {
        dynamicInfoBackstageService.autoAddVisualView(regionStart, regionEnd,recentDays);
    }
}
