package com.cat.job;

import cn.hutool.core.map.CamelCaseMap;
import com.alibaba.fastjson.JSONObject;
import com.cat.modules.article.service.IArticleInfoService;
import com.cat.modules.dynamic.service.IDynamicInfoService;
import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;


@Slf4j
@Component("dynamicWeightJob")
public class DynamicWeightJob {


    @Autowired
    private RedisUtil redisUtil;

    private String recommendKey = "we_chat:recommendList";

    @Autowired
    private IDynamicInfoService dynamicInfoService;
    @Autowired
    private IArticleInfoService articleInfoService;


    
    
    public void calculateWeight(){
        int i = dynamicInfoService.calculateWeight();
        int j = articleInfoService.calculateWeight();
        log.info("=======内容权重计算完成=======");
        writeRedis();
        log.info("=======推荐内容写入redis完成=======");
    }


    
    public boolean writeRedis(){
        List<CamelCaseMap> result = new ArrayList<>();
        List<CamelCaseMap> dyList = dynamicInfoService.recommendList();
        
        result.addAll(dyList);
        
        
        Collections.sort(result, new Comparator<CamelCaseMap>(){
            public int compare(CamelCaseMap o1, CamelCaseMap o2) {
                
                if((Integer) o1.get("weight") < (Integer)o2.get("weight")){
                    return 1;
                }
                if((Integer) o1.get("weight") == (Integer)o2.get("weight")){
                    return 0;
                }
                return -1;
            }
        });
        redisUtil.del(recommendKey);
        for (int i = 0; i < result.size(); i++) {
            redisUtil.rpush(recommendKey, JSONObject.toJSONString(result.get(i)));
        }
        return false;
    }





}
