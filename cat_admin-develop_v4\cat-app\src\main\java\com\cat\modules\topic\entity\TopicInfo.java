package com.cat.modules.topic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class TopicInfo extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "topic_id",type = IdType.UUID)
    @TableField("topic_id")
    private String topicId;

    
    @TableField("topic_name")
    private String topicName;

    
    @TableField("cite_count")
    private Integer citeCount;

    
    @TableField("join_count")
    private Integer joinCount;

    
    @TableField("user_id")
    private String userId;


    @TableField("group_id")
    private String groupId;
    
    @TableField(exist = false)
    private boolean flag = false;



}
