package com.cat.modules.product.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                                    import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;


 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_sku")
public class ProductSku extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    
    @Excel(name = "商品id")
    private String productId;
    
    @Excel(name = "skuKey组合_分割")
    private String groupKeyId;
    
    @Excel(name = "skuVal组合_分割")
    private String groupValId;
    
    @Excel(name = "属性搭配方式", readConverterExp = "k=ey,val")
    private String groupSpace;
    
    @Excel(name = "价格")
    private Double price;
    
    @Excel(name = "会员价")
    private BigDecimal vipPrice;
    
    @Excel(name = "原价")
    private BigDecimal originalPrice;
    
    @Excel(name = "库存数量")
    private Integer stock;
    
    @Excel(name = "总销量")
    private Integer totalSales;
    
    @Excel(name = "产品组合图")
    private String mainimage;
    
    @Excel(name = "sku状态")
    private Integer status;
    
    
    
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;
    
    @Excel(name = "店铺id")
    private String shopId;
    
    private String isDelete;
}
