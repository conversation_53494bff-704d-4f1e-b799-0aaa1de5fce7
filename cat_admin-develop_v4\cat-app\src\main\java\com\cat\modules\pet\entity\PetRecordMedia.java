package com.cat.modules.pet.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ToString
public class PetRecordMedia extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id",type = IdType.UUID)
    @TableField("id")
    private String id;

    
    @TableField("record_id")
    private String recordId;

    
    @TableField("pet_id")
    private String petId;

    
    @TableField("media_url")
    private String mediaUrl;

    
    @TableField("small_url")
    private String smallUrl;

    
    @TableField("type")
    private String type;

    
    @TableField("sort_no")
    private Integer sortNo;

    
    @TableField("width")
    private Integer width;

    
    @TableField("height")
    private Integer height;



}
