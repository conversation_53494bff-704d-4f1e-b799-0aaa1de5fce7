package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                            import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * vip开通配置表 vip_open_config
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("vip_open_config")
public class VipOpenConfigBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /** 名称 */
    @Excel(name = "名称")
    private String name;
    /** 天数 */
    @Excel(name = "天数")
    private Integer day;
    /** 现价格 */
    @Excel(name = "现价格")
    private Double price;
    /** 原价格 */
    @Excel(name = "原价格")
    private Double originalPrice;
    /** vip等级code */
    @Excel(name = "等级code对应字典（钻石、黄金)")
    private String levelCode;
    /** 描述 */
    @Excel(name = "描述")
    private String depict;
    /** 排序值 */
    @Excel(name = "排序值")
    private Integer sortOn;
    /** 是否显示 */
    @Excel(name = "是否显示")
    private Integer isShow;
    /** 创建时间 *//*
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;*/
    /** 更新时间 */
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;
    /** 备注 */
    @Excel(name = "备注")
    private String remark;
}
