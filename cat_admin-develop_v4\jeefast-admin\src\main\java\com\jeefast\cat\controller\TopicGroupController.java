package com.jeefast.cat.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.StrUtil;
import com.cat.modules.topic.entity.TopicGroup;
import com.cat.modules.topic.service.ITopicGroupService;
import com.jeefast.common.utils.file.FileUploadUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 话题分组Controller
 * 
 * <AUTHOR>
 * @date 2021-04-01
 */
@Controller
@RequestMapping("/cat/TopicGroup")
public class TopicGroupController extends BaseController {
    private String prefix = "cat/TopicGroup";

    @Autowired
    private ITopicGroupService topicGroupService;

    @RequiresPermissions("cat:TopicGroup:view")
    @GetMapping()
    public String TopicGroup() {
        return prefix + "/TopicGroup";
    }

    /**
     * 查询话题分组列表
     */
    @RequiresPermissions(value = {"cat:TopicGroup:list","cat:dynamicBackstage:add:page"},logical = Logical.OR)
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(TopicGroup req) {
        QueryWrapper<TopicGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("1","1");
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getName())) {
    		queryWrapper.like("name", req.getName());
    	}
        if(StringUtils.isNotEmpty(req.getIsShow())) {
            queryWrapper.eq("is_show", req.getIsShow());
        }
        if(StringUtils.isNotEmpty(req.getIsRecommend())) {
            queryWrapper.eq("is_recommend", req.getIsRecommend());
        }
        // 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("startTime")), "create_date", params.get("startTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_date", params.get("endTime"));
		}
        startPage();
        return getDataTable(topicGroupService.list(queryWrapper));
    }

    /**
     * 导出话题分组列表
     */
    @RequiresPermissions("cat:TopicGroup:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(TopicGroup topicGroup) {
        List<TopicGroup> list = topicGroupService.list(new QueryWrapper<>());
        ExcelUtil<TopicGroup> util = new ExcelUtil<TopicGroup>(TopicGroup.class);
        return util.exportExcel(list, "TopicGroup");
    }

    /**
     * 新增话题分组
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存话题分组
     */
    @RequiresPermissions("cat:TopicGroup:add")
    @Log(title = "话题分组", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestParam("file") MultipartFile iconUrl, TopicGroup topicGroup) throws IOException {
        if(iconUrl!=null&& StrUtil.isNotEmpty(iconUrl.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(iconUrl);
            topicGroup.setCoverImg(cloudPath);
        }
        return toAjax(topicGroupService.save(topicGroup));
    }

    /**
     * 修改话题分组
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        TopicGroup topicGroup = topicGroupService.getById(id);
        mmap.put("topicGroup", topicGroup);
        return prefix + "/edit";
    }

    /**
     * 修改保存话题分组
     */
    @RequiresPermissions("cat:TopicGroup:edit")
    @Log(title = "话题分组", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestParam("file") MultipartFile iconUrl,TopicGroup topicGroup) throws IOException {
        if(iconUrl!=null&& StrUtil.isNotEmpty(iconUrl.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(iconUrl);
            topicGroup.setCoverImg(cloudPath);
        }
        return toAjax(topicGroupService.updateById(topicGroup));
    }

    /**
     * 删除话题分组
     */
    @RequiresPermissions("cat:TopicGroup:remove")
    @Log(title = "话题分组", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(topicGroupService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
