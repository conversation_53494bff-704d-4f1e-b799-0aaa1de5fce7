package com.cat.modules.order.thread.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


@Data
public class OrderPrimary implements Serializable {

    private String id;
    private String userId;
    private String orderNo;
    private String outTradeNo;
    private String businessId;
    private String businessType;
    private BigDecimal payment;
    private Integer paymentType;
    
    private Integer status;
    private String shopId;
    private Date createDate;

}
