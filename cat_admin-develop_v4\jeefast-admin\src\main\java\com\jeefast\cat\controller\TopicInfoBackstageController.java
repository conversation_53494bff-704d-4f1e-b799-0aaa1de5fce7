package com.jeefast.cat.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.cat.modules.topic.entity.TopicGroup;
import com.cat.modules.topic.service.ITopicGroupService;
import com.cat.util.BlankUtil;
import com.jeefast.common.utils.file.FileUploadUtils;
import com.jeefast.common.utils.file.UploadCloudFileUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.TopicInfoBackstage;
import com.jeefast.cat.service.ITopicInfoBackstageService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 话题Controller
 * 
 * <AUTHOR>
 * @date 2020-08-08
 */
@Controller
@RequestMapping("/cat/topicInfoBackstage")
public class TopicInfoBackstageController extends BaseController {
    private String prefix = "cat/topicInfoBackstage";

    @Autowired
    private ITopicInfoBackstageService topicInfoBackstageService;
    @Autowired
    private ITopicGroupService topicGroupService;

    @Autowired
    private UploadCloudFileUtil uploadCloudFileUtil;

    @RequiresPermissions("cat:topicInfoBackstage:view")
    @GetMapping()
    public String topicInfoBackstage(TopicInfoBackstage topicInfoBackstage,ModelMap mmap) {
        QueryWrapper<TopicGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("1","1");
        queryWrapper.orderByAsc("sort_no");
        mmap.put("topicGrpupList",topicGroupService.list(queryWrapper));
        mmap.put("groupId",topicInfoBackstage.getGroupId());
        return prefix + "/topicInfoBackstage";
    }

    /**
     * 查询话题列表
     */
    @RequiresPermissions("cat:topicInfoBackstage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(TopicInfoBackstage topicInfoBackstage,ModelMap mmap) {
        QueryWrapper<TopicInfoBackstage> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
        if(StringUtils.isNotEmpty(topicInfoBackstage.getGroupId())) {
            queryWrapper.eq("group_id", topicInfoBackstage.getGroupId());
        }
        if(StringUtils.isNotEmpty(topicInfoBackstage.getTopicName())) {
    		queryWrapper.like("topic_name", topicInfoBackstage.getTopicName());
    	} 
    	if(BlankUtil.isNotEmpty(topicInfoBackstage.getJoinCount())) {
    		queryWrapper.gt("join_count", topicInfoBackstage.getJoinCount());
    	}
        if(BlankUtil.isNotEmpty(topicInfoBackstage.getCiteCount())) {
            queryWrapper.gt("cite_count", topicInfoBackstage.getCiteCount());
        }
        if (ObjectUtil.isNotNull(topicInfoBackstage.getParams()) && BlankUtil.isNotEmpty(topicInfoBackstage.getParams().get("beginCreateDate"))) {
            queryWrapper.ge("create_date", topicInfoBackstage.getParams().get("beginCreateDate"));
        }
        if (ObjectUtil.isNotNull(topicInfoBackstage.getParams()) && BlankUtil.isNotEmpty(topicInfoBackstage.getParams().get("endCreateDate"))) {
            queryWrapper.le("create_date", topicInfoBackstage.getParams().get("endCreateDate"));
        }
		// 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        return getDataTable(topicInfoBackstageService.list(queryWrapper));
    }

    /**
     * 导出话题列表
     */
    @RequiresPermissions("cat:topicInfoBackstage:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(TopicInfoBackstage topicInfoBackstage) {
        List<TopicInfoBackstage> list = topicInfoBackstageService.list(new QueryWrapper<>());
        ExcelUtil<TopicInfoBackstage> util = new ExcelUtil<TopicInfoBackstage>(TopicInfoBackstage.class);
        return util.exportExcel(list, "topicInfoBackstage");
    }

    /**
     * 新增话题
     */
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        QueryWrapper<TopicGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("1","1");
        queryWrapper.orderByAsc("sort_no");
        mmap.put("topicGrpupList",topicGroupService.list(queryWrapper));
        return prefix + "/add";
    }

    /**
     * 新增保存话题
     */
    @RequiresPermissions("cat:topicInfoBackstage:add")
    @Log(title = "话题", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestParam("file") MultipartFile multipartFile,TopicInfoBackstage topicInfoBackstage) throws IOException {
        if(multipartFile!=null&& StrUtil.isNotEmpty(multipartFile.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            topicInfoBackstage.setCoverImage(cloudPath);
        }
        return toAjax(topicInfoBackstageService.save(topicInfoBackstage));
    }

    /**
     * 修改话题
     */
    @GetMapping("/edit/{topicId}")
    public String edit(@PathVariable("topicId") String topicId, ModelMap mmap) {
        TopicInfoBackstage topicInfoBackstage = topicInfoBackstageService.getById(topicId);
        QueryWrapper<TopicGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("1","1");
        queryWrapper.orderByAsc("sort_no");
        mmap.put("topicGrpupList",topicGroupService.list(queryWrapper));
        mmap.put("topicInfoBackstage", topicInfoBackstage);
        return prefix + "/edit";
    }

    /**
     * 修改保存话题
     */
    @RequiresPermissions("cat:topicInfoBackstage:edit")
    @Log(title = "话题", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestParam("file") MultipartFile multipartFile,TopicInfoBackstage topicInfoBackstage) throws IOException {
        if(multipartFile!=null&& StrUtil.isNotEmpty(multipartFile.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            topicInfoBackstage.setCoverImage(cloudPath);
        }
        return toAjax(topicInfoBackstageService.updateById(topicInfoBackstage));
    }

    /**
     * 删除话题
     */
    @RequiresPermissions("cat:topicInfoBackstage:remove")
    @Log(title = "话题", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(topicInfoBackstageService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
