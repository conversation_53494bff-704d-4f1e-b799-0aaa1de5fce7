package com.jeefast.cat.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.jeefast.common.utils.file.FileUploadUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.HomeMenuBackstage;
import com.jeefast.cat.service.IHomeMenuBackstageService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 首页菜单Controller
 * 
 * <AUTHOR>
 * @date 2020-11-08
 */
@Controller
@RequestMapping("/cat/homeMenuBackstage")
public class HomeMenuBackstageController extends BaseController {
    private String prefix = "cat/homeMenuBackstage";

    @Autowired
    private IHomeMenuBackstageService homeMenuBackstageService;

    @RequiresPermissions("cat:homeMenuBackstage:view")
    @GetMapping()
    public String homeMenuBackstage() {
        return prefix + "/homeMenuBackstage";
    }

    /**
     * 查询首页菜单列表
     */
    @RequiresPermissions("cat:homeMenuBackstage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(HomeMenuBackstage homeMenuBackstage) {
        QueryWrapper<HomeMenuBackstage> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(homeMenuBackstage.getName())) {
    		queryWrapper.like("name", homeMenuBackstage.getName());
    	} 
    	if(ObjectUtil.isNotNull(homeMenuBackstage.getStatus())) {
    		queryWrapper.eq("status", homeMenuBackstage.getStatus());
    	}
        if(StrUtil.isNotEmpty(homeMenuBackstage.getBusinessType())) {
            queryWrapper.eq("business_type", homeMenuBackstage.getBusinessType());
        }
		// 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        queryWrapper.orderByAsc("sort_on");
        return getDataTable(homeMenuBackstageService.list(queryWrapper));
    }

    /**
     * 导出首页菜单列表
     */
    @RequiresPermissions("cat:homeMenuBackstage:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(HomeMenuBackstage homeMenuBackstage) {
        List<HomeMenuBackstage> list = homeMenuBackstageService.list(new QueryWrapper<>());
        ExcelUtil<HomeMenuBackstage> util = new ExcelUtil<HomeMenuBackstage>(HomeMenuBackstage.class);
        return util.exportExcel(list, "homeMenuBackstage");
    }

    /**
     * 新增首页菜单
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存首页菜单
     */
    @RequiresPermissions("cat:homeMenuBackstage:add")
    @Log(title = "首页菜单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestParam("file") MultipartFile multipartFile, HomeMenuBackstage homeMenuBackstage) throws IOException {
        if(multipartFile!=null&& StrUtil.isNotEmpty(multipartFile.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            homeMenuBackstage.setImageUrl(cloudPath);
        }
        return toAjax(homeMenuBackstageService.save(homeMenuBackstage));
    }

    /**
     * 修改首页菜单
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        HomeMenuBackstage homeMenuBackstage = homeMenuBackstageService.getById(id);
        mmap.put("homeMenuBackstage", homeMenuBackstage);
        return prefix + "/edit";
    }

    /**
     * 修改保存首页菜单
     */
    @RequiresPermissions("cat:homeMenuBackstage:edit")
    @Log(title = "首页菜单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestParam("file") MultipartFile multipartFile,HomeMenuBackstage homeMenuBackstage) throws IOException {
        if(multipartFile!=null&& StrUtil.isNotEmpty(multipartFile.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            homeMenuBackstage.setImageUrl(cloudPath);
        }
        return toAjax(homeMenuBackstageService.updateById(homeMenuBackstage));
    }

    /**
     * 删除首页菜单
     */
    @RequiresPermissions("cat:homeMenuBackstage:remove")
    @Log(title = "首页菜单", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(homeMenuBackstageService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
