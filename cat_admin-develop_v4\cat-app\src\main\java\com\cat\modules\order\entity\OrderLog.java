package com.cat.modules.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class OrderLog extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id",type = IdType.UUID)
    @TableField("id")
    private String id;

    

    @TableField("order_id")
    private String orderId;

    

    @TableField("user_id")
    private String userId;

    

    @TableField("business_id")
    private String businessId;

    

    @TableField("business_type")
    private String businessType;

    

    @TableField("status")
    private Integer status;

    

    @TableField("remark")
    private String remark;



}
