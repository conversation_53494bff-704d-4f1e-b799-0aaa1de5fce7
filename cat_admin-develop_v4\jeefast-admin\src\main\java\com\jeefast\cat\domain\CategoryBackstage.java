package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                            import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 商品类别表 category
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("category")
public class CategoryBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /** 父类别id */
    @Excel(name = "父级ID")
    private String parentId;
    /** 类别名称 */
    @Excel(name = "类别名称")
    private String name;
    /** 分类图标 */
    @Excel(name = "分类图标")
    private String image;
    /** 类别状态 */
    @Excel(name = "类别状态")
    private Integer status;
    /** 排序值 */
    @Excel(name = "排序值")
    private Integer sortOn;
    /** 创建时间 */
    /*@Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;*/
    @TableField(exist = false)
    private boolean flag;
}
