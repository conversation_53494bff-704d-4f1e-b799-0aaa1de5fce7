package com.cat.modules.attention.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.CamelCaseMap;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.attention.entity.AttentionInfo;
import com.cat.modules.attention.mapper.AttentionInfoMapper;
import com.cat.modules.attention.service.IAttentionInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Slf4j
@Service
public class AttentionInfoServiceImpl extends ServiceImpl<AttentionInfoMapper, AttentionInfo> implements IAttentionInfoService {
    @Autowired
    private AttentionInfoMapper attentionInfoMapper;

    @Value("${dynamic.attentionPubDate}")
    private String attentionPubDate;

    @Override
    public Integer updateToUserNewDynamicNum(String toUserId, Integer diffNum, Date dynamicDate) {
        if(ObjectUtil.isNotNull(dynamicDate) && DateUtil.parse(attentionPubDate).after(dynamicDate)){
            //此功能上线前的动态不处理
            return 1;
        }
        return attentionInfoMapper.updateToUserNewDynamicNum(toUserId,diffNum,dynamicDate);
    }

}
