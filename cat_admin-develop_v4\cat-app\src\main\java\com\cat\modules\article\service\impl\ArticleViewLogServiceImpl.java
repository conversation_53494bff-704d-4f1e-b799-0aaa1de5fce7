package com.cat.modules.article.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.article.entity.ArticleViewLog;
import com.cat.modules.article.mapper.ArticleViewLogMapper;
import com.cat.modules.article.service.IArticleViewLogService;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service

public class ArticleViewLogServiceImpl extends ServiceImpl<ArticleViewLogMapper, ArticleViewLog> implements IArticleViewLogService {
    @Autowired
    private ArticleViewLogMapper articleViewLogMapper;



}
