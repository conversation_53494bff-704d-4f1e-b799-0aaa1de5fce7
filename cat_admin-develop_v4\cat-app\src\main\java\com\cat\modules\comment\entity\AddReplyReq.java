package com.cat.modules.comment.entity;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;


@Data
public class AddReplyReq {


    @NotBlank(message = "业务ID不允许为空")
    private String businessId;

    
    @NotBlank(message = "业务Type不允许为空")
    private String businessType;

    @NotBlank(message = "评论ID不允许为空")
    private String parentId;

    @NotBlank(message = "业务Type不允许为空")
    private String content;

    private String userId;

    
    private String formId;

    private Date createDate;

}
