package com.jeefast.cat.job;

import com.jeefast.cat.service.IDynamicContentSecurityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 视频内容安全任务情况查询
 */
@Slf4j
@Component("videoSecurityTaskJob")
public class VideoSecurityTaskJob {

    @Resource
    IDynamicContentSecurityService dynamicContentSecurityService;

    /**
     * 每分钟查一次
     */
    public void videoModerationQueryJob() {
        log.info("视频内容安全任务情况查询开始");
        dynamicContentSecurityService.videoModerationQueryJob();
        log.info("视频内容安全任务情况查询结束");
    }
}
