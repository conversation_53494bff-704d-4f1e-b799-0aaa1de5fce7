package com.jeefast.cat.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.jeefast.cat.common.EleAliasInfo;
import com.jeefast.cat.domain.NurseBillModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigInteger;
import java.util.List;

/**
 * 动态内容 数据层
 *
 * <AUTHOR>
 * @date 2020-08-08
 */
public interface NurseBillMapper extends BaseMapper<NurseBillModel> {

    List<CamelCaseMap<String, Object>> nurseBillList(@Param("ew") Wrapper queryWrapper);

    void moveEleAliasToNewEle(@Param("eleId") BigInteger eleId, @Param("ew") Wrapper queryWrapper);

    void moveEleDetail(@Param("eleId") BigInteger eleId, @Param("eleName") String eleName, @Param("ew") Wrapper queryWrapper);

    void insertEleAlias(@Param("eleId") BigInteger eleId, @Param("aliasName") String aliasName, @Param("projectLabel") String projectLabel);

    EleAliasInfo selectAliasByEleId(BigInteger eleId);

    int deleteAliasByEleId(BigInteger eleId);
}