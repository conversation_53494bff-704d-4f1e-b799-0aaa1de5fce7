package com.jeefast.cat.job;

import com.jeefast.cat.service.IHotSearchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 刷新热门版单
 */
@Slf4j
@Component("hotSearchJob")
public class HotSearchJob {

    @Resource
    IHotSearchService hotSearchService;

    /**
     * 每小时刷新一次热版
     */
    public void refreshTopTen() {
        log.info("刷新热门版单开始");
        hotSearchService.refreshTopTen(null);
        log.info("刷新热门版单结束");
    }
}
