package com.cat.modules.sensiWords.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class SensiWords extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id",type = IdType.UUID)
    @TableField("id")
    private String id;

    
    @TableField("word")
    private String word;

    
    @TableField("classify")
    private String classify;

    
    @TableField("level")
    private Integer level;

    
    @TableField("type")
    private String type;



}
