package com.cat.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cat.modules.advert.entity.AdvertInfo;
import com.cat.modules.advert.service.IAdvertInfoService;
import com.jeefast.common.enums.CachEnum;
import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;


@Slf4j
@Component("adInitJob")
public class AdInitJob {

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IAdvertInfoService advertInfoService;


    
    public void main() {
        initStartAd();
    }


    
    public void initStartAd() {
        Date dateNow = new Date();
        List<AdvertInfo> adList = advertInfoService.list(new QueryWrapper<AdvertInfo>()
                .select("id,type,file_url,href,href_type")
                .eq("type","4")
                .ge("end_date",dateNow).le("start_date",dateNow).orderByAsc("sort_no")
        );
        if(ArrayUtil.isNotEmpty(adList)){
            redisUtil.set(CachEnum.ADVERT_START.getCode(), JSON.toJSONString(adList.get(0)));
        }else{
            redisUtil.set(CachEnum.ADVERT_START.getCode(), "");
        }

    }


}
