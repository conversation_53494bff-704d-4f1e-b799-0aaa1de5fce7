package com.cat.modules.pet.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.pet.entity.PetFeedLog;
import com.cat.modules.pet.mapper.PetFeedLogMapper;
import com.cat.modules.pet.service.IPetFeedLogService;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;


@Slf4j
@Service

public class PetFeedLogServiceImpl extends ServiceImpl<PetFeedLogMapper, PetFeedLog> implements IPetFeedLogService {
    @Autowired
    private PetFeedLogMapper petFeedLogMapper;

    private static SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");


    @Override
    public boolean getByUserIdDayLog(String userId, String petId) {
        String days = df.format(new Date());
        int i = petFeedLogMapper.getByUserIdDayLog(userId,petId,days);
        if(i>0){
            return false;
        }
        return true;
    }



}
