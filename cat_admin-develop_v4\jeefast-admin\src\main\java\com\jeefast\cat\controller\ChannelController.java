package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;

import com.jeefast.system.domain.SysUser;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.Channel;
import com.jeefast.cat.service.IChannelService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * AI分诊渠道Controller
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@Controller
@RequestMapping("/cat/channel")
public class ChannelController extends BaseController {
    private String prefix = "cat/channel";

    @Autowired
    private IChannelService channelService;

    @RequiresPermissions("cat:channel:view")
    @GetMapping()
    public String channel() {
        return prefix + "/channel";
    }

    /**
     * 查询AI分诊渠道列表
     */
    @RequiresPermissions("cat:channel:view")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Channel channel) {
        QueryWrapper<Channel> queryWrapper = new QueryWrapper<>();
        // 需要根据页面查询条件进行组装
        if (StringUtils.isNotEmpty(channel.getChannelName())) {
            queryWrapper.like("channel_name", channel.getChannelName());
        }

        queryWrapper.eq("is_delete", 0);
        // 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        return getDataTable(channelService.list(queryWrapper));
    }

    /**
     * 导出AI分诊渠道列表
     */
    @RequiresPermissions("cat:channel:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Channel channel) {
        List<Channel> list = channelService.list(new QueryWrapper<>());
        ExcelUtil<Channel> util = new ExcelUtil<Channel>(Channel.class);
        return util.exportExcel(list, "channel");
    }

    /**
     * 新增AI分诊渠道
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存AI分诊渠道
     */
    @RequiresPermissions("cat:channel:add")
    @Log(title = "AI分诊渠道", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Channel channel) {
        return toAjax(channelService.save(channel));
    }

    /**
     * 修改AI分诊渠道
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        Channel channel = channelService.getById(id);
        mmap.put("channel", channel);
        return prefix + "/edit";
    }

    /**
     * 修改保存AI分诊渠道
     */
    @RequiresPermissions("cat:channel:edit")
    @Log(title = "AI分诊渠道", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Channel channel) {
        return toAjax(channelService.updateById(channel));
    }

    /**
     * 删除AI分诊渠道
     */
    @RequiresPermissions("cat:channel:remove")
    @Log(title = "AI分诊渠道", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(channelService.softDelete(ids));
    }

    /**
     * 用户状态修改
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @RequiresPermissions("cat:channel:edit")
    @PostMapping("/changeStatus")
    @ResponseBody
    public AjaxResult changeStatus(Channel channel) {
        return toAjax(channelService.changeStatus(channel));
    }
}
