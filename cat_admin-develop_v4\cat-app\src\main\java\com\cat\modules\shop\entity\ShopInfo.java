package com.cat.modules.shop.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;

import com.jeefast.common.annotation.Excel;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import com.jeefast.common.core.entity.CatBaseEntity;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.annotation.TableField;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ShopInfo extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id",type = IdType.UUID)
    @TableField("id")
    private String id;

    
    @Excel(name = "店铺名称")
    @TableField("shop_name")
    private String shopName;

    

    @TableField("user_id")
    private String userId;

    

    @TableField("shop_type")
    private Integer shopType;

    

    @TableField("intro")
    private String intro;

    

    @TableField("shop_notice")
    private String shopNotice;

    

    @TableField("shop_industry")
    private Integer shopIndustry;

    
    @Excel(name = "分类名称")
    @TableField("category_id")
    private String categoryId;

    

    @TableField("category_name")
    private String categoryName;

    

    @TableField("shop_owner")
    private String shopOwner;

    

    @TableField("mobile")
    private String mobile;

    

    @TableField("tel")
    private String tel;

    
    @Excel(name = "评分")
    @TableField("score")
    private BigDecimal score;

    
    @Excel(name = "平均价")
    @TableField("avg_price")
    private Integer avgPrice;

    

    @TableField("shop_lat")
    private BigDecimal shopLat;

    

    @TableField("shop_lng")
    private BigDecimal shopLng;

    
    @Excel(name = "详细地址")
    @TableField("shop_address")
    private String shopAddress;

    
    @Excel(name = "省份")
    @TableField("province")
    private String province;

    

    @TableField("city")
    private String city;

    
    @Excel(name = "城市")
    @TableField("area")
    private String area;

    

    @TableField("pca_code")
    private String pcaCode;

    
    @Excel(name = "店铺logo")
    @TableField("shop_logo")
    private String shopLogo;

    

    @TableField("shop_photos")
    private String shopPhotos;

    

    @TableField("open_time")
    private String openTime;

    
    @Excel(name = "店铺状态")
    @TableField("shop_status")
    private Integer shopStatus;

    

    @TableField("apply_date")
    private LocalDateTime applyDate;

    

    @TableField("success_date")
    private LocalDateTime successDate;

    

    @TableField("fail_message")
    private String failMessage;

    

    @TableField("fail_date")
    private LocalDateTime failDate;

    

    @TableField("transport_type")
    private Integer transportType;

    

    @TableField("fixed_freight")
    private BigDecimal fixedFreight;

    

    @TableField("full_free_shipping")
    private BigDecimal fullFreeShipping;

    

    @TableField("praise_count")
    private Integer praiseCount;

    

    @TableField("star_count")
    private Integer starCount;

    
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("update_date")
    private LocalDateTime updateDate;

    
    @TableField("restaurant_id")
    private String restaurantId;


}
