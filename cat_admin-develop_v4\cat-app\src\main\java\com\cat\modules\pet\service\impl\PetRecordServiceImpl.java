package com.cat.modules.pet.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.pet.entity.PetRecord;
import com.cat.modules.pet.entity.PetRecordMedia;
import com.cat.modules.pet.mapper.PetRecordMapper;
import com.cat.modules.pet.service.IPetRecordMediaService;
import com.cat.modules.pet.service.IPetRecordService;
import com.cat.util.BlankUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class PetRecordServiceImpl extends ServiceImpl<PetRecordMapper, PetRecord> implements IPetRecordService {
    @Autowired
    private PetRecordMapper petRecordMapper;
    @Autowired
    private IPetRecordMediaService petRecordMediaService;



}
