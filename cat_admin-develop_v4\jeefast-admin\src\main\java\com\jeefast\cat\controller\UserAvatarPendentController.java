package com.jeefast.cat.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.StrUtil;
import com.cat.modules.user.entity.UserAvatarPendent;
import com.cat.modules.user.service.IUserAvatarPendentService;
import com.jeefast.common.utils.file.FileUploadUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 头像挂件Controller
 * 
 * <AUTHOR>
 * @date 2021-08-28
 */
@Controller
@RequestMapping("/cat/userAvatarPendent")
public class UserAvatarPendentController extends BaseController {
    private String prefix = "cat/userAvatarPendent";

    @Autowired
    private IUserAvatarPendentService userAvatarPendentService;

    @RequiresPermissions("cat:userAvatarPendent:view")
    @GetMapping()
    public String userAvatarPendent() {
        return prefix + "/userAvatarPendent";
    }

    /**
     * 查询头像挂件列表
     */
    @RequiresPermissions("cat:userAvatarPendent:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(UserAvatarPendent req) {
        QueryWrapper<UserAvatarPendent> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getName())) {
    		queryWrapper.like("name", req.getName());
    	} 
    	if(StringUtils.isNotEmpty(req.getGroupCode())) {
    		queryWrapper.eq("group_code", req.getGroupCode());
    	}
        if(StringUtils.isNotEmpty(req.getStatus())) {
            queryWrapper.eq("status", req.getStatus());
        }
		// 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        return getDataTable(userAvatarPendentService.list(queryWrapper));
    }

    /**
     * 导出头像挂件列表
     */
    @RequiresPermissions("cat:userAvatarPendent:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(UserAvatarPendent userAvatarPendent) {
        List<UserAvatarPendent> list = userAvatarPendentService.list(new QueryWrapper<>());
        ExcelUtil<UserAvatarPendent> util = new ExcelUtil<UserAvatarPendent>(UserAvatarPendent.class);
        return util.exportExcel(list, "userAvatarPendent");
    }

    /**
     * 新增头像挂件
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存头像挂件
     */
    @RequiresPermissions("cat:userAvatarPendent:add")
    @Log(title = "头像挂件", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestParam("iconUrlFile") MultipartFile iconUrl, @RequestParam("thumnailFile") MultipartFile thumnail, UserAvatarPendent userAvatarPendent) throws IOException {
        if(iconUrl!=null&& StrUtil.isNotEmpty(iconUrl.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(iconUrl);
            userAvatarPendent.setIconUrl(cloudPath);
        }
        if(thumnail!=null&& StrUtil.isNotEmpty(thumnail.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(thumnail);
            userAvatarPendent.setThumnail(cloudPath);
        }
        return toAjax(userAvatarPendentService.save(userAvatarPendent));
    }

    /**
     * 修改头像挂件
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        UserAvatarPendent userAvatarPendent = userAvatarPendentService.getById(id);
        mmap.put("userAvatarPendent", userAvatarPendent);
        return prefix + "/edit";
    }

    /**
     * 修改保存头像挂件
     */
    @RequiresPermissions("cat:userAvatarPendent:edit")
    @Log(title = "头像挂件", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestParam("iconUrlFile") MultipartFile iconUrl, @RequestParam("thumnailFile") MultipartFile thumnail,UserAvatarPendent userAvatarPendent) throws IOException {
        if(iconUrl!=null&& StrUtil.isNotEmpty(iconUrl.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(iconUrl);
            userAvatarPendent.setIconUrl(cloudPath);
        }
        if(thumnail!=null&& StrUtil.isNotEmpty(thumnail.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(thumnail);
            userAvatarPendent.setThumnail(cloudPath);
        }
        return toAjax(userAvatarPendentService.updateById(userAvatarPendent));
    }

    /**
     * 删除头像挂件
     */
    @RequiresPermissions("cat:userAvatarPendent:remove")
    @Log(title = "头像挂件", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(userAvatarPendentService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
