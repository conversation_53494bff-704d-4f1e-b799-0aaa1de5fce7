package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.StrUtil;
import com.cat.modules.auth.entity.AuthInfo;
import com.cat.modules.auth.service.IAuthInfoService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 认证信息Controller
 * 
 * <AUTHOR>
 * @date 2020-08-26
 */
@Controller
@RequestMapping("/cat/auth")
public class AuthInfoController extends BaseController {
    private String prefix = "cat/auth";

    @Autowired
    private IAuthInfoService authInfoService;

    @RequiresPermissions("cat:auth:view")
    @GetMapping()
    public String auth() {
        return prefix + "/auth";
    }

    /**
     * 查询认证信息列表
     */
    @RequiresPermissions("cat:auth:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AuthInfo authInfo) {
        Map<String, Object> condi = new HashMap<>();
//        QueryWrapper<AuthInfo> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
        if (StrUtil.isNotBlank(authInfo.getAuthType())) {
            condi.put("authType", authInfo.getAuthType());
        }
        if (StrUtil.isNotBlank(authInfo.getSkype())) {
            condi.put("skype", authInfo.getSkype());
        }
        if (StrUtil.isNotBlank(authInfo.getTelegram())) {
            condi.put("telegram", authInfo.getTelegram());
        }

        if (StrUtil.isNotBlank(authInfo.getStatus())) {
            condi.put("status", authInfo.getStatus());
        }
        startPage();
        return getDataTable(authInfoService.getList(condi));
    }

    /**
     * 导出认证信息列表
     */
    @RequiresPermissions("cat:auth:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(AuthInfo authInfo) {
        List<AuthInfo> list = authInfoService.list(new QueryWrapper<>());
        ExcelUtil<AuthInfo> util = new ExcelUtil<AuthInfo>(AuthInfo.class);
        return util.exportExcel(list, "auth");
    }

    /**
     * 新增认证信息
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    @GetMapping("/audit")
    public String audit() {
        return prefix + "/audit";
    }

    /**
     * 新增保存认证信息
     */
    @RequiresPermissions("cat:auth:add")
    @Log(title = "认证信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(AuthInfo authInfo) {
        return toAjax(authInfoService.save(authInfo));
    }

    /**
     * 修改认证信息
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        AuthInfo authInfo = authInfoService.getById(id);
        mmap.put("authInfo", authInfo);
        return prefix + "/edit";
    }

    /**
     * 修改保存认证信息
     */
    @RequiresPermissions("cat:auth:edit")
    @Log(title = "认证信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(AuthInfo authInfo) {
        return toAjax(authInfoService.updateById(authInfo));
    }

    /**
     * 删除认证信息
     */
    @RequiresPermissions("cat:auth:remove")
    @Log(title = "认证信息", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(authInfoService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
