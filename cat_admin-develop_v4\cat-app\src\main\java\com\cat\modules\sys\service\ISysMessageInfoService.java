package com.cat.modules.sys.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cat.modules.push.entity.PushVo;
import com.cat.modules.sys.dto.SysEndMessageDTO;
import com.cat.modules.sys.entity.SysMessageInfo;

import java.util.List;


public interface ISysMessageInfoService extends IService<SysMessageInfo> {

    
    void endMessage(String userId,String content,String businessId,String businessType,String type);



    void endMessage(SysEndMessageDTO dto);

    boolean appMeesgaSend(PushVo pushVo);


}
