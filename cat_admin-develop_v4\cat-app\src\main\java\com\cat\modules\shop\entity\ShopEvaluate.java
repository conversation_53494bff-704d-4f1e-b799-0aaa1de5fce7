package com.cat.modules.shop.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import com.jeefast.common.core.entity.CatBaseEntity;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.annotation.TableField;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ShopEvaluate extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id",type = IdType.UUID)
    @TableField("id")
    private String id;

    

    @TableField("shop_id")
    private String shopId;

    

    @TableField("product_id")
    private String productId;

    

    @TableField("content")
    private String content;

    

    @TableField("reply_content")
    private String replyContent;

    

    @TableField("user_id")
    private String userId;

    

    @TableField("praise_count")
    private Integer praiseCount;

    

    @TableField("score")
    private BigDecimal score;

    

    @TableField("media")
    private String media;

    

    @TableField("is_check")
    private String isCheck;



}
