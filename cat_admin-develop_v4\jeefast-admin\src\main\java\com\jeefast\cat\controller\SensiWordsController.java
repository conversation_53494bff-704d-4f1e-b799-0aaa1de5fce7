package com.jeefast.cat.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cat.modules.sensiWords.entity.SensiWords;
import com.cat.modules.sensiWords.service.ISensiWordsService;
import com.jeefast.common.annotation.Log;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.core.page.TableDataInfo;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 敏感词管理Controller
 * 
 * <AUTHOR>
 * @date 2023-05-28
 */
@Controller
@RequestMapping("/cat/sensiWords")
public class SensiWordsController extends BaseController {
    private String prefix = "cat/sensiWords";

    @Autowired
    private ISensiWordsService sensiWordsService;

    @RequiresPermissions("cat:sensiWords:view")
    @GetMapping()
    public String sensiWords() {
        return prefix + "/sensiWords";
    }

    /**
     * 查询敏感词管理列表
     */
    @RequiresPermissions("cat:sensiWords:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SensiWords sensiWords) {
        QueryWrapper<SensiWords> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(sensiWords.getWord())) {
    		queryWrapper.like("word", sensiWords.getWord());
    	}
    	if(StringUtils.isNotEmpty(sensiWords.getClassify())) {
    		queryWrapper.eq("classify", sensiWords.getClassify());
    	}
        if(StringUtils.isNotEmpty(sensiWords.getType())) {
            queryWrapper.eq("type", sensiWords.getType());
        }
		// 特殊查询时条件需要进行单独组装
		Map<String, Object> params = sensiWords.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endCreateDate")), "create_date", params.get("endCreateDate"));
		}
        startPage();
        return getDataTable(sensiWordsService.list(queryWrapper));
    }

    /**
     * 导出敏感词管理列表
     */
    @RequiresPermissions("cat:sensiWords:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SensiWords sensiWords) {
        List<SensiWords> list = sensiWordsService.list(new QueryWrapper<>());
        ExcelUtil<SensiWords> util = new ExcelUtil<SensiWords>(SensiWords.class);
        return util.exportExcel(list, "sensiWords");
    }

    /**
     * 新增敏感词管理
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存敏感词管理
     */
    @RequiresPermissions("cat:sensiWords:add")
    @Log(title = "敏感词管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SensiWords sensiWords) {
        return toAjax(sensiWordsService.save(sensiWords));
    }

    /**
     * 修改敏感词管理
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        SensiWords sensiWords = sensiWordsService.getById(id);
        mmap.put("sensiWords", sensiWords);
        return prefix + "/edit";
    }

    /**
     * 修改保存敏感词管理
     */
    @RequiresPermissions("cat:sensiWords:edit")
    @Log(title = "敏感词管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SensiWords sensiWords) {
        return toAjax(sensiWordsService.updateById(sensiWords));
    }

    /**
     * 删除敏感词管理
     */
    @RequiresPermissions("cat:sensiWords:remove")
    @Log(title = "敏感词管理", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(sensiWordsService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
