package com.cat.modules.advert.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class AdvertInfo extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    

    @TableField("type")
    private String type;

    

    @TableField("file_id")
    private String fileId;

    

    @TableField("file_url")
    private String fileUrl;


    

    @TableField("href_type")
    private String hrefType;



    

    @TableField("href")
    private String href;


    
    @TableField("sort_no")
    private Integer sortNo;






}
