package com.jeefast.cat.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cat.modules.user.entity.UserLogoutLog;
import com.cat.modules.user.service.IUserLogoutLogService;
import com.jeefast.common.annotation.Log;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.core.page.TableDataInfo;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 用户注销账号记录Controller
 * 
 * <AUTHOR>
 * @date 2023-03-05
 */
@Controller
@RequestMapping("/cat/UserLogoutLog")
public class UserLogoutLogController extends BaseController {
    private String prefix = "cat/UserLogoutLog";

    @Autowired
    private IUserLogoutLogService userLogoutLogService;

    @RequiresPermissions("cat:UserLogoutLog:view")
    @GetMapping()
    public String UserLogoutLog() {
        return prefix + "/UserLogoutLog";
    }

    /**
     * 查询用户注销账号记录列表
     */
    @RequiresPermissions("cat:UserLogoutLog:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(UserLogoutLog req) {
        QueryWrapper<UserLogoutLog> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getUserId())) {
    		queryWrapper.eq("t1.user_id", req.getUserId());
    	}
        if(ObjectUtil.isNotNull(req.getStatus())) {
            queryWrapper.eq("t1.status", req.getStatus());
        }
        // 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
            queryWrapper.like(StringUtils.isNotEmpty((String)params.get("userName")), "t2.user_name", params.get("userName"));
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "t1.create_date", params.get("beginCreateDate"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endCreateDate")), "t1.create_date", params.get("endCreateDate"));
		}
        startPage();
        return getDataTable(userLogoutLogService.listExt(queryWrapper));
    }

    /**
     * 导出用户注销账号记录列表
     */
    @RequiresPermissions("cat:UserLogoutLog:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(UserLogoutLog userLogoutLog) {
        List<UserLogoutLog> list = userLogoutLogService.list(new QueryWrapper<>());
        ExcelUtil<UserLogoutLog> util = new ExcelUtil<UserLogoutLog>(UserLogoutLog.class);
        return util.exportExcel(list, "UserLogoutLog");
    }

    /**
     * 新增用户注销账号记录
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存用户注销账号记录
     */
    @RequiresPermissions("cat:UserLogoutLog:add")
    @Log(title = "用户注销账号记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(UserLogoutLog userLogoutLog) {
        return toAjax(userLogoutLogService.save(userLogoutLog));
    }

    /**
     * 修改用户注销账号记录
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        UserLogoutLog userLogoutLog = userLogoutLogService.getById(id);
        mmap.put("userLogoutLog", userLogoutLog);
        return prefix + "/edit";
    }

    /**
     * 修改保存用户注销账号记录
     */
    @RequiresPermissions("cat:UserLogoutLog:edit")
    @Log(title = "用户注销账号记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(UserLogoutLog userLogoutLog) {
        Integer status = userLogoutLog.getStatus();
        if(3 == status) {
            // 3-审核通过，进行账号权限清空
            return toAjax(userLogoutLogService.clearUserInfo(userLogoutLog));
        }else{
            return toAjax(userLogoutLogService.updateById(userLogoutLog));
        }
    }

    /**
     * 删除用户注销账号记录
     */
    @RequiresPermissions("cat:UserLogoutLog:remove")
    @Log(title = "用户注销账号记录", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(userLogoutLogService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
