package com.cat.util;

import cn.hutool.core.io.FileUtil;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

@Slf4j
@Component
public class HttpFile {


    
    public File getImageFromNetByUrl(String strUrl){
        try {
            String fileName=strUrl.substring(strUrl.lastIndexOf("/"),strUrl.length());
            if(fileName.lastIndexOf("?")>0){
                fileName = fileName.substring(0,fileName.lastIndexOf("?"));
            }
            
            URL url = new URL(strUrl);
            
            HttpURLConnection conn = (HttpURLConnection)url.openConnection();
            
            conn.setRequestMethod("GET");
            
            conn.setConnectTimeout(5 * 1000);
            
            InputStream inStream = conn.getInputStream();
            
            byte[] data = readInputStream(inStream);
            
            RedisUtil redisUtil = SpringContextHolder.getBean(RedisUtil.class);
            String path = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.upload.path");
            return FileUtil.writeBytes(data,path+fileName);
        } catch (Exception e) {
            log.error("strUrl："+strUrl+"，",e);
        }
        return null;
    }

    
    public File getImageFromNetByUrl(String strUrl,String fileName){
        try {
            
            URL url = new URL(strUrl);
            
            HttpURLConnection conn = (HttpURLConnection)url.openConnection();
            
            conn.setRequestMethod("GET");
            
            conn.setConnectTimeout(5 * 1000);
            
            InputStream inStream = conn.getInputStream();
            
            byte[] data = readInputStream(inStream);
            
            RedisUtil redisUtil = SpringContextHolder.getBean(RedisUtil.class);
            String path = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.upload.path");
            return FileUtil.writeBytes(data,path+fileName);
        } catch (Exception e) {
            log.error("strUrl："+strUrl+"，",e);
        }
        return null;
    }


    
    public static byte[] readInputStream(InputStream inStream) throws Exception{
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        
        byte[] buffer = new byte[1024];
        
        int len = 0;
        
        while( (len=inStream.read(buffer)) != -1 ){
            
            outStream.write(buffer, 0, len);
        }
        
        inStream.close();
        
        return outStream.toByteArray();
    }

}
