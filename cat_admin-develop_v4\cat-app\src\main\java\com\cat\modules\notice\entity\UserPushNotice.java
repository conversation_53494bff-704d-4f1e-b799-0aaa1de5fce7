package com.cat.modules.notice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 用户推送通知
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("user_push_notice")
public class UserPushNotice  extends CatBaseEntity {

    @TableId(type = IdType.UUID)
    private String id;

    /**
     * 发送方id
     */
    private String userId;

    /**
     * 接收方id
     */
    private String toUserId;

    /**
     * 类型
     */
    private String type;

    /**
     * 业务id，对应主键表id
     */
    private String objectId;

    /**
     * 标题
     */
    private String pushTitle;

    /**
     * 内容
     */
    private String pushDesc;

    /**
     * 额外参数
     */
    private String extraStr;

    /**
     * 返回结果
     */
    private String resJson;

    /**
     * 是否发送
     */
    private String isSend;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 是否阅读
     */
    private String isRead;

    /**
     * 阅读时间
     */
    private Date readTime;

    /**
     * 是否取消
     */
    private String isDelete;

    /**
     * 推送次数
     */
    private Integer sendCount;



    /**
     * 更新时间
     */
    private Date writeDate;

    /**
     * 创建人
     */
    private String createUid;

    /**
     * 更新人
     */
    private String writeUid;

    /**
     * 其他信息
     */
    private String other;
} 