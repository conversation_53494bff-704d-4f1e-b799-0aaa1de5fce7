package com.cat.modules.pet.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.dynamic.entity.DynamicMedia;
import com.cat.modules.pet.entity.PetInfo;
import com.cat.modules.pet.mapper.PetInfoMapper;
import com.cat.modules.pet.service.IPetInfoService;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.file.UploadCloudFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service

public class PetInfoServiceImpl extends ServiceImpl<PetInfoMapper, PetInfo> implements IPetInfoService {
    @Autowired
    private PetInfoMapper petInfoMapper;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private UploadCloudFileUtil uploadCloudFileUtil;


    @Override
    public boolean delete(List<String> asList) {
        boolean falg = false;
        
        String uploadType = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.cat.upload.type");
        for (String item : asList) {
            if("2".equals(uploadType)){
                
                PetInfo petInfo = this.getById(item);
                
                try {
                    uploadCloudFileUtil.delFile(petInfo.getAvatarUrl());
                }catch (Exception e){
                    log.error("删除云上文件失败：",e);
                }
            }
            
            falg = this.removeById(item);
        }
        return falg;
    }
}
