package com.cat.modules.shop.service.impl;

import com.cat.modules.shop.entity.ShopEvaluate;
import com.cat.modules.shop.mapper.ShopEvaluateMapper;
import com.cat.modules.shop.service.IShopEvaluateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import org.springframework.beans.factory.annotation.*;
import org.springframework.transaction.annotation.*;

import lombok.extern.slf4j.Slf4j;
import java.util.*;

@Slf4j
@Service
public class ShopEvaluateServiceImpl extends ServiceImpl<ShopEvaluateMapper, ShopEvaluate> implements IShopEvaluateService {
    @Autowired
    private ShopEvaluateMapper shopEvaluateMapper;


    @Override
    public double avgScore(String shopId) {
        return shopEvaluateMapper.avgScore(shopId);
    }
}
