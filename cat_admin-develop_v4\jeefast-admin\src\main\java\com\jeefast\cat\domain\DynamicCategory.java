package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 动态分类表 dynamic_category
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dynamic_category")
public class DynamicCategory extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /** 标题 */
    @Excel(name = "标题")
    private String title;
    /** 关键词 */
    @Excel(name = "关键词")
    private String keyword;
    /** 排序 */
    @Excel(name = "排序")
    private Long sortNo;
    /** 是否默认 */
    @Excel(name = "是否默认", readConverterExp = "0=否,1=是")
    private String beDefault;
    /** 是否禁用 */
    @Excel(name = "是否禁用", readConverterExp = "0=否,1=是")
    private String beDisabled;
    /** 是否删除 */
    private String isDelete;
}
