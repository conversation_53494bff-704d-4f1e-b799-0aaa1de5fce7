package com.cat.modules.user.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("user_info")
public class User extends CatBaseEntity {

    @TableId(value = "user_id",type = IdType.UUID)
    @JSONField(ordinal = 0)
    private String userId;

    private String userName;

    private String mobile;

    private String sex;

    private String avatarUrl;

    private String profiles;
    private Integer level;
    private Integer age;
    private String province;
    private String city;
    private String gps;
    private Integer charm;
    private Integer canNumber;
    private String openId;

    private String approve;

    private String country;
    private String language;


    private Integer attentionCount;

    private Integer fansCount;

    private String unionId;
    
    private String userNum;
    private String isDelete;
}
