package com.cat.modules.topic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class TopicGroup extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id",type = IdType.UUID)
    @TableField("id")
    private String id;

    
    @TableField("name")
    private String name;

    
    @TableField("cover_img")
    private String coverImg;

    

    @TableField("is_show")
    private String isShow;

    
    @TableField("sort_no")
    private Integer sortNo;

    
    @TableField("is_recommend")
    private String isRecommend;


    
    @TableField("topic_count")
    private Integer topicCount;

    

    @TableField("remark")
    private String remark;

    

    @TableField("is_delete")
    private String isDelete;



}
