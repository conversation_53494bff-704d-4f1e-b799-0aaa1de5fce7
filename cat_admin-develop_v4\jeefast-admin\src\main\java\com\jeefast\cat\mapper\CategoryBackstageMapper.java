package com.jeefast.cat.mapper;

import com.jeefast.cat.domain.CategoryBackstage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jeefast.system.domain.SysDept;

import java.util.List;

/**
 * 商品类别 数据层
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
public interface CategoryBackstageMapper extends BaseMapper<CategoryBackstage> {

    CategoryBackstage selectByCategoryId(String categoryId);

    List<CategoryBackstage> selectDeptList(CategoryBackstage category);
}