package com.cat.modules.product.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cat.modules.product.entity.Product;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface ProductMapper extends BaseMapper<Product> {

    List<CamelCaseMap> listExt(@Param("ew") QueryWrapper<Product> qw);


}