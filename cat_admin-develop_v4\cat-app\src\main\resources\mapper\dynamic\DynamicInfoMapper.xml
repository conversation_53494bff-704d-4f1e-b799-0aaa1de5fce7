<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.dynamic.mapper.DynamicInfoMapper">

    <!-- 通用查询映射结果  配置了autoMapping=true,其他的无需配置，将会自动映射-->
    <resultMap id="BaseResultMap" type="com.cat.modules.dynamic.entity.DynamicInfo" autoMapping="true" />


    <select id="recommendList" resultType="cn.hutool.core.map.CamelCaseMap">
        select t2.user_id,t2.sex,t1.pet_id,t2.avatar_url,t2.user_name,t2.level,t2.approve
        ,t1.title,t1.content,t1.addr,t1.praise_count,t1.comment_count,t1.create_date
        ,t1.type,t1.dynamic_id,t1.source,t1.width,t1.height,t1.cover_image,t1.media_count
		,t1.weight,'dynamic' as 'group',t1.view_count,t1.visual_view_count
        from dynamic_info t1
        left join user_info t2 on t2.user_id=t1.user_id
        where t1.is_check='1' and t1.is_delete='0' and article_type = 'null'
    </select>
    <select id="getTopTen" resultType="com.cat.modules.dynamic.entity.DynamicInfo">
        SELECT t1.dynamic_id,t1.title,t1.type,
               t1.visual_view_count,t1.view_count,(t1.visual_view_count + t1.view_count) AS totalCount
        FROM dynamic_info t1
        where t1.is_check='1' and t1.is_delete='0' and article_type not in ('test','morning','noon','night','other_card')
        ORDER BY totalCount desc
            LIMIT 10
    </select>
    <select id="viewCount" resultType="com.cat.modules.dynamic.resp.DynamicViewRespVo">
        SELECT dynamic_id as dynamicId,COUNT(0) AS countNum FROM dynamic_view_log
        <where>
            <if test="startTime != null and startTime != ''">
                and  create_date BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test='dynamicIds!=null  and dynamicIds.size() > 0'>
                and dynamic_id in
                <foreach collection="dynamicIds" item="dynamicId" open="(" separator="," close=")">
                    #{dynamicId}
                </foreach>
            </if>
        </where>
        GROUP BY dynamic_id;
    </select>


</mapper>
