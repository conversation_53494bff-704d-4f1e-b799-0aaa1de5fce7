package com.jeefast.cat.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 支付信息表 pay_info
 *
 * <AUTHOR>
 * @date 2020-09-28
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("pay_info")
public class PayInfoBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /** 订单id */
    private String orderId;
    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;
    /** 用户表id */
    @Excel(name = "用户表id")
    private String userId;
    /** 支付平台 */
    @Excel(name = "支付平台")
    private Integer payplatForm;
    /** 支付流水号 */
    @Excel(name = "支付流水号")
    private String platforMnumber;
    /** 支付金额 */
    @Excel(name = "支付金额")
    private Double money;
    /** 支付状态 */
    @Excel(name = "支付状态")
    private Integer platformStatus;
    /** 创建时间
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;*/
    /** 更新时间 */
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;
    /** 支付成功响应参数 */
    private String sucRemark;
}
