package com.jeefast.cat.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import cn.hutool.core.util.StrUtil;
import com.cat.modules.advert.entity.AdvertInfo;
import com.cat.modules.advert.service.IAdvertInfoService;
import com.jeefast.common.utils.file.FileUploadUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 广告信息Controller
 * 
 * <AUTHOR>
 * @date 2020-08-26
 */
@Controller
@RequestMapping("/cat/advert")
public class AdvertInfoController extends BaseController {
    private String prefix = "cat/advert";

    @Autowired
    private IAdvertInfoService advertInfoService;

    @RequiresPermissions("cat:advert:view")
    @GetMapping()
    public String advert() {
        return prefix + "/advert";
    }

    /**
     * 查询广告信息列表
     */
    @RequiresPermissions("cat:advert:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AdvertInfo advertInfo) {
        QueryWrapper<AdvertInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByAsc("sort_no");
    	// 需要根据页面查询条件进行组装
    	/*if(StringUtils.isNotEmpty(sysDemo.getLoginName())) {
    		queryWrapper.like("login_name", sysDemo.getLoginName());
    	} 
    	if(StringUtils.isNotEmpty(sysDemo.getUserName())) {
    		queryWrapper.like("user_name", sysDemo.getUserName());
    	}*/
		// 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        return getDataTable(advertInfoService.list(queryWrapper));
    }

    /**
     * 导出广告信息列表
     */
    @RequiresPermissions("cat:advert:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(AdvertInfo advertInfo) {
        List<AdvertInfo> list = advertInfoService.list(new QueryWrapper<>());
        ExcelUtil<AdvertInfo> util = new ExcelUtil<AdvertInfo>(AdvertInfo.class);
        return util.exportExcel(list, "advert");
    }

    /**
     * 新增广告信息
     */
    @GetMapping("/avatar")
    public String avatar() {
        return prefix + "/avatar";
    }

    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存广告信息
     */
    @RequiresPermissions("cat:advert:add")
    @Log(title = "广告信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(AdvertInfo advertInfo, @RequestParam("file") MultipartFile file) throws IOException {
        if(file!=null&& StrUtil.isNotEmpty(file.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(file);
            advertInfo.setFileUrl(cloudPath);
        }

        return toAjax(advertInfoService.save(advertInfo));
    }

    /**
     * 修改广告信息
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        AdvertInfo advertInfo = advertInfoService.getById(id);
        mmap.put("advertInfo", advertInfo);
        return prefix + "/edit";
    }

    /**
     * 修改保存广告信息
     */
    @RequiresPermissions("cat:advert:edit")
    @Log(title = "广告信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(AdvertInfo advertInfo, @RequestParam("file") MultipartFile file) throws IOException {
        if(file!=null&& StrUtil.isNotEmpty(file.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(file);
            advertInfo.setFileUrl(cloudPath);
        }
        return toAjax(advertInfoService.updateById(advertInfo));
    }

    /**
     * 删除广告信息
     */
    @RequiresPermissions("cat:advert:remove")
    @Log(title = "广告信息", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(advertInfoService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
