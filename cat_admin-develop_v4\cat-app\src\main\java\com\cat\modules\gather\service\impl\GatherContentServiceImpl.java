package com.cat.modules.gather.service.impl;

import com.cat.modules.gather.entity.GatherContent;
import com.cat.modules.gather.mapper.GatherContentMapper;
import com.cat.modules.gather.service.IGatherContentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import org.springframework.beans.factory.annotation.*;
import org.springframework.transaction.annotation.*;

import lombok.extern.slf4j.Slf4j;
import java.util.*;

@Slf4j
@Service
public class GatherContentServiceImpl extends ServiceImpl<GatherContentMapper, GatherContent> implements IGatherContentService {
    @Autowired
    private GatherContentMapper gatherContentMapper;


}
