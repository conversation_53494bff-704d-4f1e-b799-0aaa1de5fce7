<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<head>
    <th:block th:include="include :: header('新增动态内容')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: bootstrap-fileinput-css" />
    <link rel="stylesheet" href="/css/article/newAdd.css">
    <link rel="stylesheet" href="/js/plugins/vue/ElementUI/css/element.css">
    <link rel="stylesheet" href="/wangeditor/wangeditor.css" />
    <link rel="stylesheet" href="/cropper/cropper.min.css" />
    <script src="/wangeditor/wangeditor.js"></script>
    <script type="text/javascript" src="/js/plugins/vue/2.6.10/vue.min.js"></script>
    <script type="text/javascript" src="/js/plugins/vue/ElementUI/js/element.js"></script>
    <script src="/js/plugins/vue/Sortable.min.js"></script>
    <style type="text/css">
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body class="white-bg">
    <div v-loading="loading" id="container" class="tabs-container" v-cloak>
        <input type="hidden" th:value="${userId}" name="userId" />
        <div v-show="form.dynamicMediaList.length <= 0 && !dynamicId" class="tabs-box">
            <el-tabs v-model="form.type" @tab-click="handleTabClick">
                <el-tab-pane label="上传视频" :name="3"></el-tab-pane>
                <el-tab-pane label="上传图文" :name="2"></el-tab-pane>
            </el-tabs>
        </div>
        <!-- 图片/视频上传 -->
        <div v-show="form.dynamicMediaList.length <= 0 && !dynamicId" id="graphicVideo">
            <div v-if="record" class="recover-box">
                <div>
                    <span class="glyphicon glyphicon-info-sign" aria-hidden="true"></span>
                    <span>已为你保存最近一条编辑且未发布的{{ form.type == 2 ? '图文' : '视频' }}笔记，是否继续编辑</span>
                </div>
                <div>
                    <el-link type="primary" :underline="false" @click="continueEdit">继续编辑</el-link>
                    <el-link type="info" style="margin-left: 15px;" :underline="false" @click="discard">放弃</el-link>
                </div>
            </div>

            <div class="form-group p-15">
                <div class="upload-area">
                    <el-upload ref="upload" class="upload-area-box" :drag="true" :auto-upload="false"
                        :accept="form.type == 2 ? '.JPG,.JPEG,.PNG,.GIF,.BMP,.TIFF,.AI,.CDR,.EPS,.TIF,.HEIF,.jpg,.jpeg,.png,.gif,.bmp,.tiff,.ai,.cdr,.eps,.tif,.heif' : 'video/*'"
                        :multiple="form.type == 2" :show-file-list="false" :limit="form.type == 2 ? null : 1"
                        :before-upload="beforeUpload" :on-change="handleFileChange" :on-exceed="handleExceed">
                        <i class="el-icon-upload"></i>
                        <div class="el-upload-hint">拖拽{{ form.type == 2 ? '图片' : '视频'}}到此或点击上传</div>
                        <el-button type="primary">{{ form.type == 2 ? '上传图片' : '上传视频'}}</el-button>
                    </el-upload>
                </div>
            </div>
            <div v-if="form.type == 3" id="videoHint" class="hint-box m-15">
                <div class="tip">
                    <div>
                        <div class="tip-title">视频大小</div>
                        <div class="tip-content">支持时长60分钟以内，</div>
                        <div class="tip-content">最大20GB的视频文件</div>
                    </div>
                </div>
                <div class="tip">
                    <div>
                        <div class="tip-title">视频格式</div>
                        <div class="tip-content">支持常用视频格式，</div>
                        <div class="tip-content">推荐使用mp4、mov</div>
                    </div>
                </div>
                <div class="tip">
                    <div>
                        <div class="tip-title">视频分辨率</div>
                        <div class="tip-content">推荐上传720P（1280*720）及以上视频</div>
                        <div class="tip-content">超过1080P的视频用网页端上传画质更清晰</div>
                    </div>
                </div>
            </div>
            <div v-if="form.type == 2" id="imageHint" class="hint-box m-15">
                <div class="tip">
                    <div>
                        <div class="tip-title">图片大小</div>
                        <div class="tip-content">支持上传的图片大小，</div>
                        <div class="tip-content">最大20MB的图片文件</div>
                    </div>
                </div>
                <div class="tip">
                    <div>
                        <div class="tip-title">图片格式</div>
                        <div class="tip-content">支持上传的图片格式，</div>
                        <div class="tip-content">推荐使用png、jpeg、png，不支持gif、live、webp及其转化后的图片</div>
                    </div>
                </div>
                <div class="tip">
                    <div>
                        <div class="tip-title">图片分辨率</div>
                        <div class="tip-content">推荐上传宽高比为3:4、分辨率不低于720*960的照片</div>
                        <div class="tip-content">超过1080P的图片用网页端上传画质更清晰</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文章内容 -->
        <div v-show="form.dynamicMediaList.length > 0 || dynamicId" id="articleContent">
            <div class="form-content" :style="form.type == 4 ? 'width: 100%;':''">
                <div class="left-content">
                    <div class="tab-label">
                        <span v-if="fromUrl" class="el-icon-arrow-left reBack" @click="goBack"></span>
                        <span v-if="form.type == 1">发布文字</span>
                        <span v-if="form.type == 2">发布图文</span>
                        <span v-if="form.type == 3">发布视频</span>
                        <span v-if="form.type == 4">发布直播</span>
                    </div>
                    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                        <div v-if="form.type == 2" class="label-nol">
                            <span>图片编辑</span>
                            <span style="font-size: 12px;color: #929292;">（默认第一张图是封面）</span>
                        </div>
                        <!-- 图片列表 -->
                        <div v-show="form.type == 2" class="image-box">
                            <el-upload ref="inUpload" class="avatar-uploader" :auto-upload="false"
                                :accept="form.type == 2 ? 'image/*' : 'video/*'" :multiple="form.type == 2"
                                :show-file-list="false" :limit="form.type == 2 ? null : 1" :before-upload="beforeUpload"
                                :on-change="handleFileChange" :disabled="isView">
                                <div class="avatar-uploader-icon flex flex-column flex-center">
                                    <i class="el-icon-plus"></i>
                                    <span>添加</span>
                                </div>
                            </el-upload>
                            <div id="itxst" class="image-edit-box col-sm-8">
                                <div class="image-box-item" v-for="(item, index) in form.dynamicMediaList"
                                    :data-id="index" :key="item.mediaUrl">
                                    <el-image style="width: 100%; height: 100%" :src="item.mediaUrl"
                                        :preview-src-list="[item.mediaUrl]" fit="cover"></el-image>
                                    <div class="image-wrapper">
                                        <div class="btn-delete" @click="delImage(index)">
                                            <i class="fa fa-times"></i>
                                        </div>
                                        <div class="btn-group">
                                            <div class="btn-edit" @click="handleEditImg(item, index)">编辑</div>
                                            <div class="line"></div>
                                            <el-upload class="btn-reupload" :auto-upload="false"
                                                :accept="form.type == 2 ? 'image/*' : 'video/*'"
                                                :multiple="form.type == 2" :file-list="form.dynamicMediaList"
                                                :show-file-list="false" :limit="form.type == 2 ? null : 1"
                                                :before-upload="beforeUpload" :on-change="replaceUpload"
                                                :disabled="isView">
                                                <div @click="replaceImg(item, index)">替换</div>
                                            </el-upload>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- 视频列表 -->
                        <div class="videoBox" v-if="form.type == 3 && form.dynamicMediaList.length">
                            <div class="videoUpload">
                                <div class="videoUpload-left">
                                    <div class="videoUpload-left-name">{{ form.dynamicMediaList[0].ext.name }}</div>
                                    <div class="videoUpload-left-operator">
                                        <div class="videoSuccess">
                                            <i class="el-icon-success"></i>
                                            <span>上传成功</span>
                                        </div>
                                        <div class="videoInfo">视频大小：{{ formatSize(form.dynamicMediaList[0].ext.size) }}
                                        </div>
                                        <div class="videoInfo">视频时长：{{ formatTime(duration) }}</div>
                                    </div>
                                </div>
                                <el-upload class="btn-reupload" :auto-upload="false" accept="video/*" :multiple="false"
                                    :show-file-list="false" :limit="1" :before-upload="beforeUpload"
                                    :on-change="replaceUpload" :disabled="isView">
                                    <div class="videoUpload-right" @click="replaceImg(null, 0)">替换视频</div>
                                </el-upload>
                            </div>

                            <div v-if="form.type == 3" class="label-nol" style="padding-left: 0;">
                                <span>封面设置</span>
                                <span style="font-size: 12px;color: #929292;">（默认截取第一帧为封面）</span>
                            </div>

                            <div class="cover-preview" @click="setCoverImage">
                                <img v-if="form.coverImage" class="preview" :src="form.coverImage" alt="">
                                <div class="cover-plus">
                                    <i class="el-icon-picture"></i>
                                    <span>设置封面</span>
                                </div>
                            </div>
                        </div>

                        <div class="label-nol">正文内容</div>

                        <el-form-item label="" label-width="0px">
                            <el-input v-model="form.title" placeholder="添加标题" maxlength="20"></el-input>
                        </el-form-item>
                        <el-form-item label="" label-width="0px">
                            <div id="editor—wrapper">
                                <div id="toolbar-container"><!-- 工具栏 --></div>
                                <div id="editor-container"><!-- 编辑器 --></div>
                            </div>
                            <div class="word-counter"
                                :style="{color: contentLength > maxContentLength ? '#f56c6c' : '#909399'}">
                                {{ contentLength }}/{{ maxContentLength }}字
                            </div>
                            <div class="row">
                                <div class="contentEditBox flex">
                                    <el-popover placement="top-end" width="386" trigger="click" v-model="emojiVisible">
                                        <div class="emojiBox">
                                            <div class="emojiItem" v-for="(emoji, index) in emojiList" :key="index"
                                                @click="selectEmoji(emoji)">
                                                {{ emoji }}
                                            </div>
                                        </div>
                                        <div class="contentBtn" slot="reference">
                                            <img src="/img/smile.png" alt="">
                                            表情
                                        </div>
                                    </el-popover>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="话题">
                            <div class="topic-input-box" @click="topicInputClick">
                                <div class="topic-item" v-for="(item,index) in topicInfoList" :key="index">
                                    <span>{{item.topicName}}</span>
                                    <i class="el-tag__close el-icon-close topic-close"
                                        @click.stop="removeTopic(index)"></i>
                                </div>
                                <div v-if="topicInfoList.length <= 0" class="topic-placeholder">请选择</div>
                            </div>
                        </el-form-item>
                        <el-form-item label="业务场景" prop="articleType">
                            <el-select v-model="form.articleType" placeholder="请选择" style="width: 100%;">
                                <el-option :label="item.dictLabel" :value="item.dictValue"
                                    v-for="(item,index) in articleTypeDict" :key="index"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="添加链接">
                            <div class="flex">
                                <el-input :placeholder="form.thirdType == 2 ? '输入链接/page' : '输入链接'"
                                    v-model="form.thirdUrl" class="input-with-select" style="width: 100%;">
                                    <el-select v-model="form.thirdType" slot="prepend" placeholder="请选择"
                                        style="width: 120px;">
                                        <el-option label="商品" value="1"></el-option>
                                        <el-option label="店铺" value="2"></el-option>
                                        <el-option label="群聊" value="3"></el-option>
                                    </el-select>
                                </el-input>
                            </div>
                        </el-form-item>

                        <div class="label-nol">发布设置</div>

                        <el-form-item label="文章分类">
                            <el-select v-model="form.dynamicCategoryId" placeholder="请选择分类" style="width: 100%;">
                                <el-option :label="item.title" :value="item.id" v-for="(item,index) in categoryList"
                                    :key="index"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="用户名称" prop="userId">
                            <el-select ref="userSelect" v-model="form.userId" filterable remote reserve-keyword
                                placeholder="搜索/选择用户名称" :remote-method="remoteMethod" :loading="selectLoading"
                                @change="selectUser" style="width: 100%;">
                                <div class="infinite-list" v-infinite-scroll="userLoad"
                                    :infinite-scroll-immediate="false" :infinite-scroll-delay="800">
                                    <el-option v-for="item in userOptions" :key="item.userId" :label="item.userName"
                                        :value="item.userId">
                                    </el-option>
                                </div>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="谁可以看">
                            <el-radio-group v-model="form.isPublic">
                                <el-radio-button label="1">公开</el-radio-button>
                                <el-radio-button label="3">仅自己可见</el-radio-button>
                                <el-radio-button label="4">仅粉丝可见</el-radio-button>
                                <el-radio-button label="2">仅家庭成员可见</el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="发布时间" :prop="form.releaseType == 0 ? 'releaseTime':''">
                            <el-radio v-model="form.releaseType" label="1">立即发布</el-radio>
                            <el-radio v-model="form.releaseType" label="0">定时发布</el-radio>
                            <el-date-picker v-if="form.releaseType == 0" v-model="form.releaseTime" type="datetime"
                                :picker-options="pickerBeginDateBefore" value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="选择日期时间">
                            </el-date-picker>
                        </el-form-item>
                        <div v-if="!isAdmin" class="flex">
                            <el-form-item label="是否推广">
                                <el-radio v-model="form.beExtend" label="0">否</el-radio>
                                <el-radio v-model="form.beExtend" label="1">是</el-radio>
                            </el-form-item>
                            <el-form-item v-if="form.beExtend == 1" label="推广时间" style="margin-left: 30px;"
                                prop="extendTime">
                                <el-date-picker v-model="form.extendTime" :picker-options="pickerBeginDateBefore"
                                    value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']"
                                    type="datetimerange" range-separator="至" start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <el-form-item v-if="isShowTop" label="是否置顶">
                            <el-radio v-model="form.isTop" label="0">否</el-radio>
                            <el-radio v-model="form.isTop" label="1">是</el-radio>
                        </el-form-item>

                        <div class="label-nol" style="cursor: pointer;" @click="otherShow = !otherShow">
                            <span>其他</span>
                            <i :class="otherShow ? 'el-icon-arrow-up':'el-icon-arrow-down'"></i>
                        </div>

                        <div v-show="otherShow" class="other-box">
                            <el-form-item label="所在位置">
                                <el-input v-model="form.addr"></el-input>
                            </el-form-item>

                            <div class="flex">
                                <el-form-item label="经度" style="width: 50%;">
                                    <el-input v-model="form.longitude" type="number"></el-input>
                                </el-form-item>
                                <el-form-item label="纬度" style="width: 50%;">
                                    <el-input v-model="form.latitude" type="number"></el-input>
                                </el-form-item>
                            </div>

                            <div class="label-nol">互动信息</div>

                            <div class="flex">
                                <el-form-item label="点赞数" style="width: 50%;">
                                    <el-input v-model="form.praiseCount" type="number"
                                        @input="val => handleNumberInput('praiseCount', val)"></el-input>
                                </el-form-item>
                                <el-form-item label="评论数" style="width: 50%;">
                                    <el-input v-model="form.commentCount" type="number"
                                        @input="val => handleNumberInput('commentCount', val)"></el-input>
                                </el-form-item>
                            </div>
                            <div class="flex">
                                <el-form-item label="权重" style="width: 50%;">
                                    <el-input v-model="form.weight" type="number"
                                        @input="val => handleNumberInput('weight', val)"></el-input>
                                </el-form-item>
                                <el-form-item label="权重系数" style="width: 50%;">
                                    <el-input v-model="form.scale" type="number"
                                        @input="val => handleNumberInput('scale', val)"></el-input>
                                </el-form-item>
                            </div>
                        </div>

                    </el-form>
                </div>
            </div>

            <div v-if="form.type != 4" class="rightPreviewArea">
                <!--图文移动端预览显示效果-->
                <div v-if="form.type == 2 || form.type == 1" class="moblie-content">
                    <div class="moblie-content-top">
                        <!--状态栏-->
                        <div class="moblie-content-status-bar flex flex-between">
                            <div class="status-bar-time">00:00</div>
                            <div class="status-bar-right flex">
                                <img src="/img/signal.png" alt="">
                                <img src="/img/wifi.png" style="height: 13px;margin-top: 2px;">
                                <img src="/img/electric-quantity.png" alt="">
                            </div>
                        </div>
                        <!--头部-->
                        <div class="moblie-content-header">
                            <div class="moblie-content-header-left">
                                <img src="/img/back-icon.png" class="img-back">
                                <img src="/img/profile.jpg" class="img-circle">
                                <span>会仙术的兔子</span>
                                <div class="followbtn">+ 关注</div>
                            </div>
                            <div class="moblie-content-header-right">
                                <i class="el-icon-more"></i>
                            </div>
                        </div>
                    </div>

                    <!--内容区-->
                    <div class="content-area">
                        <!-- 图片区 -->
                        <div v-if="form.type == 2" class="image-area">
                            <el-carousel :initial-index="initialIndex" :autoplay="false" indicator-position="none"
                                @change="carouselChange">
                                <el-carousel-item v-for="(item, index) in form.dynamicMediaList" :key="index">
                                    <el-image :src="item.mediaUrl" :preview-src-list="[item.mediaUrl]"
                                        :initial-index="index" fit="contain"
                                        :style="{width: '100%', height: '100%'}"></el-image>
                                </el-carousel-item>
                            </el-carousel>
                            <div class="initial-box">{{ initialIndex + 1}}/ {{ form.dynamicMediaList.length }}</div>
                        </div>
                        <!--标题-->
                        <div class="content-area-title">{{ form.title }}</div>
                        <div class="content-area-deta">2025/04/22 12:00</div>
                        <!-- 富文本区 -->
                        <div class="rich-text" v-html="form.content"></div>
                        <!-- 店铺/商品区 -->
                        <div v-if="form.thirdUrl" class="shop-goods-area flex">
                            <div class="shop-goods-area-left">
                                <img v-if="form.thirdType == 1" src="/img/product.png" alt="">
                                <img v-if="form.thirdType == 2" src="/img/shop.png" alt="">
                                <img v-if="form.thirdType == 3" src="/img/chat.png" alt="">
                            </div>
                            <div class="shop-goods-area-right">
                                <div v-if="form.thirdType == 1" class="shop-goods-area-right-title">同款商品</div>
                                <div v-if="form.thirdType == 2" class="shop-goods-area-right-title">店铺咨询</div>
                                <div v-if="form.thirdType == 3" class="shop-goods-area-right-title">加入群聊</div>
                            </div>
                        </div>
                    </div>

                    <div class="foot-bar flex flex-between">
                        <div class="foot-bar-left flex">
                            <div class="foot-bar-left-btn flex flex-center">
                                <img src="/img/view-icon.png" alt="">
                                <span>1.7w</span>
                            </div>
                            <div class="foot-bar-left-btn flex flex-center">
                                <img src="/img/like-icon.png" alt="">
                                <span>490</span>
                            </div>
                        </div>
                        <div class="foot-bar-left flex">
                            <div class="foot-bar-left-btn flex flex-center">
                                <img src="/img/collect-icon.png" alt="">
                                <span>20</span>
                            </div>
                            <div class="foot-bar-left-btn flex flex-center">
                                <img src="/img/share-icon.png" alt="">
                                <span>分享</span>
                            </div>
                            <div class="foot-bar-left-btn flex flex-center">
                                <img src="/img/more-icon.png" alt="">
                            </div>
                        </div>
                    </div>
                    <div class="gap"></div>

                    <!--评论区-->
                    <div class="review-area">
                        <div class="review-area-top">
                            <span class="review-area-top-left">留言</span>
                            <span class="review-area-top-line"></span>
                            <span class="review-area-top-right">总共{{ reviewList.length }}条</span>
                        </div>
                        <div class="review-list" v-for="(item,index) in reviewList" :key="index">
                            <div class="review-list-left">
                                <img :src="item.avatar" />
                            </div>
                            <div class="review-list-middle">
                                <div class="review-userName">{{item.userName}}</div>
                                <div class="review-content">{{item.content}}</div>
                                <div class="review-time">{{item.time}}</div>
                            </div>
                            <div class="review-list-right">
                                <img src="/img/like-icon.png" />
                                <span>490</span>
                            </div>
                        </div>
                    </div>

                    <!-- 输入框 -->
                    <div class="comment-box flex flex-between">
                        <div class="comment-input">
                            <img src="/img/profile.jpg" alt="">
                            <span>给他留言，鼓励一下</span>
                        </div>
                    </div>
                </div>

                <!--视频移动端预览显示效果-->
                <div v-if="form.type == 3 && form.dynamicMediaList.length" class="moblie-video-content">
                    <div class="moblie-video-content-top">
                        <!--状态栏-->
                        <div class="moblie-content-status-bar flex flex-between" style="padding: 13px 18px 0 20px;">
                            <div class="status-bar-time" style="color: #fff;">00:00</div>
                            <div class="status-bar-right flex">
                                <img src="/img/signal-white.png" alt="">
                                <img src="/img/wifi-white.png" style="height: 13px;margin-top: 2px;">
                                <img src="/img/electric-quantity-white.png" alt="">
                            </div>
                        </div>
                        <div class="moblie-video-content-header">
                            <div class="moblie-video-content-header-left">
                                <i class="el-icon-arrow-left"></i>
                            </div>
                            <div class="moblie-video-content-header-right">
                            </div>
                        </div>
                    </div>
                    <div class="moblie-video-content-body">
                        <video ref="videoPlay" controls preload="metadata" @error="handleVideoError"
                            @loadeddata="handleVideoLoaded" style="width: 100%; height: 100%;"
                            controlsList="nodownload noplaybackrate" disablepictureinpicture @pause="handleVideoPause"
                            @play="handleVideoPlay">
                            <source :src="form.dynamicMediaList[0].mediaUrl" type="video/mp4">
                            <source :src="form.dynamicMediaList[0].mediaUrl" type="video/webm">
                            您的浏览器不支持 video 标签
                        </video>
                        <div v-if="!playing" class="playBox">
                            <img class="video-stop-play" src="/img/play_stop.png" @click.prevent="handleVideoClick" />
                        </div>
                    </div>
                    <div class="moblie-video-content-footer">
                        <!-- 商品/店铺 -->
                        <div v-if="form.thirdUrl" class="shop-goods-card flex">
                            <img v-if="form.thirdType == 1" class="shop-goods-card-left" src="/img/product.png" alt="">
                            <img v-if="form.thirdType == 2" class="shop-goods-card-left" src="/img/shop.png" alt="">
                            <img v-if="form.thirdType == 3" class="shop-goods-card-left" src="/img/chat.png" alt="">
                            <div class="shop-goods-card-right">
                                <div v-if="form.thirdType == 1" class="shop-goods-area-right-title">同款商品</div>
                                <div v-if="form.thirdType == 2" class="shop-goods-area-right-title">店铺咨询</div>
                                <div v-if="form.thirdType == 3" class="shop-goods-area-right-title">加入群聊</div>
                            </div>
                        </div>
                        <!-- 用户信息 -->
                        <div class="user-info">
                            <img src="/img/profile.jpg" alt="">
                            <div class="user-info-box">
                                <div class="user-info-name">会仙术的兔子</div>
                                <div class="user-info-date">2024/01/12</div>
                            </div>
                            <div class="user-info-follow flex flex-center">+ 关注</div>
                        </div>
                        <div v-if="form.title" class="moblie-video-content-title">
                            <div class="moblie-video-content-title-left">{{ form.title.length > 10 ?
                                form.title.substring(0, 10) + '...' : form.title }}</div>
                            <!--                            <div v-if="form.title.length > 10" class="moblie-video-content-title-right">展开</div>-->
                        </div>
                        <div v-html="filteredContent" class="rich-text moblie-video-content-content"></div>
                        <div class="recommended flex flex-center">@yogax 推荐</div>
                    </div>
                    <!--视频右侧按钮-->
                    <div class="moblie-video-content-right-bar">
                        <div class="bar-list">
                            <img class="bar-img" src="/img/icon-like1.png" />
                            <div class="bar-text">489.4w</div>
                        </div>
                        <div class="bar-list">
                            <img class="bar-img" src="/img/icon-comment.png" />
                            <div class="bar-text">写评论</div>
                        </div>
                        <div class="bar-list">
                            <img class="bar-img" src="/img/icon-collect1.png" />
                            <div class="bar-text">收藏</div>
                        </div>
                        <div class="bar-list">
                            <img class="bar-img" src="/img/icon-share.png" />
                            <div class="bar-text">分享</div>
                        </div>
                        <div class="bar-list">
                            <img class="bar-img" src="/img/icon-more.png" />
                        </div>
                        <div class="bar-list">
                            <img class="bar-img" src="/img/icon-mute1.png" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="!isView && (form.dynamicMediaList.length > 0 || dynamicId)" class="bottom-btn-box">
            <el-button :disabled="loading" @click="submit(0)">保存草稿</el-button>
            <el-button :disabled="loading" type="primary" @click="submit(1)">立即发布</el-button>
        </div>

        <!-- 图片裁剪弹窗 -->
        <el-dialog title="图片编辑" :visible.sync="cropperVisible" width="950px">
            <div class="cropper-box">
                <div class="cropper-area">
                    <img ref="cropperImageRef" id="cropperImage" :src="cropperImg" alt="Picture">
                </div>
                <div class="cropper-operation">
                    <div class="cropper-operation-title">比例选择</div>
                    <div class="select-area">
                        <div class="option" @click="setAspectRatio('original')">
                            <div :class="['graph', activeRatio === 'original' ? 'graphActive' : '']">原始</div>
                        </div>
                        <div class="option" @click="setAspectRatio(1)">
                            <div :class="['graph', activeRatio === 1 ? 'graphActive' : '']"
                                style="aspect-ratio: 1 / 1;">1:1</div>
                        </div>
                        <div class="option" @click="setAspectRatio(0.75)">
                            <div :class="['graph', activeRatio === 0.75 ? 'graphActive' : '']"
                                style="aspect-ratio: 0.75 / 1;">3:4</div>
                        </div>
                        <div class="option" @click="setAspectRatio(1.33333)">
                            <div :class="['graph', activeRatio === 1.33333 ? 'graphActive' : '']"
                                style="aspect-ratio: 1.33333 / 1;">4:3</div>
                        </div>
                    </div>
                </div>
            </div>

            <span slot="footer" class="dialog-footer">
                <el-button @click="cropperVisible = false">取 消</el-button>
                <el-button :loading="cropLoading" type="primary" @click="confirmCropper">确 定</el-button>
            </span>
        </el-dialog>


        <!-- 图片裁剪弹窗 -->
        <el-dialog v-if="coverImageVisible" title="设置封面" top="5vh" :visible.sync="coverImageVisible" width="620px"
            v-loading="loading">
            <el-tabs v-loading="thumbnailLoading" element-loading-text="视频正在抽帧中，请稍后" v-model="activeCoverName">
                <div v-if="coverImg" class="cropper-area2">
                    <img ref="cropperImageRef" id="cropperImage" :src="coverImg" alt="Picture">
                </div>
                <el-tab-pane label="截取封面" name="first">
                    <div class="cover-container">
                        <div class="timeline-container">
                            <div ref="thumbnailBox" class="thumbnail-box" @mousedown="handleThumbMouseDown">
                                <div v-for="(thumb, index) in thumbnails" :key="index" class="thumbnail-item">
                                    <img :src="thumb.dataURL" alt="thumbnail">
                                </div>
                                <div class="point" :style="{ left: pointLeft + 'px' }" @mousedown.prevent="startDrag">
                                    <span class="span1"></span>
                                    <span class="span2"></span>
                                    <div class="time">{{ formatTime(currentTime) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="上传封面" name="second">
                    <el-upload v-if="!coverImg" ref="upload" class="upload-area-box" drag :auto-upload="false"
                        accept=".JPG,.JPEG,.PNG,.GIF,.BMP,.TIFF,.AI,.CDR,.EPS,.TIF,.HEIF,.jpg,.jpeg,.png,.gif,.bmp,.tiff,.ai,.cdr,.eps,.tif,.heif"
                        :multiple="false" :show-file-list="false" :limit="1" :before-upload="beforeUpload"
                        :on-change="uploadCover">
                        <i class="el-icon-upload"></i>
                        <div class="el-upload-hint">拖拽图片到此或点击上传</div>
                        <el-button style="background: #FF2840" type="danger">上传封面</el-button>
                    </el-upload>
                    <el-upload v-if="coverImg" ref="reupload" class="btn-reupload-upload" :auto-upload="false"
                        accept=".JPG,.JPEG,.PNG,.GIF,.BMP,.TIFF,.AI,.CDR,.EPS,.TIF,.HEIF,.jpg,.jpeg,.png,.gif,.bmp,.tiff,.ai,.cdr,.eps,.tif,.heif"
                        :multiple="false" :show-file-list="false" :limit="1" :before-upload="beforeUpload"
                        :on-change="uploadCover">
                        <div class="btn-reupload-text">重新上传</div>
                    </el-upload>
                </el-tab-pane>
            </el-tabs>
            <div class="crop-ratio-list-container">
                <div class="crop-title">裁剪比例</div>
                <div class="crop-ratio-list">
                    <div :class="['crop-ratio-item', activeCropRatio === 'original' ? 'graphActive' : '']"
                        style="width: 40px; height: 52px;" @click="setCropRatio('original')">原始</div>
                    <div :class="['crop-ratio-item', activeCropRatio === 1 ? 'graphActive' : '']"
                        style="width: 52px; height: 52px;" @click="setCropRatio(1)">1:1</div>
                    <div :class="['crop-ratio-item', activeCropRatio === 0.75 ? 'graphActive' : '']"
                        style="width: 40px; height: 52px;" @click="setCropRatio(0.75)">3:4</div>
                    <div :class="['crop-ratio-item', activeCropRatio === 1.33333 ? 'graphActive' : '']"
                        style="width: 52px; height: 40px;" @click="setCropRatio(1.33333)">4:3</div>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="coverImageVisible = false">取 消</el-button>
                <el-button :loading="coverLoading" type="primary" @click="confirmCover">确 定</el-button>
            </span>
        </el-dialog>

        <el-dialog v-if="topicVisible" title="选择话题" top="5vh" :visible.sync="topicVisible" width="620px">
            <div v-loading="topicLoading" class="topic-container">
                <div class="topic-container-left">
                    <div class="topic-left-item" :class="topicIndex == index ? 'topic-lfet-active' : ''"
                        v-for="(item,index) in topicList" :key="index" @click="getTopicLeft(item, index)">
                        {{item.name}}
                    </div>
                </div>
                <div class="topic-container-right">
                    <div class="topic-left-item" :class="form.topic.includes(item.topicId) ? 'topic-lfet-active' : ''"
                        v-for="(item,index) in topicList[topicIndex].children || []" :key="index"
                        @click="topicChange(item)">
                        <span>{{ item.topicName }}</span>
                    </div>
                    <div v-if="!topicList[topicIndex].children || !topicList[topicIndex].children.length"
                        class="topic-empty">暂无话题</div>
                </div>
            </div>
        </el-dialog>

    </div>


    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: bootstrap-fileinput-js" />
    <script th:src="@{/js/jquery-ui-1.10.4.min.js}"></script>
    <script th:inline="javascript">
        var dynamicSourceData = [[${@dict.getType('cat_dynamic_source') }]];
        var articleTypeData = [[${@dict.getType('article_type') }]];
        var catDynamicSecurityNoNeed = [[${@dict.getType('cat_dynamic_security_no_need') }]];
        var roles = [[${ roles }]]
    </script>
    <script type="module">
        import Cropper from '/cropper/cropper.min.js';
        window.Cropper = Cropper;  // 挂载到全局
    </script>

    <script type="text/javascript">
        var prefix = ctx + "cat/dynamicBackstage"
        // 动态分类
        var catApi = ctx + "cat/category/list";
        var editor = null;

        var app = new Vue({
            el: '#container',
            data: {
                roles: [], // 用户角色
                isAdmin: false, // 是否为超级管理员
                isShowTop: false, // 是否显示置顶
                loading: false,
                replaceIndex: null,
                action: '/common/uploadMany',
                headers: '',
                fileList: [],
                sortable: null,
                record: null,
                emojiVisible: false,
                emojiList: [
                    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
                    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
                    '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
                    '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
                    '❤️', '🧡', '💛', '💚', '💙', '💜', '🤎', '🖤', '🤍', '💔',
                    '💕', '💖', '💗', '💘', '💝', '💞',
                ],
                contentLength: 0,      // 当前字数
                maxContentLength: 1000, // 最大允许字数
                // 是否展示其他
                otherShow: false,
                pickerBeginDateBefore: {
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    },
                },
                fromUrl: '', // 上一页路径
                dynamicId: '',
                topicInfoList: [], // 话题
                form: {
                    userId: '', // 用户ID
                    userName: '', // 用户名
                    title: '', // 标题
                    content: '', // 内容
                    topic: [], // 话题
                    addr: '', // 所在位置
                    longitude: '', // 经度
                    latitude: '', // 纬度
                    praiseCount: '', // 点赞数
                    commentCount: '', // 评论数
                    type: 3, // 动态类型 1：文字、2：图文、3：视频
                    articleType: '', // 文章类型 - 业务场景
                    coverImage: '', // 封面图
                    dynamicMediaList: [], // 动态媒体列表
                    weight: '', // 权重
                    scale: '', // 权重系数
                    thirdType: '', // 链接类型 1 商品 2店铺 3群聊
                    thirdUrl: '', // 链接地址
                    releaseType: '1', // 发布类型 1：立即发布，0：定时发布
                    releaseTime: '', // 定时发布时间
                    beExtend: '0', // 是否推广 0：否，1：是
                    extendTime: [], // 推广时间范围
                    extendBeginTime: '', // 推广开始时间
                    extendEndTime: '', // 推广结束时间
                    isCheck: '0', // 是否草稿 0：是草稿，1：不是草稿
                    isTop: '0', // 是否置顶 0：否，1：是
                    isPublic: '1', // 谁可以看 1: 公开，2：家庭成员，3：仅自己，4：粉丝
                },
                rules: {
                    title: [
                        { required: true, message: '请输入标题', trigger: 'blur' },
                    ],
                    content: [
                        { required: true, message: '请输入内容', trigger: 'blur' },
                    ],
                    articleType: [
                        { required: true, message: '请选择业务场景', trigger: 'change' },
                    ],
                    userId: [
                        { required: true, message: '请选择用户', trigger: 'change' },
                    ],
                    releaseTime: [
                        { required: true, message: '请选择定时发布时间', trigger: 'change' },
                    ],
                    extendTime: [
                        { required: true, message: '请选择推广时间', trigger: 'change' },
                    ],
                },
                catDynamicSecurityNoNeed: [], // 动态内容免审核角色字典
                articleTypeDict: [], // 文章类型
                dynamicSource: [], // 分类字典
                categoryList: [], // 动态分类列表
                userPageNum: 1, // 用户列表加载页码
                userQuery: '',
                loadmore: 'loadmore', // 加载状态
                userOptions: [], // 用户列表
                selectLoading: false,

                drag: false,
                initialIndex: 0,
                playing: false,
                dragStartIndex: null,  // 新增拖拽起始位置
                reviewList: [
                    {
                        avatar: '/img/profile.jpg',
                        userName: '葫芦娃大战哥斯拉',
                        content: '厉害',
                        time: '2025-02-10 13:43 福建',
                    },
                    {
                        avatar: '/img/profile.jpg',
                        userName: '葫芦娃大战哥斯拉2',
                        content: '厉害',
                        time: '2025-02-10 13:43 福建',
                    },
                ],
                topicVisible: false,
                topicLoading: false,
                topicList: [],
                topicIndex: 0,

                // 裁剪弹窗配置
                cropperVisible: false,
                cropperImg: null,
                cropper: null,
                activeRatio: 'graphActive',
                cropLoading: false,
                currentCropIndex: null,

                // 设置封面弹窗
                coverImageVisible: false,
                coverLoading: false,
                activeCoverName: 'first',
                activeCropRatio: 'original',
                currentTime: 0,
                duration: 0,
                frameLoading: false,
                thumbnailLoading: false,
                isDragging: false,
                pointLeft: 0,
                thumbnailBoxWidth: 0, // thumbnail-box的宽度
                coverImg: '', // 封面图片
                thumbnails: [], // 缩略图数组

                isView: false, //禁用状态
            },

            watch: {
                'form.dynamicMediaList': {
                    handler(val) {
                        if (val !== undefined && val.length <= 0 && this.$refs.upload) {
                            this.$refs.upload.clearFiles();
                        }
                    },
                    deep: true,
                    immediate: true,
                },
                currentTime(newVal) {
                    this.pointLeft = this.calculatePosition(newVal);
                },
            },

            computed: {
                filteredContent() {
                    // 1. 过滤图片标签
                    if (!this.form.content) return
                    let filtered = this.form.content.replace(/<img[^>]+>/gi, (match) => {
                        const src = match.match(/src=['"]([^'"]+)['"]/)?.[1] || '#'
                        return `<a href="${src}" target="_blank">[图片]</a>`
                    })

                    // 2. 过滤视频标签（包括嵌套的source标签）
                    filtered = filtered
                        .replace(/<source[^>]+>/gi, '') // 先移除source标签
                        .replace(/<video[\s\S]*?<\/video>/gi, (match) => {
                            const src = match.match(/src=['"]([^'"]+)['"]/)?.[1] || '#'
                            return `<a href="${src}" target="_blank">[视频]</a>`
                        })

                    // 3. 过滤iframe等其他媒体（可选）
                    filtered = filtered.replace(/<iframe[\s\S]*?<\/iframe>/gi, '[外部内容]')

                    return filtered
                },
            },

            async created() {
                const urlParams = new URLSearchParams(window.location.search);
                const dynamicId = urlParams.get('dynamicId');
                const fromUrl = urlParams.get('from');
                this.fromUrl = fromUrl;
                this.dynamicId = dynamicId;
                if (this.dynamicId) {
                    // 获取详情
                    await this.getDynamicDetail();
                }
                const isView = urlParams.get('isView');
                if (isView) {
                    this.isView = true;
                }
                this.dynamicSource = dynamicSourceData;
                this.articleTypeDict = articleTypeData;
                // 获取草稿箱记录
                this.getDraftData();
            },
            mounted() {
                this.roles = roles;
                this.checkRole();
                this.initWangEditor();
                this.loadCategoryData();
                this.getTopicList();
                // 检查是否需要自动打开封面设置
                this.checkAutoOpenCoverSetting();
            },
            beforeDestroy() {
                if (editor) {
                    editor.destroy();
                    editor = null;
                }
            },

            methods: {
                // 检查是否需要自动打开封面设置
                checkAutoOpenCoverSetting() {
                    // 延迟检查，确保页面完全加载
                    this.$nextTick(() => {
                        setTimeout(() => {
                            // 如果是视频类型且有视频文件，自动打开封面设置
                            if (this.form.type === 3 && this.form.dynamicMediaList.length > 0) {
                                this.setCoverImage();
                            }
                        }, 500); // 延迟500ms确保所有初始化完成
                    });
                },

                // 返回
                goBack() {
                    if (!this.fromUrl) return;
                    $.modal.openTab("", this.fromUrl);
                    this.switchMenu(this.fromUrl);
                },

                // 获取草稿箱内最新一条数据
                getDraftData() {
                    let that = this;
                    this.record = null;
                    let form = {
                        isCheck: 0,
                        orderByColumn: 'createDate',
                        isAsc: 'desc',
                        pageSize: 1,
                        pageNum: 1,
                        needQueryTop: 0,
                        sysUserId: $('input[name="userId"]').val() || '',
                        type: this.form.type,
                    }
                    let formData = new FormData();
                    Object.keys(form).forEach(key => {
                        formData.append(key, form[key]);
                    });
                    $.ajax({
                        url: prefix + "/list",
                        data: formData,
                        type: "post",
                        dataType: "json",
                        contentType: false,
                        processData: false,
                        success: function (result) {
                            if (result.code == 0) {
                                that.record = result.rows[0];
                            }
                        },
                    })
                },

                // 继续编辑
                async continueEdit() {
                    this.dynamicId = this.record.dynamicId
                    await this.getDynamicDetail();
                },

                // 放弃
                discard() {
                    this.$confirm('确定要放弃编辑吗？', '提示', {
                        type: 'warning',
                    }).then(() => {
                        this.record = null;
                    }).catch(() => { });
                },

                // 初始化拖拽图片方法
                initDrag() {
                    const that = this;
                    //初始化拖拽图片配置
                    var el = document.getElementById('itxst');

                    // 先销毁旧实例
                    if (this.sortable) {
                        this.sortable.destroy();
                    }

                    this.sortable = Sortable.create(el, {
                        animation: 300,
                        draggable: '.image-box-item',
                        ghostClass: "sortable-ghost",
                        chosenClass: "sortable-chosen",
                        dragClass: "sortable-drag",
                        onEnd: function (evt) {
                            const tempArray = [...that.form.dynamicMediaList];
                            const movedItem = tempArray.splice(evt.oldIndex, 1)[0];
                            tempArray.splice(evt.newIndex, 0, movedItem);
                            that.form.dynamicMediaList = tempArray;
                        }
                    });
                },

                // 校验角色
                checkRole() {
                    this.roles.forEach(role => {
                        if (role.roleKey == "admin") {
                            this.isAdmin = true;
                        }
                    })
                    this.catDynamicSecurityNoNeed = catDynamicSecurityNoNeed;
                    if (this.catDynamicSecurityNoNeed.length && this.roles.length) {
                        this.catDynamicSecurityNoNeed.forEach(item => {
                            this.roles.forEach(role => {
                                if (role.roleKey == item.dictValue) {
                                    this.isShowTop = true;
                                }
                            })
                        })
                    }
                },

                // 获取动态内容
                getDynamicDetail() {
                    const that = this;
                    that.loading = true;
                    $.ajax({
                        url: prefix + "/new/get/" + this.dynamicId,
                        type: "get",
                        dataType: "json",
                        success: function (result) {
                            that.loading = false;
                            if (result.code == 0) {
                                that.form = {
                                    dynamicId: that.dynamicId,
                                    ...result.data.dynamicInfoBackstage,
                                    dynamicMediaList: result.data.dynamicMediaList.map(item => {
                                        try {
                                            item.ext = typeof item.ext === 'string' ? JSON.parse(item.ext) : (item.ext || {});
                                        } catch {
                                            item.ext = {};
                                        }
                                        return item;
                                    }),
                                    topic: result.data.topicInfoList.map(item => item.topicId),
                                    type: Number(result.data.dynamicInfoBackstage.type),
                                    extendTime: [],
                                }
                                that.topicInfoList = result.data.topicInfoList;
                                that.initDrag()
                                that.initWangEditor();
                                if (that.form.extendBeginTime && that.form.extendEndTime) {
                                    that.form.extendTime = [
                                        that.form.extendBeginTime,
                                        that.form.extendEndTime
                                    ]
                                }
                                if (that.form.userId) {
                                    that.userOptions = [{ userId: that.form.userId, userName: that.form.userName }]
                                }
                                if (editor) {
                                    editor.setHtml(that.form.content)
                                    // 计算字数
                                    that.contentLength = that.calculateTextLength(that.form.content)
                                }
                                // 检查是否需要自动打开封面设置
                                that.checkAutoOpenCoverSetting();
                            }
                        },
                    })
                },

                // tabs切换
                handleTabClick(e) {
                    this.fileList = [];
                    this.form.dynamicMediaList = [];
                    this.getDraftData();
                },

                // 编辑图片
                handleEditImg(file, index) {
                    if (this.isView) return
                    this.cropperImg = file.mediaUrl;
                    this.currentCropIndex = index;
                    this.cropperVisible = true;
                    this.$nextTick(() => {
                        this.initCropper();
                    })
                },

                // 初始化裁剪器
                initCropper() {
                    const imageElement = this.$refs.cropperImageRef;
                    if (this.cropper) {
                        this.cropper.destroy();
                    }

                    this.cropper = new window.Cropper(imageElement, {
                        autoCrop: true,
                        viewMode: 1,
                        autoCropArea: 1.0, // 初始裁剪区域为完整图片
                        aspectRatio: 1,  // 默认不限制比例
                    });

                    // 初始化快捷键
                    this.initShortcuts();
                    this.activeRatio = 'original';
                },

                // 设置裁剪比例
                setAspectRatio(ratio) {
                    if (ratio === 'original' || isNaN(ratio)) {
                        this.cropper.setAspectRatio(NaN);
                        this.activeRatio = 'original';
                    } else {
                        this.cropper.setAspectRatio(ratio);
                        this.activeRatio = ratio;
                    }
                },

                // 初始化快捷键
                initShortcuts() {
                    const handleKeyPress = (e) => {
                        if (!this.cropperVisible) return;

                        switch (e.key) {
                            case '1':
                                this.setAspectRatio(NaN);
                                break;
                            case '2':
                                this.setAspectRatio(1);
                                break;
                            case '3':
                                this.setAspectRatio(0.75);
                                break;
                            case '4':
                                this.setAspectRatio(1.33333);
                                break;
                        }
                    };

                    // 添加监听
                    document.addEventListener('keypress', handleKeyPress);
                    // 移除监听（在beforeDestroy或对话框关闭时）
                    this.$once('hook:beforeDestroy', () => {
                        document.removeEventListener('keypress', handleKeyPress);
                    });
                },

                // 确认裁剪
                confirmCropper() {
                    const that = this;
                    this.cropLoading = true;

                    // 获取裁剪后的画布
                    const canvas = this.cropper.getCroppedCanvas({
                        width: this.cropper.getData().width,
                        height: this.cropper.getData().height
                    });

                    // 转换为Blob对象
                    canvas.toBlob(async (blob) => {
                        const formData = new FormData();
                        formData.append('file', blob, `cropped_${Date.now()}.png`);

                        try {
                            const res = await $.ajax({
                                url: '/common/upload',
                                type: "POST",
                                data: formData,
                                processData: false,
                                contentType: false
                            });

                            if (res.code === 0) {
                                that.form.dynamicMediaList[that.currentCropIndex].mediaUrl = res.url;
                                that.form.dynamicMediaList[that.currentCropIndex].smallUrl = res.url;
                                that.$message.success('图片裁剪保存成功');
                                that.cropperVisible = false;
                                that.currentCropIndex = null;
                            } else {
                                that.$message.error(res.msg || '图片上传失败');
                            }
                        } catch (error) {
                            that.$message.error('网络请求异常');
                            console.error('上传失败:', error);
                        } finally {
                            that.cropLoading = false;
                        }
                    }, 'image/png');
                },

                // 替换图片/视频 方法
                async replaceUpload(file) {
                    const that = this;
                    let res = await this.uploadFile(file, '/common/uploadMany');
                    let extInfo = {
                        name: file.name,
                        size: file.size,
                    };
                    if (that.replaceIndex !== null) {
                        that.$set(
                            that.form.dynamicMediaList,
                            that.replaceIndex,
                            {
                                ...that.form.dynamicMediaList[that.replaceIndex],
                                width: res.fileUrlList[0].width,
                                height: res.fileUrlList[0].height,
                                ext: extInfo,
                                smallUrl: res.fileUrlList[0].small,
                                mediaUrl: res.fileUrlList[0].url,
                            }
                        );
                        if (that.form.type == 3) {
                            that.$nextTick(() => {
                                if (that.$refs.videoPlay) {
                                    that.$refs.videoPlay.load(); // 强制重新加载视频
                                }
                            });
                            that.form.coverImage = res.fileUrlList[0].small;
                        }
                    }
                    that.replaceIndex = null;
                },

                // 替换图片
                replaceImg(file, index) {
                    this.replaceIndex = index;
                },

                // 上传封面 方法
                async uploadCover(file) {
                    const res = await this.uploadFile(file);
                    this.coverImg = res;
                    this.$nextTick(() => {
                        this.initCropper();
                        this.$refs.reupload.clearFiles();
                    })
                },

                // 打开设置封面弹窗
                setCoverImage() {
                    this.coverImageVisible = true;
                    this.activeCoverName = 'first';
                    this.currentTime = 0;
                    if (this.form.coverImage) {
                        this.coverImg = this.form.coverImage;
                        this.$nextTick(() => {
                            this.initCropper();
                        })
                    }
                    this.$nextTick(() => {
                        // 生成缩略图数组
                        this.generateThumbnails();
                    })
                },

                // 设置封面裁剪比例
                setCropRatio(ratio) {
                    if (ratio === 'original' || isNaN(ratio)) {
                        this.cropper.setAspectRatio(NaN);
                        this.activeCropRatio = 'original';
                    } else {
                        this.cropper.setAspectRatio(ratio);
                        this.activeCropRatio = ratio;
                    }
                },

                // 生成缩略图方法
                async generateThumbnails() {
                    this.thumbnailLoading = true;
                    const interval = this.duration / 19; // 分19个间隔（得到20个点）
                    const captureTimes = [];
                    const url = this.form.dynamicMediaList[0]?.mediaUrl;
                    // 生成时间点数组（包含首尾）
                    for (let i = 0; i < 20; i++) {
                        captureTimes.push(i * interval);
                    }

                    this.thumbnails = []; // 清空旧缩略图
                    // 异步截取所有帧
                    for (const time of captureTimes) {
                        this.thumbnails.push({
                            time: time,
                            dataURL: await this.captureFrameAtTime(url, time) // 截取帧并保存为DataURL
                        });
                    }
                    this.$nextTick(() => {
                        this.thumbnailBoxWidth = this.$refs.thumbnailBox.offsetWidth;
                        this.thumbnailLoading = false;
                    })
                },

                // 在指定时间截取帧
                async captureFrameAtTime(url, time) {
                    let that = this;
                    return new Promise((resolve) => {
                        var baseUrl = '';
                        let video = document.createElement('video');
                        video.setAttribute('crossOrigin', 'anonymous');
                        video.setAttribute('src', url);
                        if (time == 0) time = 0.2; // 防止截取到黑屏
                        video.currentTime = time;
                        video.addEventListener('loadeddata', function () {
                            let canvas = document.createElement('canvas');
                            let width = video.videoWidth;
                            let height = video.videoHeight;
                            canvas.width = width;
                            canvas.height = height;
                            canvas.getContext('2d').drawImage(video, 0, 0, width, height);
                            baseUrl = canvas.toDataURL('image/jpeg');
                            return resolve(baseUrl);
                        })
                    });
                },

                // 时间轴拖动处理
                async onTimeUpdate(time) {
                    const url = this.form.dynamicMediaList[0]?.mediaUrl;
                    this.coverImg = await this.captureFrameAtTime(url, time)
                    this.$nextTick(() => {
                        this.initCropper();
                    })
                },

                // 计算当前时间对应的位置
                calculatePosition(time) {
                    const ratio = time / this.duration;
                    return ratio * (this.thumbnailBoxWidth - 12); // 12是point的宽度
                },

                // 开始拖动
                startDrag(e) {
                    this.isDragging = true;
                    document.addEventListener('mousemove', this.handleDrag);
                    document.addEventListener('mouseup', this.stopDrag);
                    this.handleDrag(e);
                },

                // 处理拖动
                handleDrag(e) {
                    if (!this.isDragging) return;

                    const rect = this.$refs.thumbnailBox.getBoundingClientRect();
                    let newX = e.clientX - rect.left - 6; // 6是point宽度的一半
                    newX = Math.max(0, Math.min(newX, this.thumbnailBoxWidth - 12));

                    const ratio = newX / (this.thumbnailBoxWidth - 12);
                    this.currentTime = ratio * this.duration;
                },

                // 停止拖动
                stopDrag() {
                    this.isDragging = false;
                    document.removeEventListener('mousemove', this.handleDrag);
                    document.removeEventListener('mouseup', this.stopDrag);
                    this.onTimeUpdate(this.currentTime);
                },

                // 点击缩略图容器跳转时间
                handleThumbMouseDown(e) {
                    if (this.isDragging) return; // 如果正在拖动，忽略点击事件
                    // if (e.target.closest('.thumbnail-item')) return;
                    const rect = this.$refs.thumbnailBox.getBoundingClientRect();
                    const clickX = e.clientX - rect.left;
                    const ratio = clickX / this.thumbnailBoxWidth;
                    this.currentTime = ratio * this.duration;
                    this.onTimeUpdate(this.currentTime);
                },

                // 格式化文件大小显示
                formatSize(bytes) {
                    if (bytes === undefined || bytes === null) return '--';
                    if (bytes === 0) return '0 Bytes';
                    const units = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(1024));
                    if (i === 0) return bytes + ' Bytes';
                    const size = bytes / Math.pow(1024, i);
                    return (size % 1 === 0 ? size.toFixed(0) : size.toFixed(2)) + ' ' + units[i];
                },

                // 格式化时间显示
                formatTime(seconds) {
                    const mins = Math.floor(seconds / 60);
                    const secs = Math.floor(seconds % 60);
                    return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
                },

                // 确定设置封面
                async confirmCover() {
                    const that = this;
                    that.coverLoading = true;
                    // 获取裁剪后的画布
                    const canvas = this.cropper.getCroppedCanvas({
                        width: this.cropper.getData().width,
                        height: this.cropper.getData().height
                    });

                    // 转换为Blob对象
                    canvas.toBlob(async (blob) => {
                        const formData = new FormData();
                        formData.append('file', blob, `cropped_${Date.now()}.png`);
                        try {
                            const res = await $.ajax({
                                url: '/common/upload',
                                type: "POST",
                                data: formData,
                                processData: false,
                                contentType: false
                            });
                            if (res.code === 0) {
                                that.form.coverImage = res.url
                                that.$message.success('保存成功');
                                that.coverImageVisible = false;
                            } else {
                                that.$message.error(res.msg || '图片上传失败');
                            }
                        } catch (error) {
                            that.$message.error('网络请求异常');
                            console.error('上传失败:', error);
                        } finally {
                            that.coverLoading = false;
                        }
                    }, 'image/png')
                },


                // 新增获取视频时长方法
                getVideoDuration(file) {
                    return new Promise((resolve, reject) => {
                        const video = document.createElement('video');
                        const url = URL.createObjectURL(file);

                        video.preload = 'metadata';
                        video.src = url;

                        video.onloadedmetadata = () => {
                            URL.revokeObjectURL(url); // 释放内存
                            resolve(video.duration);
                        };

                        video.onerror = (error) => {
                            URL.revokeObjectURL(url);
                            reject(error);
                        };
                    });
                },

                // 上传文件前操作
                beforeUpload(file) {
                    const isLt = file.size / 1024 / 1024 < 10;
                    if (!isLt) {
                        this.$message.error(`上传文件大小不能超过10 MB!`);
                        return false;
                    }
                },

                // 处理文件超出数量限制
                handleExceed(files, fileList) {
                    if (this.form.type === 3) {
                        this.$message.warning('视频只能上传一个文件');
                    }
                },

                // 监听文件选择事件
                async handleFileChange(file, fileList) {
                    if (this.loading) return;
                    // 图片限制20MB
                    // 视频限制20GB，60分钟时长以内
                    if (this.form.type === 3) {
                        try {
                            const duration = await this.getVideoDuration(file.raw);
                            if (duration > 3600) { // 超过60分钟时长
                                this.$message.error('视频时长不能超过60分钟');
                                clearTimeout(this.uploadTimer);
                                this.$refs.upload.clearFiles();
                                if (this.form.dynamicMediaList.length) {
                                    this.$refs.inUpload.clearFiles();
                                }
                                return false;
                            }
                        } catch (error) {
                            console.error('获取视频时长失败:', error);
                            this.$message.error('视频解析失败，请检查文件格式');
                        }
                    }
                    const isVideo = this.form.type === 3;
                    const maxSizeMB = isVideo ? 20 * 1024 : 20; // 视频20GB，图片20MB
                    const unit = isVideo ? 'GB' : 'MB';
                    const maxSizeBytes = maxSizeMB * 1024 * 1024;
                    if (file.size > maxSizeBytes) {
                        this.$message.error(`${isVideo ? '视频' : '图片'}大小不能超过${maxSizeMB}${unit}!`);
                        clearTimeout(this.uploadTimer);
                        this.$refs.upload.clearFiles();
                        if (this.form.dynamicMediaList.length) {
                            this.$refs.inUpload.clearFiles();
                        }
                        return false;
                    }
                    // 防抖处理：500ms 内未新增文件则触发上传
                    clearTimeout(this.uploadTimer);
                    this.uploadTimer = setTimeout(() => {
                        this.uploadAllFiles(fileList);
                    }, 500);
                    return true;
                },

                // 单文件上传方法
                uploadFile(file, api) {
                    return new Promise((resolve, reject) => {
                        const isLt = file.size / 1024 / 1024 < 10;
                        if (!isLt) {
                            this.$message.error(`上传文件大小不能超过10 MB!`);
                            return reject(new Error('文件大小超过限制'));
                        }
                        let apiUrl = api ? api : '/common/upload'
                        let that = this;
                        this.loading = true;
                        const formData = new FormData();
                        if (api) {
                            formData.append(`file_data`, file.raw);
                            formData.append('fileType', this.form.type);
                        } else {
                            formData.append('file', file.raw);
                        }
                        $.ajax({
                            url: apiUrl,
                            type: "POST",
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function (res) {
                                that.loading = false;
                                if (res.code == 0) {
                                    if (api) {
                                        resolve(res)
                                    } else {
                                        resolve(res.url);
                                    }
                                }
                                if (res.code == 500) {
                                    that.$message.error(res.msg || '上传失败');
                                    reject(new Error(res.msg || '上传失败'));
                                }
                            },
                            error: function (err) {
                                that.loading = false;
                                console.log('error', err)
                                reject(new Error('网络请求失败'));
                            }
                        })
                    })
                },

                // 多文件上传方法
                uploadAllFiles(fileList) {
                    let that = this;
                    this.loading = true;
                    let extInfo = null;
                    const formData = new FormData(); // 创建 FormData 对象
                    formData.append('fileType', this.form.type);

                    // 添加所有文件
                    fileList.forEach((file, index) => {
                        extInfo = {
                            name: file.name,
                            size: file.size,
                        }
                        formData.append(`file_data`, file.raw);
                    });

                    $.ajax({
                        url: '/common/uploadMany',
                        type: "POST",
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function (res) {
                            that.loading = false;
                            if (res.code == 0) {
                                if (that.form.type == 2) {
                                    that.form.dynamicMediaList.push(
                                        ...res.fileUrlList.map(item => ({
                                            width: item.width,
                                            height: item.height,
                                            smallUrl: item.small,
                                            mediaUrl: item.url
                                        }))
                                    );
                                    that.initDrag()
                                } else if (that.form.type == 3) {
                                    that.form.dynamicMediaList = [{
                                        ...res.fileUrlList[0],
                                        ext: extInfo,
                                        smallUrl: res.fileUrlList[0].small,
                                        mediaUrl: res.fileUrlList[0].url
                                    }];
                                    that.form.coverImage = res.fileUrlList[0].small;
                                    // 视频上传成功后自动打开封面设置弹窗
                                    that.$nextTick(() => {
                                        that.setCoverImage();
                                    });
                                }
                                that.initWangEditor();
                            }
                            if (res.code == 500) {
                                that.$message.error(res.msg || '上传失败');
                            }
                            that.$refs.upload.clearFiles();
                            if (that.form.dynamicMediaList.length) {
                                that.$refs.inUpload.clearFiles();
                            }
                        },
                        error: function (err) {
                            that.loading = false;
                            that.$refs.upload.clearFiles();
                            if (that.form.dynamicMediaList.length) {
                                that.$refs.inUpload.clearFiles();
                            }
                            console.log('error', err)
                        }
                    })
                },

                // 下拉加载用户
                userLoad() {
                    if (this.loadmore == 'nomore') {
                        return;
                    }
                    this.userPageNum += 1;
                    this.getUserList()
                },

                // 远程搜索用户
                remoteMethod(query) {
                    let that = this;
                    if (query !== '') {
                        // this.selectLoading = true;
                        that.userOptions = [];
                        that.userPageNum = 1;
                        that.loadmore = 'loadmore';
                        that.userQuery = query;
                        that.getUserList();
                    } else {
                        this.userOptions = [];
                    }
                },

                // 搜索用户列表
                getUserList(query) {
                    let that = this;
                    this.selectLoading = true;
                    $.ajax({
                        url: '/cat/userInfo/list',
                        type: "post",
                        dataType: "json",
                        data: {
                            userName: that.userQuery,
                            pageNum: that.userPageNum,
                            pageSize: 10,
                        },
                        success: function (result) {
                            that.selectLoading = false;
                            if (result.code == 0) {
                                that.userOptions = [...that.userOptions, ...result.rows]
                                if (result.rows.length < 10) {
                                    that.loadmore = 'nomore';
                                }
                            }
                        },
                        error: function () {
                            that.selectLoading = false;
                            that.$message.error(res.msg || '搜索用户失败');
                        }
                    });
                },

                // 选择用户
                selectUser(e) {
                    this.$nextTick(() => {
                        let userName = this.$refs.userSelect.selected.currentLabel
                        this.form.userName = userName;
                    })
                },

                // 初始化富文本编辑器
                initWangEditor() {
                    let that = this;

                    // 销毁旧实例
                    if (editor) {
                        editor.destroy()
                        editor = null
                    }
                    const { createEditor, createToolbar } = window.wangEditor
                    const editorConfig = {
                        placeholder: '请输入正文内容...',
                        MENU_CONF: {
                            // 上传图片
                            uploadImage: {
                                server: '/common/upload',
                                fieldName: 'file',
                                // 自定义插入图片
                                customInsert: (res, insertFn) => {
                                    if (res.code == 0) {
                                        insertFn(res.url)
                                    }
                                }
                            },
                            // 上传视频
                            uploadVideo: {
                                server: '/common/upload',
                                fieldName: 'file', // 自定义字段名
                                // 自定义插入视频
                                customInsert: (res, insertFn) => {
                                    if (res.code == 0) {
                                        insertFn(res.url)
                                    }
                                }
                            },
                        },
                        onChange(editor) {
                            const html = editor.getHtml()
                            // 计算字数
                            that.contentLength = that.calculateTextLength(html)

                            // 字数超限处理
                            if (that.contentLength > that.maxContentLength) {
                                that.handleContentOverflow(editor, html)
                            } else {
                                that.form.content = html
                            }
                        },
                    }

                    // 创建编辑器实例
                    editor = createEditor({
                        selector: '#editor-container',
                        html: that.form.content || '',
                        config: editorConfig,
                        mode: 'simple'
                    })

                    let toolbarConfig = {}
                    toolbarConfig.excludeKeys = ['insertTable', 'group-video',]
                    if (this.form.type == 3) {
                        toolbarConfig.excludeKeys = [
                            'insertTable', 'group-video', 'todo', 'insertLink', 'bulletedList',
                            'numberedList', 'group-image', 'blockquote', 'group-more-style',
                            'group-indent', 'codeBlock', 'divider',
                        ]
                    }

                    // 创建工具栏
                    let toolbar = createToolbar({
                        editor,
                        selector: '#toolbar-container',
                        config: toolbarConfig,
                        mode: 'default', // or 'simple'
                    })
                },

                // 计算内容长度
                calculateTextLength(val) {
                    var reg = /<[^<>]+>/g; //去标签
                    var value = val.replace(reg, "");
                    value = value.replace(/&nbsp;/gi, "")
                    return value.length
                },

                // 处理内容溢出
                handleContentOverflow(editor, text) {
                    // 保留最后一次合法内容
                    const lastValidHtml = this.form.content

                    // 恢复内容并保留光标位置
                    editor.setHtml(lastValidHtml)
                    editor.restoreSelection()

                    // 显示错误提示
                    this.$message.error(`内容不能超过${this.maxContentLength}字`)
                },

                // 添加表情
                selectEmoji(emoji) {
                    editor.focus()
                    editor.dangerouslyInsertHtml(emoji)
                    // this.emojiVisible = false
                },

                // 删除图片
                delImage(index) {
                    if (this.isView) return
                    this.form.dynamicMediaList.splice(index, 1);
                },

                // 添加视频错误处理方法
                handleVideoError(e) {
                    console.error('视频加载错误:', e.target.error);
                    console.log('视频错误代码:', e.target.error.code);
                    console.log('视频错误消息:', e.target.error.message);
                },
                // 添加视频加载成功处理方法
                handleVideoLoaded() {
                    const videoElement = this.$refs.videoPlay;
                    this.duration = videoElement.duration;
                },
                // 添加视频点击处理方法
                handleVideoClick() {
                    const videoPlayer = this.$refs.videoPlay;
                    if (videoPlayer.paused) {
                        videoPlayer.play();
                        this.playing = true;
                    } else {
                        videoPlayer.pause();
                        this.playing = false;
                    }
                },
                // 监听视频播放暂停
                handleVideoPause() {
                    this.playing = false;
                },
                handleVideoPlay() {
                    this.playing = true;
                },

                // 加载动态分类数据
                loadCategoryData() {
                    let that = this;
                    $.ajax({
                        url: catApi + "?pageNo=1&pageSize=999",
                        type: "post",
                        dataType: "json",
                        success: function (result) {
                            if (result.code == 0) {
                                that.categoryList = result.rows || [];
                            } else {
                                that.$message.error(result.msg || '获取动态分类失败');
                            }
                        },
                        error: function () {
                            that.$message.error(result.msg || '获取动态分类失败');
                        }
                    });
                },

                // 选择话题弹窗
                topicInputClick() {
                    this.topicIndex = 0;
                    this.topicVisible = true;
                },

                // 删除话题
                removeTopic(index) {
                    this.topicInfoList.splice(index, 1)
                    this.form.topic = this.topicInfoList.map(item => item.topicId)
                },

                // 选择话题
                topicChange(topic) {
                    if (this.form.topic.includes(topic.topicId)) {
                        this.topicInfoList = this.topicInfoList.filter(item => item.topicId !== topic.topicId);
                        this.form.topic = this.topicInfoList.map(item => item.topicId)
                        return;
                    }
                    if (this.topicInfoList.length >= 3) {
                        this.$message.error('最多选择3个话题');
                        return
                    }
                    this.topicInfoList.push(topic);
                    this.form.topic = this.topicInfoList.map(item => item.topicId)
                },

                // 加载话题列表
                getTopicList() {
                    let that = this;
                    $.ajax({
                        url: '/cat/TopicGroup/list',
                        type: "post",
                        dataType: "json",
                        data: {
                            orderByColumn: 'sortNo',
                            isAsc: 'asc',
                            isShow: 1,
                            pageNum: 1,
                            pageSize: 999,
                        },
                        success: function (result) {
                            that.loading = false;
                            if (result.code == 0) {
                                that.topicList = result.rows;
                                that.topicList.map(item => {
                                    item.children = []
                                })
                                that.getTopicListById(result.rows[0].id, 0)
                            } else {
                                that.$message.error(res.msg || '请求失败');
                            }
                        },
                        error: function () {
                            that.loading = false;
                            that.$message.error(res.msg || '请求失败');
                        }
                    });
                },

                // 获取话题列表
                getTopicLeft(item, index) {
                    this.topicIndex = index;
                    if (!item.children || item.children.length <= 0) {
                        this.getTopicListById(item.id)
                    }
                },

                // 获取话题列表
                getTopicListById(id) {
                    let that = this;
                    that.topicLoading = true;
                    return new Promise((resolve, reject) => {
                        $.ajax({
                            url: '/cat/topicInfoBackstage/list',
                            type: "post",
                            dataType: "json",
                            data: {
                                groupId: id,
                                pageSize: 999
                            },
                            success: function (result) {
                                if (result.code == 0) {
                                    const nodes = result.rows || []
                                    if (that.topicList.length) {
                                        that.topicList.forEach((item) => {
                                            if (item.id == id) {
                                                item.children = nodes
                                            }
                                        })
                                    }
                                } else {
                                    reject(new Error(result.msg || '请求失败'));
                                }
                                that.topicLoading = false;
                                resolve();
                            },
                            error: function (xhr, status, error) {
                                that.topicLoading = false;
                                reject(new Error(`请求失败: ${status} ${error}`));
                            }
                        });
                    });
                },

                // 轮播图切换
                carouselChange(e) {
                    this.initialIndex = e;
                },

                // 提交
                submit(type) {
                    let that = this;
                    let fnApi = prefix + "/new/add";
                    that.form.isCheck = type;
                    if (that.form.extendTime && that.form.extendTime.length) {
                        that.form.extendBeginTime = that.form.extendTime[0]
                        that.form.extendEndTime = that.form.extendTime[1]
                    }
                    if (that.dynamicId) {
                        fnApi = prefix + `/new/edit`
                    }
                    if (type == 0) {
                        that.loading = true;
                        let subData = JSON.parse(JSON.stringify(that.form));
                        if (subData.dynamicMediaList.length > 0) {
                            subData.dynamicMediaList.forEach((item) => {
                                item.ext = JSON.stringify(item.ext)
                            })
                        }
                        $.ajax({
                            url: fnApi,
                            data: JSON.stringify(subData),
                            type: "post",
                            dataType: "json",
                            contentType: "application/json;charset=utf-8",
                            success: function (result) {
                                that.loading = false
                                if (result.code === 0) {
                                    that.$message.success('保存成功');
                                    $.modal.openTab("修改动态内容", prefix + "/drafts");
                                    that.switchMenu(prefix + "/drafts");
                                } else {
                                    that.$message.error(result.msg || '保存失败');
                                }
                            },
                            error: function () {
                                that.loading = false
                                that.$message.error('保存失败');
                            }
                        })
                    } else if (type == 1) {
                        that.$refs.form.validate((valid) => {
                            if (valid) {
                                if (that.form.title == '') {
                                    that.$message.error('请填写标题');
                                    return
                                }
                                else if (that.form.content == '<p><br></p>' || that.form.content == '') {
                                    that.$message.error('请填写内容');
                                    return
                                } else if (that.form.type == 2 && that.form.dynamicMediaList.length <= 0) {
                                    that.$message.error('类型图片 请先上传【图片】');
                                    return
                                } else if (that.form.type == 3 && that.form.dynamicMediaList.length <= 0) {
                                    that.$message.error('类型视频 请先上传【视频】');
                                    return
                                } else if (that.form.releaseType == 0 && that.form.releaseTime == '') {
                                    that.$message.error('请选择定时发布时间');
                                    return
                                } else if (that.form.beExtend == 1 && (!that.form.extendTime || that.form.extendTime.length <= 0)) {
                                    that.$message.error('请选择定时推广时间');
                                    return
                                }
                                if (that.contentLength > that.maxContentLength) {
                                    that.$message.error(`内容超过${that.maxContentLength}字限制`)
                                    return false
                                }
                                that.loading = true;
                                let subData = JSON.parse(JSON.stringify(that.form));
                                if (subData.dynamicMediaList.length > 0) {
                                    subData.dynamicMediaList.forEach((item) => {
                                        item.ext = JSON.stringify(item.ext)
                                    })
                                }
                                $.ajax({
                                    url: fnApi,
                                    data: JSON.stringify(subData),
                                    type: "post",
                                    dataType: "json",
                                    contentType: "application/json;charset=utf-8",
                                    success: function (result) {
                                        that.loading = false
                                        if (result.code === 0) {
                                            that.$message.success('保存成功');
                                            $.modal.openTab("修改动态内容", prefix);
                                            that.switchMenu(prefix);
                                        } else {
                                            that.$message.error(result.msg || '保存失败');
                                        }
                                    },
                                    error: function () {
                                        that.loading = false
                                        that.$message.error('保存失败');
                                    }
                                })
                            } else {
                                console.log('error submit!!');
                                return false;
                            }
                        })
                    }

                },

                switchMenu(url) {
                    var parentDoc = window.parent.document;
                    $(parentDoc).find(".menuItem").parent().removeClass("selected");
                    $(parentDoc).find(".menuItem").each(function () {
                        if ($(this).attr("href") == url) {
                            $(this).parent().addClass("selected");
                        }
                    });
                },

                // 限制数字输入超过9位数
                handleNumberInput(field, value) {
                    if (value.length > 9) {
                        this.form[field] = value.slice(0, 9);
                    }
                },
            },
        })

        $('#type').change(function () {
            // 当输入框内容修改时执行这个函数
            if ($('#type').val() === '1') {
                $('#media').hide();
            } else {
                $('#media').show()
            }
        });


    </script>
</body>

</html>