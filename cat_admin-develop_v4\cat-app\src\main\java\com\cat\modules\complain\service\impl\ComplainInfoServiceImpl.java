package com.cat.modules.complain.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.complain.entity.ComplainInfo;
import com.cat.modules.complain.mapper.ComplainInfoMapper;
import com.cat.modules.complain.service.IComplainInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class ComplainInfoServiceImpl extends ServiceImpl<ComplainInfoMapper, ComplainInfo> implements IComplainInfoService {
    @Autowired
    private ComplainInfoMapper complainInfoMapper;



}
