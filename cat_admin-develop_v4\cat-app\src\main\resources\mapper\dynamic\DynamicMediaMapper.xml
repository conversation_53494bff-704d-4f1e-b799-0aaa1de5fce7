<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.dynamic.mapper.DynamicMediaMapper">

    <!-- 通用查询映射结果  配置了autoMapping=true,其他的无需配置，将会自动映射-->
    <resultMap id="BaseResultMap" type="com.cat.modules.dynamic.entity.DynamicMedia" autoMapping="true" />


    <update id="updateSmallImage">
        update dynamic_media set small_url=#{smallPath} where dynamic_media_id=#{mediaId}

    </update>

    <select id="getDynamicMedia" resultType="cn.hutool.core.map.CamelCaseMap">
        select  media_url,small_url,type,sort_no
        from dynamic_media
        where dynamic_id=#{dynamicId} and type=#{type}
    </select>

</mapper>
