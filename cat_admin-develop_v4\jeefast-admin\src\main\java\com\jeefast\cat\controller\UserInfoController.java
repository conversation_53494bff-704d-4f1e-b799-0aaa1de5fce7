package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cat.modules.attention.entity.AttentionInfo;
import com.cat.modules.attention.service.IAttentionInfoService;
import com.cat.modules.user.entity.User;
import com.jeefast.cat.domain.UserInfo;
import com.jeefast.cat.req.UserRechargeReq;
import com.jeefast.cat.service.IUserInfoService;
import com.jeefast.common.utils.RedisUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 用户信息Controller
 * 
 * <AUTHOR>
 * @date 2019-12-19
 */
@Controller
@RequestMapping("/cat/userInfo")
public class UserInfoController extends BaseController {
    private String prefix = "cat/userInfo";

    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IAttentionInfoService attentionInfoService;

    @RequiresPermissions("cat:userInfo:view")
    @GetMapping()
    public String userInfo() {
        return prefix + "/userInfo";
    }

    /**
     * 查询用户信息列表
     */
    @RequiresPermissions("cat:userInfo:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(UserInfo userInfo) {
        QueryWrapper<UserInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("1","1");
    	// 需要根据页面查询条件进行组装
        if(StringUtils.isNotEmpty(userInfo.getUserId())){
            queryWrapper.eq("user_id", userInfo.getUserId());
        }
        if(StringUtils.isNotEmpty(userInfo.getUserName())) {
    		queryWrapper.like("user_name", userInfo.getUserName());
    	}
        if(StringUtils.isNotEmpty(userInfo.getSex())) {
            queryWrapper.eq("sex", userInfo.getSex());
        }
        if(StringUtils.isNotEmpty(userInfo.getMobile())) {
            queryWrapper.eq("mobile", userInfo.getMobile());
        }
        if(StringUtils.isNotEmpty(userInfo.getApprove())) {
            queryWrapper.eq("approve", userInfo.getApprove());
        }
        if (StringUtils.isNotEmpty(userInfo.getBeginCreateDate()) && StringUtils.isNotEmpty(userInfo.getEndCreateDate())) {
            queryWrapper.ge("create_date",userInfo.getBeginCreateDate() + " 00:00:00");
            queryWrapper.le("create_date",userInfo.getEndCreateDate() + " 59:59:59");
        }
        startPage();
        return getDataTable(userInfoService.list(queryWrapper));
    }

    /**
     * 导出用户信息列表
     */
    @RequiresPermissions("cat:userInfo:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(UserInfo userInfo) {
        List<UserInfo> list = userInfoService.list(new QueryWrapper<>());
        ExcelUtil<UserInfo> util = new ExcelUtil<UserInfo>(UserInfo.class);
        return util.exportExcel(list, "userInfo");
    }

    /**
     * 新增用户信息
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存用户信息
     */
    @RequiresPermissions("cat:userInfo:add")
    @Log(title = "用户信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(UserInfo userInfo) {
        return toAjax(userInfoService.save(userInfo));
    }

    /**
     * 修改用户信息
     */
    @GetMapping("/edit/{userId}")
    public String edit(@PathVariable("userId") String userId, ModelMap mmap) {
        UserInfo userInfo = userInfoService.getById(userId);
        mmap.put("userInfo", userInfo);
        return prefix + "/edit";
    }

    /**
     * 修改保存用户信息
     */
    @RequiresPermissions("cat:userInfo:edit")
    @Log(title = "用户信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(UserInfo userInfo) {
        boolean flag = userInfoService.updateById(userInfo);
        if(flag){
            if("1".equals(userInfo.getIsMute())){
                delRedisUserData(userInfo.getUserId());
            }
        }
        return toAjax(flag);
    }

    /**
     * 删除用户信息
     */
    @RequiresPermissions("cat:userInfo:remove")
    @Log(title = "用户信息", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        String[] idList = Convert.toStrArray(ids);
        for (String id : idList) {
            delRedisUserData(id);
        }
        boolean flag  = userInfoService.removeByIds(Arrays.asList(idList));
        return toAjax(flag);
    }

    /**
     * 详述: 删除用户同时删除缓存
     * 开发人员：jingwei.huang
     * 创建时间：2020/9/26 1:08 上午
     * @param userId 要更新缓存的id
     * @return: void
     */
    public void delRedisUserData(String userId){
        //1.更新就token里的用户数为新用户数据
        UserInfo user = userInfoService.getById(userId);
        String openId = user.getOpenId();
        String mobile = user.getMobile();
        String unionId = user.getUnionId();
        String userNum = user.getUserNum();
        if(StrUtil.isNotEmpty(openId)){
            //查询redis中token
            String token = redisUtil.get("we_chat:open_id:"+openId);
            redisUtil.del("we_chat:open_id:"+openId);
            redisUtil.del("we_chat:token:"+token);
        }
        if(StrUtil.isNotEmpty(mobile)){
            //查询redis中token
            String token = redisUtil.get("we_chat:open_id:"+mobile);
            redisUtil.del("we_chat:open_id:"+mobile);
            redisUtil.del("we_chat:token:"+token);
        }
        if(StrUtil.isNotEmpty(unionId)){
            //查询redis中token
            String token = redisUtil.get("we_chat:open_id:"+unionId);
            redisUtil.del("we_chat:open_id:"+unionId);
            redisUtil.del("we_chat:token:"+token);
        }
        if(StrUtil.isNotEmpty(userNum)){
            //查询redis中token
            String token = redisUtil.get("we_chat:open_id:"+userNum);
            redisUtil.del("we_chat:open_id:"+userNum);
            redisUtil.del("we_chat:token:"+token);
        }
        //删除用户关注与粉丝数据
        //喜欢的人(关注)
        List<AttentionInfo> likeList = attentionInfoService.list(new LambdaQueryWrapper<AttentionInfo>().eq(AttentionInfo::getUserId,userId));
        //被其他人关注(粉丝)
        List<AttentionInfo> fansList = attentionInfoService.list(new LambdaQueryWrapper<AttentionInfo>().eq(AttentionInfo::getToUserId,userId).eq(AttentionInfo::getToType,"1"));
        List<String> likeUserIdList = likeList.stream().map(AttentionInfo::getToUserId).collect(Collectors.toList());
        List<String> fansUserIdList = fansList.stream().map(AttentionInfo::getUserId).collect(Collectors.toList());
        //修改关注粉丝数
        if(likeUserIdList.size()>0){
            userInfoService.update(new LambdaUpdateWrapper<UserInfo>().setSql("fans_count = fans_count - 1").in(UserInfo::getUserId,likeUserIdList));
        }
        if(fansUserIdList.size()>0){
            userInfoService.update(new LambdaUpdateWrapper<UserInfo>().setSql("attention_count = attention_count - 1").in(UserInfo::getUserId,fansUserIdList));
        }
        //删除关注记录
        attentionInfoService.remove(new LambdaQueryWrapper<AttentionInfo>().eq(AttentionInfo::getUserId,userId));
        attentionInfoService.remove(new LambdaQueryWrapper<AttentionInfo>().eq(AttentionInfo::getToUserId,userId).eq(AttentionInfo::getToType,"1"));

    }

    /**
     * 用户充值页面
     */
    @GetMapping("/rechargeView")
    public String rechargeView(String id,ModelMap mmap){
        UserInfo userInfo = userInfoService.getById(id);
        mmap.put("userInfo", userInfo);
        return prefix + "/recharge";
    }

    /**
     * 用户充值
     */
    @RequiresPermissions("cat:userInfo:recharge")
    @Log(title = "用户充值", businessType = BusinessType.UPDATE)
    @PostMapping("/recharge")
    @ResponseBody
    public AjaxResult recharge(UserRechargeReq req, ModelMap mmap){
        return userInfoService.recharge(req);
    }


}
