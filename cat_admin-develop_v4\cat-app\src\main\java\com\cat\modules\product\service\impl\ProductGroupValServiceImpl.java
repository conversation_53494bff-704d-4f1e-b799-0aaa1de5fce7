package com.cat.modules.product.service.impl;

import cn.hutool.core.map.CamelCaseLinkedMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.product.entity.ProductGroupVal;
import com.cat.modules.product.mapper.ProductGroupValMapper;
import com.cat.modules.product.service.IProductGroupValService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

import java.util.List;


@Service

public class ProductGroupValServiceImpl extends ServiceImpl<ProductGroupValMapper, ProductGroupVal> implements IProductGroupValService {
    @Autowired
    private ProductGroupValMapper dao;

    @Override
    public List<CamelCaseLinkedMap> listExt(QueryWrapper<ProductGroupVal> qw) {
        return dao.listExt(qw);
    }


}