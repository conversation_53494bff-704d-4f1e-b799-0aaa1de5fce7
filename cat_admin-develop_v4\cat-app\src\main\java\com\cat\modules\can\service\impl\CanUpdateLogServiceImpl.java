package com.cat.modules.can.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.can.entity.CanUpdateLog;
import com.cat.modules.can.mapper.CanUpdateLogMapper;
import com.cat.modules.can.service.ICanUpdateLogService;
import com.cat.modules.user.entity.User;
import com.cat.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Slf4j
@Service
public class CanUpdateLogServiceImpl extends ServiceImpl<CanUpdateLogMapper, CanUpdateLog> implements ICanUpdateLogService {
    @Autowired
    private CanUpdateLogMapper canUpdateLogMapper;
    

    @Override
    public boolean addLog(String userId, int updateValue,String type,String remark) {
        
        String operation="1";
        if(updateValue<0){
            operation="2";
        }
        CanUpdateLog canUpdateLog = new CanUpdateLog();
        canUpdateLog.setOperation(operation);
        canUpdateLog.setUserId(userId);
        canUpdateLog.setType(type);
        canUpdateLog.setUpdateValue(updateValue);
        canUpdateLog.setRemark(remark);
        return this.save(canUpdateLog);
    }
}
