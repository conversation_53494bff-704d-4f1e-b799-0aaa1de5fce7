package com.cat.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class SysMessageInfo extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "message_id",type = IdType.UUID)
    @TableField("message_id")
    private String messageId;

    
    @TableField("type")
    private String type;

    
    @TableField("content")
    private String content;

    
    @TableField("business_id")
    private String businessId;

    
    @TableField("business_type")
    private String businessType;


    
    @TableField("user_id")
    private String userId;

    
    @TableField("to_user_id")
    private String toUserId;

    
    @TableField("is_read")
    private String isRead = "0";

    
    @TableField("update_date")
    private LocalDateTime updateDate;

    
    @TableField("cover_image")
    private String coverImage;

    
    @TableField("cover_text")
    private String coverText;

}
