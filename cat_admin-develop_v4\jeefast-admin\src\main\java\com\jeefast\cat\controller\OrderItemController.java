package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.cat.modules.order.entity.OrderItem;
import com.cat.modules.order.service.IOrderItemService;
import com.cat.modules.order.service.IShoppingService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 订单子Controller
 * 
 * <AUTHOR>
 * @date 2021-04-26
 */
@Controller
@RequestMapping("/cat/orderItem")
public class OrderItemController extends BaseController {
    private String prefix = "cat/orderItem";

    @Autowired
    private IOrderItemService orderItemService;

    @RequiresPermissions("cat:orderItem:view")
    @GetMapping()
    public String orderItem(OrderItem req,ModelMap mmap) {
        mmap.put("orderId",req.getOrderId());
        return prefix + "/orderItem";
    }

    /**
     * 查询订单子列表
     */
    @RequiresPermissions("cat:orderItem:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(OrderItem req) {
        QueryWrapper<OrderItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("1","1");
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getOrderId())) {
    		queryWrapper.eq("order_id", req.getOrderId());
    	} 
    	if(StringUtils.isNotEmpty(req.getUserId())) {
    		queryWrapper.eq("user_id", req.getUserId());
    	}
        if(StringUtils.isNotEmpty(req.getProductId())) {
            queryWrapper.eq("product_id", req.getProductId());
        }
        if(StringUtils.isNotEmpty(req.getProductName())) {
            queryWrapper.like("product_name", req.getProductName());
        }
		// 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
		}
        startPage();
        queryWrapper.orderByDesc("create_date");
        return getDataTable(orderItemService.list(queryWrapper));
    }

    /**
     * 导出订单子列表
     */
    @RequiresPermissions("cat:orderItem:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(OrderItem orderItem) {
        List<OrderItem> list = orderItemService.list(new QueryWrapper<>());
        ExcelUtil<OrderItem> util = new ExcelUtil<OrderItem>(OrderItem.class);
        return util.exportExcel(list, "orderItem");
    }

    /**
     * 新增订单子
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存订单子
     */
    @RequiresPermissions("cat:orderItem:add")
    @Log(title = "订单子", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(OrderItem orderItem) {
        return toAjax(orderItemService.save(orderItem));
    }

    /**
     * 修改订单子
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        OrderItem orderItem = orderItemService.getById(id);
        mmap.put("orderItem", orderItem);
        return prefix + "/edit";
    }

    /**
     * 修改保存订单子
     */
    @RequiresPermissions("cat:orderItem:edit")
    @Log(title = "订单子", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(OrderItem orderItem) {
        return toAjax(orderItemService.updateById(orderItem));
    }

    /**
     * 删除订单子
     */
    @RequiresPermissions("cat:orderItem:remove")
    @Log(title = "订单子", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(orderItemService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
