package com.cat.modules.product.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cat.modules.product.entity.Product;

import java.util.List;


public interface IProductService extends IService<Product> {

    List<CamelCaseMap> listExt(QueryWrapper<Product> qw);
}