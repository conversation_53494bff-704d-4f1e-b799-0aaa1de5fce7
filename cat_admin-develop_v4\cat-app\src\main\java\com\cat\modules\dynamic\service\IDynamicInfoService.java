package com.cat.modules.dynamic.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cat.modules.dynamic.entity.DynamicInfo;
import com.cat.modules.dynamic.resp.DynamicViewRespVo;

import java.util.List;


public interface IDynamicInfoService extends IService<DynamicInfo> {

    int calculateWeight();

    List<CamelCaseMap> recommendList();

    List<DynamicViewRespVo> viewCount(List<String> dynamicIds, String startTime, String endTime);

    /**
     * 根据浏览量获取前十的动态信息
     *
     * @return
     */
    List<DynamicInfo> getTopTen();
}
