package com.cat.modules.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class OrderItem extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id",type = IdType.UUID)
    @TableField("id")
    private String id;

    

    @TableField("order_id")
    private String orderId;

    

    @TableField("user_id")
    private String userId;

    

    @TableField("product_id")
    private String productId;

    

    @TableField("product_name")
    private String productName;

    

    @TableField("product_image")
    private String productImage;

    

    @TableField("product_sku_id")
    private String productSkuId;

    

    @TableField("product_sku_name")
    private String productSkuName;

    

    @TableField("currentunitprice")
    private BigDecimal currentunitprice;

    
    @TableField("quantity")
    private Integer quantity;

    
    @TableField("price")
    private BigDecimal price;

    
    @TableField("vip_price")
    private BigDecimal vipPrice;

    
    @TableField("totalprice")
    private BigDecimal totalprice;

    
    @TableField("discount")
    private BigDecimal discount;

    
    @TableField("refund_sts")
    private Integer refundSts;

    
    @TableField("seller_msg")
    private String sellerMsg;


    
    @TableField("refund_id")
    private String refundId;

    
    @TableField("remark")
    private String remark;

    
    @TableField("logistics")
    private String logistics;
    
    @TableField("logistics_num")
    private String logisticsNum;


}
