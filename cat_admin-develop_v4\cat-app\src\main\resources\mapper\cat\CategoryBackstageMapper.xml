<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeefast.cat.mapper.CategoryBackstageMapper">
    <!--
    <resultMap type="CategoryBackstage" id="CategoryBackstageResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="name"    column="name"    />
        <result property="image"    column="image"    />
        <result property="status"    column="status"    />
        <result property="sortOn"    column="sort_on"    />
        <result property="createDate"    column="create_date"    />
    </resultMap>
    -->


    <select id="selectByCategoryId" resultType="com.jeefast.cat.domain.CategoryBackstage">
        

    </select>

    <select id="selectDeptList" resultType="com.jeefast.cat.domain.CategoryBackstage">

    </select>
</mapper>

