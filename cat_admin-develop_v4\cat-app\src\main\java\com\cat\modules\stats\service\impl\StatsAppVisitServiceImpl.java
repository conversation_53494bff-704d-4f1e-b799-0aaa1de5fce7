package com.cat.modules.stats.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.cat.modules.stats.entity.StatsAppVisit;
import com.cat.modules.stats.mapper.StatsAppVisitMapper;
import com.cat.modules.stats.service.IStatsAppVisitService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.util.DataRow;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import org.springframework.beans.factory.annotation.*;
import org.springframework.transaction.annotation.*;

import lombok.extern.slf4j.Slf4j;
import java.util.*;

@Slf4j
@Service
public class StatsAppVisitServiceImpl extends ServiceImpl<StatsAppVisitMapper, StatsAppVisit> implements IStatsAppVisitService {
    @Autowired
    private StatsAppVisitMapper statsAppVisitMapper;


    @Override
    public StatsAppVisit nowVisit() {
        return statsAppVisitMapper.nowVisit();
    }

    @Override
    public DataRow realActiveBetweenDay(String event,Date start, Date end) {
        return statsAppVisitMapper.realActiveBetweenDay(event,DateUtil.formatDateTime(start),DateUtil.formatDateTime(end));
    }

    @Override
    public List<DataRow> platformPieChart(Date start, Date end) {
        return statsAppVisitMapper.platformPieChart(start,end);
    }

    @Override
    public List<DataRow> versionPieChart(DateTime start, DateTime end) {
        return statsAppVisitMapper.versionPieChart(start,end);
    }

    @Override
    public List<DataRow> brandPieChart(DateTime start, DateTime end) {
        return statsAppVisitMapper.brandPieChart(start,end);
    }

    @Override
    public List<DataRow> channelPieChart(DateTime start, DateTime end) {
        return statsAppVisitMapper.channelPieChart(start,end);
    }

    @Override
    public List<DataRow> userTrendChart(DateTime start, DateTime end) {
        return statsAppVisitMapper.userTrendChart(start,end);
    }

    @Override
    public List<DataRow> registerUser(DateTime start, DateTime end) {
        return statsAppVisitMapper.registerUser(start,end);
    }


}
