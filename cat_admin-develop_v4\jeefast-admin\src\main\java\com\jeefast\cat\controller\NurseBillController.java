package com.jeefast.cat.controller;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import cn.hutool.core.map.CamelCaseMap;
import com.alibaba.fastjson.JSONObject;
import com.cat.util.BlankUtil;
import com.jeefast.cat.common.EleAliasInfo;
import com.jeefast.cat.req.addEleAliasReq;
import com.jeefast.cat.service.NurseBillService;
import com.jeefast.common.annotation.Log;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.enums.BusinessType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.NurseBillModel;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 动态内容Controller
 */
@Controller
@RequestMapping("/report/nurseBill")
public class NurseBillController extends BaseController {
    private final String PREFIX = "report/nurseBill";

    @Autowired
    private NurseBillService nurseBillService;


    @GetMapping()
    public String dynamicBackstage() {
        return PREFIX + "/view";
    }

    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(NurseBillModel req) {
        QueryWrapper<NurseBillModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("1", "1");
        // 需要根据页面查询条件进行组装
        if (StringUtils.isNotEmpty(req.getEleMaster())) {
            queryWrapper.like("ele.ele_master", req.getEleMaster());
        }
        if (BlankUtil.isNotEmpty(req.getReportType())) {
            queryWrapper.eq("ele.report_type", req.getReportType());
        }
        if (BlankUtil.isNotEmpty(req.getIsImportance())) {
            queryWrapper.eq("ele.is_importance", req.getIsImportance());
        }
        if (StringUtils.isNotEmpty(req.getEleRemarks())) {
            queryWrapper.like("ele.ele_remarks", req.getEleRemarks()).or().like("alias_name_list", req.getEleRemarks());
//            queryWrapper.like("alias.alias_name", req.getEleRemarks());
        }
        if (StringUtils.isNotEmpty(req.getProjectLabel())) {
            queryWrapper.like("ele.project_label", req.getProjectLabel()).or().like("project_alias", req.getProjectLabel());
//            queryWrapper.like("alias.project_label", req.getProjectLabel());
        }
        if (BlankUtil.isNotEmpty(req.getParams().get("beginCreateDate"))) {
            queryWrapper.ge("ele.create_date", req.getParams().get("beginCreateDate"));
        }
        if (BlankUtil.isNotEmpty(req.getParams().get("endCreateDate"))) {
            queryWrapper.le("ele.create_date", req.getParams().get("endCreateDate"));
        }
        if (BlankUtil.isNotEmpty(req.getNotIdList())) {
            String notIdList = req.getNotIdList();
            List<String> eleIdList = Arrays.asList(Convert.toStrArray(notIdList));
            queryWrapper.notIn("ele.ele_id", eleIdList);
        }

        startPage();
        List<CamelCaseMap<String, Object>> result = nurseBillService.NurseBillList(queryWrapper);
        for (CamelCaseMap<String, Object> item : result) {
            Long originalId = (Long) item.get("eleId");
            item.put("eleId", originalId.toString());
        }
        return getDataTable(result);
    }

    @GetMapping("/add")
    public String add() {
        return PREFIX + "/add";
    }

    @PostMapping("/add")
    @Log(title = "指标数据", businessType = BusinessType.INSERT)
    @ResponseBody
    public AjaxResult addSve(@RequestBody addEleAliasReq req) {
        boolean flag = nurseBillService.addSave(req);
        return toAjax(flag);
    }


    @GetMapping("/move/")
    public String move() {
        return PREFIX + "/move";
    }


    @GetMapping("/move/list")
    @ResponseBody
    public TableDataInfo move_list(NurseBillModel req) {
        QueryWrapper<NurseBillModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("1", "1");
        String ele_id_list = req.getIdList();
        List<String> eleIdList = Arrays.asList(Convert.toStrArray(ele_id_list));
        queryWrapper.in("ele_id", eleIdList);
        List<CamelCaseMap<String, Object>> result = nurseBillService.NurseBillList(queryWrapper);
        for (CamelCaseMap<String, Object> item : result) {
            Long originalId = (Long) item.get("eleId");
            item.put("eleId", originalId.toString());
        }
        return getDataTable(result);
    }

    @PostMapping("/move/list")
    @Log(title = "报告指标", businessType = BusinessType.UPDATE)
    @ResponseBody
    public AjaxResult move_save(@RequestBody NurseBillModel req) {
        QueryWrapper<NurseBillModel> queryWrapper = new QueryWrapper<>();
        String ele_id_list = req.getIdList();
        List<String> eleIdList = Arrays.asList(Convert.toStrArray(ele_id_list));
        queryWrapper.in("ele_id", eleIdList);
        boolean flag = nurseBillService.Move(req, queryWrapper);
        return toAjax(flag);
    }

    @Log(title = "报告指标", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        boolean flag = nurseBillService.delete(Arrays.asList(Convert.toStrArray(ids)));
        return toAjax(flag);
    }

    @GetMapping("/edit/{eleId}")
    public String edit(@PathVariable("eleId") String eleId, ModelMap modelMap) {
        NurseBillModel nurseBill = nurseBillService.getById(eleId);
        EleAliasInfo eleAliasInfo = nurseBillService.selectAliasByEleId(new BigInteger(eleId));
        if (eleAliasInfo != null) {
            nurseBill.setAliasNameList(eleAliasInfo.getAliasName());
            nurseBill.setProjectAlias(eleAliasInfo.getProjectLabel());
        }
        modelMap.put("nurseBill", nurseBill);
        return PREFIX + "/edit";
    }

    @PostMapping("/edit")
    @ResponseBody
    @Log(title = "报告指标", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@RequestBody NurseBillModel nurseBill) {
        return toAjax(nurseBillService.updateNurseBill(nurseBill));
    }
}
