package com.cat.modules.praise.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.praise.entity.PraiseLog;
import com.cat.modules.praise.mapper.PraiseLogMapper;
import com.cat.modules.praise.service.IPraiseLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Slf4j
@Service

public class PraiseLogServiceImpl extends ServiceImpl<PraiseLogMapper, PraiseLog> implements IPraiseLogService {




    @Autowired
    private PraiseLogMapper praiseLogMapper;


}
