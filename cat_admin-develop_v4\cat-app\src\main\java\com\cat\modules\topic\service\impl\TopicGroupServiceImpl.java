package com.cat.modules.topic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.topic.entity.TopicGroup;
import com.cat.modules.topic.mapper.TopicGroupMapper;
import com.cat.modules.topic.service.ITopicGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class TopicGroupServiceImpl extends ServiceImpl<TopicGroupMapper, TopicGroup> implements ITopicGroupService {
    @Autowired
    private TopicGroupMapper topicGroupMapper;


}
