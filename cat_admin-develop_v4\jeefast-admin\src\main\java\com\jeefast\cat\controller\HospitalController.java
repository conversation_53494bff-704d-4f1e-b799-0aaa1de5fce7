package com.jeefast.cat.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.StrUtil;
import com.jeefast.cat.domain.Channel;
import com.jeefast.cat.resp.DictResp;
import com.jeefast.common.utils.file.FileUploadUtils;
import com.jeefast.system.domain.SysDictData;
import com.jeefast.system.service.ISysDictDataService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.Hospital;
import com.jeefast.cat.service.IHospitalService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 医院Controller
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Controller
@RequestMapping("/cat/hospital")
public class HospitalController extends BaseController {
    private String prefix = "cat/hospital";

    @Autowired
    private IHospitalService hospitalService;

    @Resource
    private ISysDictDataService dictDataService;

    @RequiresPermissions("cat:hospital:view")
    @GetMapping()
    public String hospital() {
        return prefix + "/hospital";
    }

    /**
     * 查询医院列表
     */
    @RequiresPermissions("cat:hospital:view")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Hospital hospital) {
        QueryWrapper<Hospital> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        // 需要根据页面查询条件进行组装
        if (StringUtils.isNotEmpty(hospital.getHospitalCode())) {
            queryWrapper.like("hospital_code", hospital.getHospitalCode());
        }
        if (StringUtils.isNotEmpty(hospital.getHospitalName())) {
            queryWrapper.like("hospital_name", hospital.getHospitalName());
        }
        // 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        List<Hospital> hospitalList = hospitalService.processList(hospitalService.list(queryWrapper));

        return getDataTable(hospitalList);
    }

    /**
     * 导出医院列表
     */
    @RequiresPermissions("cat:hospital:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Hospital hospital) {
        List<Hospital> list = hospitalService.list(new QueryWrapper<>());
        ExcelUtil<Hospital> util = new ExcelUtil<Hospital>(Hospital.class);
        return util.exportExcel(list, "hospital");
    }

    /**
     * 新增医院
     */
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        mmap.put("hospital_tags", dictDataService.selectDictDataByType("hospital_tag"));
        return prefix + "/add";
    }

    /**
     * 新增保存医院
     */
    @RequiresPermissions("cat:hospital:add")
    @Log(title = "医院", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestParam("file") MultipartFile multipartFile, Hospital hospital) throws IOException {
        if (multipartFile != null && StrUtil.isNotEmpty(multipartFile.getOriginalFilename())) {
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            hospital.setLogo(cloudPath);
        }
        return toAjax(hospitalService.save(hospital));
    }

    /**
     * 修改医院
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        Hospital hospital = hospitalService.getById(id);
        mmap.put("hospital", hospital);
        List<SysDictData> dataList = dictDataService.selectDictDataByType("hospital_tag");
        if (StringUtils.isNotEmpty(hospital.getHospitalTags())) {
            if (StringUtils.isNotEmpty(hospital.getHospitalTags())) {
                List<String> tagValueList = Arrays.asList(StringUtils.split(hospital.getHospitalTags(), ",").clone());
                for (SysDictData dictData : dataList) {
                    tagValueList.forEach(itm -> {
                        if (itm.equals(dictData.getDictValue())) {
                            dictData.setSelected(true);
                        }
                    });
                }
            }
        }
        mmap.put("hospital_tags", dataList);
        return prefix + "/edit";
    }

    /**
     * 修改保存医院
     */
    @RequiresPermissions("cat:hospital:edit")
    @Log(title = "医院", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestParam("file") MultipartFile multipartFile, Hospital hospital) throws IOException {
        if (multipartFile != null && StrUtil.isNotEmpty(multipartFile.getOriginalFilename())) {
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            hospital.setLogo(cloudPath);
        }
        return toAjax(hospitalService.updateById(hospital));
    }

    /**
     * 删除医院
     */
    @RequiresPermissions("cat:hospital:remove")
    @Log(title = "医院", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(hospitalService.softDelete(ids));
    }

    /**
     * 状态修改
     */
    @Log(title = "医院管理", businessType = BusinessType.UPDATE)
    @RequiresPermissions("cat:hospital:edit")
    @PostMapping("/changeStatus")
    @ResponseBody
    public AjaxResult changeStatus(Hospital hospital) {
        return toAjax(hospitalService.changeStatus(hospital));
    }


    /**
     * 状态修改
     */
    @GetMapping("/getDeptInfo")
    @ResponseBody
    public AjaxResult getRelateDeptInfo(@RequestParam("id") Long id) {
        List<DictResp> dictRespList = hospitalService.getHospitalDeptInfo(id);
        return AjaxResult.success(dictRespList);
    }
}
