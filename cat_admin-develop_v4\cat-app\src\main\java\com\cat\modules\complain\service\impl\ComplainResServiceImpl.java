package com.cat.modules.complain.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.complain.entity.ComplainRes;
import com.cat.modules.complain.mapper.ComplainResMapper;
import com.cat.modules.complain.service.IComplainResService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class ComplainResServiceImpl extends ServiceImpl<ComplainResMapper, ComplainRes> implements IComplainResService {
    @Autowired
    private ComplainResMapper complainResMapper;



}
