<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.shop.mapper.ShopEvaluateMapper">

    <!-- 通用查询映射结果  配置了autoMapping=true,其他的无需配置，将会自动映射-->
    <resultMap id="BaseResultMap" type="com.cat.modules.shop.entity.ShopEvaluate" autoMapping="true" />


    <select id="avgScore" resultType="double">
        select avg(score)
        from shop_evaluate
        where is_check='1' and shop_id=#{shopId}
    </select>

</mapper>
