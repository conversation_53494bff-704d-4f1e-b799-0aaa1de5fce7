package com.cat.modules.sys.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.annotation.TableField;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class SysCity extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId("id")
    @TableField("id")
    private String id;

    

    @TableField("city_id")
    private String cityId;

    

    @TableField("level")
    private Integer level;

    

    @TableField("parent_id")
    private String parentId;

    

    @TableField("area_code")
    private String areaCode;

    

    @TableField("name")
    private String name;

    

    @TableField("merger_name")
    private String mergerName;

    

    @TableField("lng")
    private BigDecimal lng;

    

    @TableField("lat")
    private BigDecimal lat;

    

    @TableField("is_show")
    private Boolean isShow;



}
