package com.jeefast.cat.job;

import com.cat.modules.dynamic.service.IDynamicMediaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 视频转码信息查询
 */
@Slf4j
@Component("videoStreamTaskJob")
public class VideoStreamTaskJob {

    @Resource
    IDynamicMediaService dynamicMediaService;

    /**
     * 每5秒钟查一次
     */
    public void adaptiveDynamicStreamingInfoQueryJob() {
        log.info("视频转码信息查询任务开始");
        dynamicMediaService.adaptiveDynamicStreamingInfoQueryJob();
        log.info("视频转码信息查询任务结束");
    }
}
