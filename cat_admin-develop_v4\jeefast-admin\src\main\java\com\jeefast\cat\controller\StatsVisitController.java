package com.jeefast.cat.controller;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cat.modules.dynamic.entity.DynamicInfo;
import com.cat.modules.dynamic.service.IDynamicInfoService;
import com.cat.modules.stats.service.IStatsAppVisitService;
import com.cat.modules.user.entity.User;
import com.cat.modules.user.service.UserService;
import com.cat.util.DataRow;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

/**
 * 统计api
 * 
 * <AUTHOR>
 * @date 2021-10-25
 */
@Controller
@RequestMapping("/stats")
public class StatsVisitController extends BaseController {
    @Autowired
    private IStatsAppVisitService statsAppVisitService;

    @Autowired
    private UserService userService;

    @Autowired
    private IDynamicInfoService dynamicInfoService;

    /**
     * 日统计访客数
     */
    @PostMapping( "/dayVisit")
    @ResponseBody
    public AjaxResult dayVisit() {
        Date nowDate = new Date();
        Date nowStartDate = DateUtil.beginOfDay(nowDate);
        Date nowEndDate = DateUtil.endOfDay(nowDate);
        Date yesterdayDate = DateUtil.offsetDay(nowDate,-1);
        //实时统计数据
        DataRow nowData = statsAppVisitService.realActiveBetweenDay("start",nowStartDate,nowEndDate);
        DataRow yesterdayData = statsAppVisitService.realActiveBetweenDay("start",DateUtil.beginOfDay(yesterdayDate),DateUtil.endOfDay(yesterdayDate));

        int yearDynamicCut = dynamicInfoService.count(new QueryWrapper<DynamicInfo>()
                .ge("create_date",DateUtil.beginOfYear(nowDate))
                .le("create_date",DateUtil.endOfYear(nowDate))
        );
        int nowDynamicCut = dynamicInfoService.count(new QueryWrapper<DynamicInfo>()
                .ge("create_date",nowStartDate)
                .le("create_date",nowEndDate)
        );
        nowData.set("nowDynamicCut",nowDynamicCut);
        nowData.set("yearDynamicCut",yearDynamicCut);

        DataRow result = new DataRow();
        result.set("nowData",nowData);
        result.set("yesterdayData",yesterdayData);
        return AjaxResult.success(result);
    }


    /**
     * 详述: 统计设备平台活跃度，饼图
     * 开发人员：jingwei.huang
     * 创建时间：2021/12/1 5:34 下午
     */
    @PostMapping( "/platformPieChart")
    @ResponseBody
    public AjaxResult platformPieChart(String scope){
        Date nowDate = new Date();
        Date nowStartDate = DateUtil.beginOfDay(nowDate);
        Date nowEndDate = DateUtil.endOfDay(nowDate);
        if("moon".equals(scope)){
            nowStartDate = DateUtil.beginOfMonth(nowDate);
            nowEndDate = DateUtil.endOfMonth(nowDate);
        }else if("year".equals(scope)){
            nowStartDate = DateUtil.beginOfYear(nowDate);
            nowEndDate = DateUtil.endOfYear(nowDate);
        }

        List<DataRow> result = statsAppVisitService.platformPieChart(DateUtil.beginOfDay(nowStartDate),DateUtil.endOfDay(nowEndDate));
        return AjaxResult.success(result);
    }

    /**
     * 详述: 统计版本号活跃度，饼图
     * 开发人员：jingwei.huang
     * 创建时间：2021/12/1 5:34 下午
     */
    @PostMapping( "/versionPieChart")
    @ResponseBody
    public AjaxResult versionPieChart(String scope){
        Date nowDate = new Date();
        Date nowStartDate = DateUtil.beginOfDay(nowDate);
        Date nowEndDate = DateUtil.endOfDay(nowDate);
        if("moon".equals(scope)){
            nowStartDate = DateUtil.beginOfMonth(nowDate);
            nowEndDate = DateUtil.endOfMonth(nowDate);
        }else if("year".equals(scope)){
            nowStartDate = DateUtil.beginOfYear(nowDate);
            nowEndDate = DateUtil.endOfYear(nowDate);
        }
        List<DataRow> result = statsAppVisitService.versionPieChart(DateUtil.beginOfDay(nowStartDate),DateUtil.endOfDay(nowEndDate));
        return AjaxResult.success(result);
    }


    /**
     * 详述: 统计手机品牌活跃度，饼图
     * 开发人员：jingwei.huang
     * 创建时间：2021/12/1 5:34 下午
     */
    @PostMapping( "/brandPieChart")
    @ResponseBody
    public AjaxResult brandPieChart(String scope){
        Date nowDate = new Date();
        Date nowStartDate = DateUtil.beginOfDay(nowDate);
        Date nowEndDate = DateUtil.endOfDay(nowDate);
        if("moon".equals(scope)){
            nowStartDate = DateUtil.beginOfMonth(nowDate);
            nowEndDate = DateUtil.endOfMonth(nowDate);
        }else if("year".equals(scope)){
            nowStartDate = DateUtil.beginOfYear(nowDate);
            nowEndDate = DateUtil.endOfYear(nowDate);
        }
        List<DataRow> result = statsAppVisitService.brandPieChart(DateUtil.beginOfDay(nowStartDate),DateUtil.endOfDay(nowEndDate));
        return AjaxResult.success(result);
    }

    /**
     * 详述: 来源渠道活跃度，饼图
     * 开发人员：jingwei.huang
     * 创建时间：2021/12/1 5:34 下午
     */
    @PostMapping( "/channelPieChart")
    @ResponseBody
    public AjaxResult channelPieChart(String scope){
        Date nowDate = new Date();
        Date nowStartDate = DateUtil.beginOfDay(nowDate);
        Date nowEndDate = DateUtil.endOfDay(nowDate);
        if("moon".equals(scope)){
            nowStartDate = DateUtil.beginOfMonth(nowDate);
            nowEndDate = DateUtil.endOfMonth(nowDate);
        }else if("year".equals(scope)){
            nowStartDate = DateUtil.beginOfYear(nowDate);
            nowEndDate = DateUtil.endOfYear(nowDate);
        }
        List<DataRow> result = statsAppVisitService.channelPieChart(DateUtil.beginOfDay(nowStartDate),DateUtil.endOfDay(nowEndDate));
        return AjaxResult.success(result);
    }


    /**
     * 详述: 用户使用趋势图
     * 开发人员：jingwei.huang
     * 创建时间：2021/12/1 5:34 下午
     */
    @PostMapping( "/userTrendChart")
    @ResponseBody
    public AjaxResult userTrendChart(String scope){
        Date nowDate = new Date();
        Date nowStartDate = DateUtil.beginOfDay(nowDate);
        Date nowEndDate = DateUtil.endOfDay(nowDate);
        if("moon".equals(scope)){
            //近30天
            nowStartDate = DateUtil.offsetDay(nowDate,-30);//DateUtil.beginOfMonth(nowDate);
            //nowEndDate = DateUtil.endOfMonth(nowDate);
        }else if("year".equals(scope)){
            nowStartDate = DateUtil.beginOfYear(nowDate);
            //nowEndDate = DateUtil.endOfYear(nowDate);
        }
        List<DateTime> dateTimeList = DateUtil.rangeToList(nowStartDate, nowEndDate, DateField.DAY_OF_YEAR);
        Map<String,Integer> dateMap = new LinkedHashMap<>();
        Map<String,Integer> registerCounts  = new LinkedHashMap<>();
        for (DateTime dateTime : dateTimeList) {
            dateMap.put(dateTime.toString("yyyy-MM-dd"),0);
            registerCounts.put(dateTime.toString("yyyy-MM-dd"),0);
        }

        List<DataRow> data = statsAppVisitService.userTrendChart(DateUtil.beginOfDay(nowStartDate),DateUtil.endOfDay(nowEndDate));

        List<DataRow> registerData = statsAppVisitService.registerUser(DateUtil.beginOfDay(nowStartDate),DateUtil.endOfDay(nowEndDate));

        for (DataRow dataRow : data) {
            dateMap.put(dataRow.getString("date"),dataRow.getInt("count"));
        }

        for (DataRow dataRow : registerData) {
            registerCounts.put(dataRow.getString("date"),dataRow.getInt("count"));
        }

        Set<String> xAxis = dateMap.keySet();
        Collection<Integer> series = dateMap.values();
        Collection<Integer> register = registerCounts.values();
        DataRow result = new DataRow();
        result.set("series",series);
        result.set("xAxis",xAxis);
        result.set("register",register);
        return AjaxResult.success(result);
    }


    /**
     * 详述: 统计用户数
     * 开发人员：jingwei.huang
     * 创建时间：2021/12/1 5:34 下午
     */
    @PostMapping( "/userSum")
    @ResponseBody
    public AjaxResult userSum(String scope){
        Date nowDate = new Date();
        Date nowStartDate = DateUtil.beginOfDay(nowDate);
        Date nowEndDate = DateUtil.endOfDay(nowDate);

        int userSum = userService.count();

        int dayCount = userService.count(new QueryWrapper<User>()
                .ge("create_date",nowStartDate)
                .le("create_date",nowEndDate)
        );

        //昨日
        int yesterdayCount = userService.count(new QueryWrapper<User>()
                .ge("create_date",DateUtil.offsetDay(nowStartDate,-1))
                .le("create_date",DateUtil.offsetDay(nowEndDate,-1))
        );

        int monthCount = userService.count(new QueryWrapper<User>()
                .ge("create_date",DateUtil.beginOfMonth(nowDate))
                .le("create_date",DateUtil.endOfMonth(nowDate))
        );

        DataRow result = new DataRow();
        result.set("userSum",userSum);
        result.set("monthUserCount",monthCount);
        result.set("dayUserCount",dayCount);
        result.set("yesterdayCount",yesterdayCount);
        return AjaxResult.success(result);
    }


}
