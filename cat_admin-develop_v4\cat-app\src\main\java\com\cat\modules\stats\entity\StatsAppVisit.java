package com.cat.modules.stats.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.annotation.TableField;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.extern.slf4j.Slf4j;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class StatsAppVisit extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId("id")
    @TableField("id")
    private String id;

    

    @TableField("visit_pv")
    private Integer visitPv;

    

    @TableField("new_user")
    private Integer newUser;

    

    @TableField("visit_uv")
    private Integer visitUv;



}
