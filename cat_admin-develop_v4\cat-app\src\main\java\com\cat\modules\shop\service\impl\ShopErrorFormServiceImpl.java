package com.cat.modules.shop.service.impl;

import com.cat.modules.shop.entity.ShopErrorForm;
import com.cat.modules.shop.mapper.ShopErrorFormMapper;
import com.cat.modules.shop.service.IShopErrorFormService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import org.springframework.beans.factory.annotation.*;
import org.springframework.transaction.annotation.*;

import lombok.extern.slf4j.Slf4j;
import java.util.*;

@Slf4j
@Service
public class ShopErrorFormServiceImpl extends ServiceImpl<ShopErrorFormMapper, ShopErrorForm> implements IShopErrorFormService {
    @Autowired
    private ShopErrorFormMapper shopErrorFormMapper;


}
