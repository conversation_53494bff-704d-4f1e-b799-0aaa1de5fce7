package com.jeefast.cat.domain.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 文字内容安全审核请求实体
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
public class DynamicContentCheckReq {
    
    /**
     * 动态id
     */
    @NotBlank(message = "动态id不能为空")
    private String dynamicId;
    List<String> addDynamicMediaId;
    List<String> delDynamicMediaId;
    
    /**
     * 旧内容
     */
    private String content;

    /**
     * 旧标题
     */
    private String title;
} 