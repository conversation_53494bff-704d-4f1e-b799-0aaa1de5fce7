package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;
import com.cat.modules.order.entity.Shopping;
import com.cat.modules.order.service.IShoppingService;
import com.cat.modules.shop.entity.ShopPhotos;
import com.cat.modules.shop.service.IShopPhotosService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.ExchangeGoodsLogBackstage;
import com.jeefast.cat.service.IExchangeGoodsLogBackstageService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 兑换商品日志Controller
 * 
 * <AUTHOR>
 * @date 2020-11-08
 */
@Controller
@RequestMapping("/cat/ExchangeGoodsLogBackstage")
public class ExchangeGoodsLogBackstageController extends BaseController {
    private String prefix = "cat/ExchangeGoodsLogBackstage";

    @Autowired
    private IExchangeGoodsLogBackstageService exchangeGoodsLogBackstageService;
    @Autowired
    private IShoppingService shoppingService;

    @RequiresPermissions("cat:ExchangeGoodsLogBackstage:view")
    @GetMapping()
    public String ExchangeGoodsLogBackstage() {
        return prefix + "/ExchangeGoodsLogBackstage";
    }

    /**
     * 查询兑换商品日志列表
     */
    @RequiresPermissions("cat:ExchangeGoodsLogBackstage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ExchangeGoodsLogBackstage req) {
        QueryWrapper<ExchangeGoodsLogBackstage> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(ObjectUtil.isNotNull(req.getPayType())) {
    		queryWrapper.eq("t1.pay_type",req.getPayType());
    	} 
    	if(StringUtils.isNotEmpty(req.getUserId())) {
    		queryWrapper.eq("t1.user_id", req.getUserId());
    	}
		// 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("startTime")), "t1.create_date", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "t1.create_date", params.get("endTime"));
		}
        startPage();
        return getDataTable(exchangeGoodsLogBackstageService.logList(queryWrapper));
    }

    /**
     * 导出兑换商品日志列表
     */
    @RequiresPermissions("cat:ExchangeGoodsLogBackstage:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ExchangeGoodsLogBackstage exchangeGoodsLogBackstage) {
        List<ExchangeGoodsLogBackstage> list = exchangeGoodsLogBackstageService.list(new QueryWrapper<>());
        ExcelUtil<ExchangeGoodsLogBackstage> util = new ExcelUtil<ExchangeGoodsLogBackstage>(ExchangeGoodsLogBackstage.class);
        return util.exportExcel(list, "ExchangeGoodsLogBackstage");
    }

    /**
     * 新增兑换商品日志
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存兑换商品日志
     */
    @RequiresPermissions("cat:ExchangeGoodsLogBackstage:add")
    @Log(title = "兑换商品日志", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ExchangeGoodsLogBackstage exchangeGoodsLogBackstage) {
        return toAjax(exchangeGoodsLogBackstageService.save(exchangeGoodsLogBackstage));
    }

    /**
     * 修改兑换商品日志
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        ExchangeGoodsLogBackstage exchangeGoodsLogBackstage = exchangeGoodsLogBackstageService.getById(id);
        mmap.put("exchangeGoodsLogBackstage", exchangeGoodsLogBackstage);
        return prefix + "/edit";
    }

    /**
     * 修改保存兑换商品日志
     */
    @RequiresPermissions("cat:ExchangeGoodsLogBackstage:edit")
    @Log(title = "兑换商品日志", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ExchangeGoodsLogBackstage exchangeGoodsLogBackstage) {
        return toAjax(exchangeGoodsLogBackstageService.updateById(exchangeGoodsLogBackstage));
    }

    /**
     * 删除兑换商品日志
     */
    @RequiresPermissions("cat:ExchangeGoodsLogBackstage:remove")
    @Log(title = "兑换商品日志", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(exchangeGoodsLogBackstageService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }


    /**
     * 修改兑换商品日志
     */
    @GetMapping("/define/{id}")
    public String define(@PathVariable("id") String id, ModelMap mmap) {
        ExchangeGoodsLogBackstage exchangeGoodsLogBackstage = exchangeGoodsLogBackstageService.getById(id);
        //兑换记录id替换orderid查询
        Shopping shopping = shoppingService.getOne(new QueryWrapper<Shopping>().eq("order_id",exchangeGoodsLogBackstage.getId()));
        mmap.put("exchangeGoodsLogBackstage", exchangeGoodsLogBackstage);
        mmap.put("shopping", shopping);
        return prefix + "/define";
    }


    /**
     * 修改兑换商品日志
     */
    @RequiresPermissions("cat:ExchangeGoodsLogBackstage:edit")
    @Log(title = "确认兑换商品信息", businessType = BusinessType.UPDATE)
    @PostMapping("/defineSave")
    @ResponseBody
    public AjaxResult defineSave(Shopping shopping) {
        boolean flage = shoppingService.updateById(shopping);
        return toAjax(flage);
    }
}
