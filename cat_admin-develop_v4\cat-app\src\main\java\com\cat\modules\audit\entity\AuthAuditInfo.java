package com.cat.modules.audit.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                            import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;


 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("auth_audit_info")
public class AuthAuditInfo extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id", type = IdType.AUTO)
    private String id;
    
    @Excel(name = "认证记录id")
    private String authId;
    
    @Excel(name = "审核状态 1已通过 2未通过 3待审核")
    private String status;
    
    @Excel(name = "审核人")
    private String userId;
    
    @Excel(name = "审核人名称")
    private String truename;
    
    @Excel(name = "审核描述")
    private String description;

    @TableField(exist = false)
    private String authIds;
}
