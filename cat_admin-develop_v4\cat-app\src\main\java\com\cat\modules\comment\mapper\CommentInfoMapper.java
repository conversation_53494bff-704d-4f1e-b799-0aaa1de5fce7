package com.cat.modules.comment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cat.modules.comment.entity.CommentInfo;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Select;


public interface CommentInfoMapper extends BaseMapper<CommentInfo> {

    
    @Select("select parent_ids  from  comment_info where comment_id=#{id}")
    String getParentIds(String id);
}
