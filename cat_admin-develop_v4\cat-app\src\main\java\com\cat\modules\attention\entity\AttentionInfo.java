package com.cat.modules.attention.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class AttentionInfo extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "attention_id",type = IdType.UUID)
    @TableField("attention_id")
    private String attentionId;

    
    @TableField("to_user_id")
    private String toUserId;

    
    @TableField("to_type")
    private String toType;

    
    @TableField("user_id")
    private String userId;



}
