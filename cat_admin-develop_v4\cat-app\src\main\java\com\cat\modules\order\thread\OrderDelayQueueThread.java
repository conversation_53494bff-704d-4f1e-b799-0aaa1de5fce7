package com.cat.modules.order.thread;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cat.modules.order.entity.OrderInfo;
import com.cat.modules.order.entity.OrderLog;
import com.cat.modules.order.service.IOrderInfoService;
import com.cat.modules.order.thread.entity.OrderDelayedTask;
import com.cat.modules.order.thread.entity.OrderPrimary;
import com.jeefast.common.enums.CachEnum;
import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.Delayed;
import java.util.concurrent.atomic.AtomicBoolean;


@Slf4j
public class OrderDelayQueueThread implements Runnable{

    private static IOrderInfoService orderService;

    private static RedisUtil redisTemplate;

    private static AtomicBoolean isrun = new AtomicBoolean(true);

    private  static DelayQueue delayQueue  = new DelayQueue();

    public OrderDelayQueueThread(IOrderInfoService orderService,RedisUtil redisTemplate) {
        super();
        this.orderService = orderService;
        this.redisTemplate=redisTemplate;
    }

    public static DelayQueue getDelayQueue() {
        return delayQueue;
    }

    public static void setDelayQueue(DelayQueue delayQueue) {
        OrderDelayQueueThread.delayQueue = delayQueue;
    }

    
    @SuppressWarnings("unchecked")
    public void add(String orderId,long timeOut) {
        Map<String, String> map = new HashMap<String, String>();
        OrderPrimary findOrderOne = orderService.selectByPrimaryKey(orderId);
        String jsonString = JSON.toJSONString(findOrderOne);
        OrderDelayedTask orderDelayedTask = new OrderDelayedTask(jsonString,timeOut);
        map.put(orderId, JSON.toJSONString(orderDelayedTask));
        redisTemplate.hmset(CachEnum.ORDER_DELAY_QUEUE.getCode(),map);
        delayQueue.offer(orderDelayedTask);
    }

    @Override
    public void run() {
        try {
            while (isrun.get()) {
                Delayed take = delayQueue.take();
                if (ObjectUtil.isNotNull(take)) {
                    OrderDelayedTask orderDelayedTask = (OrderDelayedTask) take;
                    String orderJson = orderDelayedTask.getOrderJson();
                    OrderPrimary tbOrder = JSONObject.parseObject(orderJson, OrderPrimary.class);
                    String orderId = tbOrder.getId();
                    if ("40".equals(tbOrder.getStatus())) {
                        OrderPrimary findOrderOne = orderService.selectByPrimaryKey(tbOrder.getId());
                        if ("40".equals(findOrderOne.getStatus())) {
                            OrderLog orderLog = new OrderLog();
                            orderLog.setOrderId(orderId);
                            orderLog.setRemark("收货超时系统自动收货");
                            orderService.receipt(orderLog);
                            redisTemplate.hdel(CachEnum.ORDER_DELAY_QUEUE.getCode(),orderId);
                        }
                    }else if("10".equals(tbOrder.getStatus())){
                        OrderPrimary findOrderOne = orderService.selectByPrimaryKey(tbOrder.getId());
                        if ("10".equals(findOrderOne.getStatus())) {
                            OrderLog orderLog = new OrderLog();
                            orderLog.setOrderId(orderId);
                            orderLog.setRemark("未支付超时系统自动取消");
                            orderService.cancel(orderLog);
                            redisTemplate.hdel(CachEnum.ORDER_DELAY_QUEUE.getCode(),orderId);
                        }
                    }
                }
            }
        } catch (InterruptedException e) {
            log.error("",e);
        }
    }

    
    public static boolean removeDelay(OrderPrimary order) {
        
        if (isrun.get()) {
            Object[] objectArray =  delayQueue.toArray();
            if (objectArray!=null && objectArray.length>0) {
                for (Object object: objectArray) {
                    OrderDelayedTask orderDelayedTask=(OrderDelayedTask) object;
                    OrderPrimary orderTask = JSON.parseObject(orderDelayedTask.getOrderJson(),OrderPrimary.class);
                    if(orderTask.getId().equals(order.getId())){
                        redisTemplate.hdel(CachEnum.ORDER_DELAY_QUEUE.getCode(),order.getId());
                        boolean remove = delayQueue.remove(orderDelayedTask);
                        return remove;
                    }
                }
            }
        }
        return false;
    }

    public void putDelayQueueForRedis(OrderDelayedTask orderDelayedTask) {
        delayQueue.offer(orderDelayedTask);
    }

}
