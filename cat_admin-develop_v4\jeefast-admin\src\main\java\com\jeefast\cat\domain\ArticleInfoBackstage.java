package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                                                                import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 文章表 article_info
 *
 * <AUTHOR>
 * @date 2020-08-30
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("article_info")
public class ArticleInfoBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "article_id", type = IdType.UUID)
    private String articleId;
    /** 封面图 */
    @Excel(name = "封面图")
    private String coverImage;
    /** 文章标题 */
    @Excel(name = "文章标题")
    private String title;
    /** 文章类型（1：测评、2：健康、3：其他） */
    @Excel(name = "文章类型", readConverterExp = "1=：测评、2：健康、3：其他")
    private String type;
    /** 文章状态（1：草稿、2：发布、3：下架） */
    @Excel(name = "文章状态", readConverterExp = "1=：草稿、2：发布、3：下架")
    private String state;
    /** 内容摘要 */
    private String summary;
    /** 文章内容 */
    private String content;
    /** 访问量 */
    @Excel(name = "访问量")
    private Integer visitCount;
    /** 评论数 */
    @Excel(name = "评论数")
    private Integer commentCount;
    /** 点赞数 */
    @Excel(name = "点赞数")
    private Integer praiseCount;
    /** 是否推荐(0：未推荐、1：推荐) */
    @Excel(name = "是否推荐(0：未推荐、1：推荐)")
    private String isRecommend;
    /** 排序值 */
    @Excel(name = "排序值")
    private Integer sortOn;
    /** 作者名称 */
    @Excel(name = "作者名称")
    private String author;
    /** 数据来源连接 */
    private String sourceUrl;
    /** 创建人id */
    private String userId;
    /** 权重 */
    @Excel(name = "权重")
    private Integer weight;
    /** 封面图宽px */
    @Excel(name = "封面图宽px")
    private Integer width;
    /** 封面图高px */
    @Excel(name = "封面图高px")
    private Integer height;
    /** 是否后台审核通过：0：否、1：是 */
    @Excel(name = "是否后台审核通过：0：否、1：是")
    private String isCheck;
}
