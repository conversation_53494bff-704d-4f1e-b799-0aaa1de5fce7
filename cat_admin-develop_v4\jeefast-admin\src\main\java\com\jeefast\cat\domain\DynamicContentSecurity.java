package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                                                        import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 动态内容安全审核记录表 dynamic_content_security
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dynamic_content_security")
public class DynamicContentSecurity extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /** 动态内容id */
    private String dynamicId;
    /** 类型 动态标题、动态文字内容、图、视频 */
    @Excel(name = "类型 1动态标题、2动态文字内容、3图、4视频")
    private String type;
    /** 任务taskId */
    @Excel(name = "任务taskId")
    private String taskId;
    /** 动态内容媒体id */
    @Excel(name = "动态内容媒体id")
    private String relateId;
    /** 媒体内容url */
    @Excel(name = "媒体内容url")
    private String relateUrl;
    /** 文字内容 */
    @Excel(name = "文字内容")
    private String content;
    /** 识别策略 */
    @Excel(name = "识别策略")
    private String bizType;
    /** 任务状态：FINISH（任务已完成）、PENDING （任务等待中）、RUNNING （任务进行中）、ERROR （任务出错）、CANCELLED （任务已取消） */
    @Excel(name = "任务状态：FINISH", readConverterExp = "任=务已完成")
    private String status;
    /** 后续操作建议：Block：建议屏蔽，Review ：建议人工复审，Pass：建议通过 */
    @Excel(name = "后续操作建议：Block：建议屏蔽，Review ：建议人工复审，Pass：建议通过")
    private String suggestion;
    /** 返回检测结果：Normal：正常，Porn：色情，Abuse：谩骂，Ad：广告；以及其他令人反感、不安全或不适宜的内容类型。 */
    @Excel(name = "返回检测结果：Normal：正常，Porn：色情，Abuse：谩骂，Ad：广告；以及其他令人反感、不安全或不适宜的内容类型。")
    private String labels;
    /** 返回检测结果，命中的关键词。 */
    @Excel(name = "返回检测结果，命中的关键词。")
    private String keywords;
    /** 置信度,取值范围：0（置信度最低）-100（置信度最高 ），越高代表越有可能属于当前返回的标签 */
    @Excel(name = "置信度,取值范围：0", readConverterExp = "置=信度最低")
    private Long score;
    /** 内容安全检查返回信息 */
    @Excel(name = "内容安全检查返回信息")
    private String respContent;
    /** 最终审核状态 0待审核 1通过 2不通过 */
    @Excel(name = "最终审核状态 0待审核 1通过 2不通过")
    private String auditStatus;
    /** 审核原因 */
    @Excel(name = "审核原因")
    private String auditReason;
    /** 排序号 */
    @Excel(name = "排序号")
    private Long sortNo;
}
