<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.task.mapper.TaskLogsMapper">
    <!--
    <resultMap type="TaskLogs" id="TaskLogsResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="progress"    column="progress"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
    </resultMap>
    -->

    <select id="listExt" resultType="cn.hutool.core.map.CamelCaseMap">
        select t1.*,t2.user_name,t3.name taskName
        from task_logs t1
        left join user_info t2 on t1.user_id=t2.user_id
        left join task_info t3 on t3.id=t1.task_id
        ${ew.customSqlSegment}
    </select>

</mapper>

