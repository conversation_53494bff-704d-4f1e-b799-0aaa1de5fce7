package com.cat.modules.user.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.cat.modules.user.entity.UserLogoutLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface UserLogoutLogMapper extends BaseMapper<UserLogoutLog> {

    List<CamelCaseMap> listExt(@Param(Constants.WRAPPER) QueryWrapper qw);
}
