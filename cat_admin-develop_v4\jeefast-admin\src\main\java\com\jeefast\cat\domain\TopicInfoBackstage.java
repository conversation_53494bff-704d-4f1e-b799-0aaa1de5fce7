package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 话题表 topic_info
 *
 * <AUTHOR>
 * @date 2020-08-08
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("topic_info")
public class TopicInfoBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "topic_id", type = IdType.UUID)
    private String topicId;
    /** 话题名称 */
    @Excel(name = "话题名称")
    private String topicName;
    /** 话题被引用次数 */
    @Excel(name = "话题被引用次数")
    private Integer citeCount;
    /** 话题创建人 */
    @Excel(name = "话题创建人")
    private String userId;
    /** 封面图 */
    @Excel(name = "封面图")
    private String coverImage;
    /** 简介 */
    @Excel(name = "简介")
    private String brief;
    /** 话题加入人数 */
    @Excel(name = "话题加入人数")
    private Integer joinCount;
    /** 话题分组id */
    @Excel(name = "话题分组id")
    private String groupId;
}
