package com.jeefast.cat.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.VipOpenLogBackstage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * vip开通记录 数据层
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
public interface VipOpenLogBackstageMapper extends BaseMapper<VipOpenLogBackstage> {

    List<CamelCaseMap<String, Object>> userList(@Param("ew") Wrapper queryWrapper);
}