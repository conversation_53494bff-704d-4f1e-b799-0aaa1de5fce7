<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeefast.cat.mapper.DynamicInfoBackstageMapper">
    <!--
    <resultMap type="DynamicInfoBackstage" id="DynamicInfoBackstageResult">
        <result property="dynamicId"    column="dynamic_id"    />
        <result property="petId"    column="pet_id"    />
        <result property="userId"    column="user_id"    />
        <result property="content"    column="content"    />
        <result property="addr"    column="addr"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="praiseCount"    column="praise_count"    />
        <result property="commentCount"    column="comment_count"    />
        <result property="type"    column="type"    />
        <result property="source"    column="source"    />
        <result property="createDate"    column="create_date"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="height"    column="height"    />
        <result property="width"    column="width"    />
        <result property="mediaCount"    column="media_count"    />
        <result property="weight"    column="weight"    />
    </resultMap>
    -->


    <select id="dynamicList" resultType="cn.hutool.core.map.CamelCaseMap">
        select t1.*,t2.user_name
        from dynamic_info t1
        left join user_info t2 on t1.user_id=t2.user_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>


</mapper>

