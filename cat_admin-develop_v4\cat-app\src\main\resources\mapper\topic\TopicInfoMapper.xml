<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.topic.mapper.TopicInfoMapper">

    <!-- 通用查询映射结果  配置了autoMapping=true,其他的无需配置，将会自动映射-->
    <resultMap id="BaseResultMap" type="com.cat.modules.topic.entity.TopicInfo" autoMapping="true" />
    <select id="getByBusinessTopicList" resultType="com.cat.modules.topic.entity.TopicInfo">
        SELECT r.topic_id,r.topic_name,i.group_id
        from content_topic_rel r
                 LEFT JOIN topic_info i ON r.topic_id = i.topic_id
        where r.business_id=#{businessId} and r.business_type=#{type}
    </select>

    <select id="imageList" resultType="java.lang.String">
        select t2.cover_image
        from content_topic_rel t1
        inner join dynamic_info t2 on t1.business_id=t2.dynamic_id
        where t1.topic_id=#{topicId} and t2.is_delete='0' and t2.is_check='1'
        group by t2.dynamic_id
        order by t2.weight desc
        limit 3
    </select>


</mapper>
