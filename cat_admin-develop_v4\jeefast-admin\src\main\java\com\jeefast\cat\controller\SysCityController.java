package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;

import com.cat.modules.sys.entity.SysCity;
import com.cat.modules.sys.service.ISysCityService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 城市Controller
 * 
 * <AUTHOR>
 * @date 2020-11-28
 */
@Controller
@RequestMapping("/cat/sysCity")
public class SysCityController extends BaseController {
    private String prefix = "cat/sysCity";

    @Autowired
    private ISysCityService sysCityService;

    @RequiresPermissions("cat:sysCity:view")
    @GetMapping()
    public String sysCity() {
        return prefix + "/sysCity";
    }

    /**
     * 查询城市列表
     */
    @RequiresPermissions("cat:sysCity:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysCity sysCity) {
        QueryWrapper<SysCity> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	/*if(StringUtils.isNotEmpty(sysDemo.getLoginName())) {
    		queryWrapper.like("login_name", sysDemo.getLoginName());
    	} 
    	if(StringUtils.isNotEmpty(sysDemo.getUserName())) {
    		queryWrapper.like("user_name", sysDemo.getUserName());
    	}*/
		// 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        return getDataTable(sysCityService.list(queryWrapper));
    }

    /**
     * 导出城市列表
     */
    @RequiresPermissions("cat:sysCity:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysCity sysCity) {
        List<SysCity> list = sysCityService.list(new QueryWrapper<>());
        ExcelUtil<SysCity> util = new ExcelUtil<SysCity>(SysCity.class);
        return util.exportExcel(list, "sysCity");
    }

    /**
     * 新增城市
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存城市
     */
    @RequiresPermissions("cat:sysCity:add")
    @Log(title = "城市", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SysCity sysCity) {
        return toAjax(sysCityService.save(sysCity));
    }

    /**
     * 修改城市
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        SysCity sysCity = sysCityService.getById(id);
        mmap.put("sysCity", sysCity);
        return prefix + "/edit";
    }

    /**
     * 修改保存城市
     */
    @RequiresPermissions("cat:sysCity:edit")
    @Log(title = "城市", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SysCity sysCity) {
        return toAjax(sysCityService.updateById(sysCity));
    }

    /**
     * 删除城市
     */
    @RequiresPermissions("cat:sysCity:remove")
    @Log(title = "城市", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(sysCityService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }

    @PostMapping( "/getByparentIdList")
    @ResponseBody
    public AjaxResult getByparentIdList(String parentId){
        return AjaxResult.success(sysCityService.list(new QueryWrapper<SysCity>()
                .eq("parent_id",parentId)
        ));
    }

}
