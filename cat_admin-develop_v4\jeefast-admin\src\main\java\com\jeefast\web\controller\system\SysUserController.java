package com.jeefast.web.controller.system;

import java.util.Arrays;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.Channel;
import com.jeefast.cat.domain.Hospital;
import com.jeefast.cat.service.IChannelService;
import com.jeefast.cat.service.IHospitalService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import com.jeefast.common.annotation.Log;
import com.jeefast.common.constant.UserConstants;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.core.page.TableDataInfo;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.framework.shiro.service.SysPasswordService;
import com.jeefast.framework.util.ShiroUtils;
import com.jeefast.system.domain.SysUser;
import com.jeefast.system.service.ISysPostService;
import com.jeefast.system.service.ISysRoleService;
import com.jeefast.system.service.ISysUserService;

import javax.annotation.Resource;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/system/user")
public class SysUserController extends BaseController {
    private String prefix = "system/user";

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    private SysPasswordService passwordService;

    @Resource
    private IChannelService channelService;

    @Resource
    private IHospitalService hospitalService;

    @RequiresPermissions("system:user:view")
    @GetMapping()
    public String user() {
        return prefix + "/user";
    }

    @RequiresPermissions("system:user:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SysUser user) {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:user:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SysUser user) {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        return util.exportExcel(list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:user:import")
    @PostMapping("/importData")
    @ResponseBody
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        String operName = ShiroUtils.getSysUser().getUserName();
        String message = userService.importUser(userList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    @RequiresPermissions("system:user:view")
    @GetMapping("/importTemplate")
    @ResponseBody
    public AjaxResult importTemplate() {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        return util.importTemplateExcel("用户数据");
    }

    /**
     * 新增用户
     */
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        mmap.put("roles", roleService.selectRoleAll());
        mmap.put("posts", postService.selectPostAll());
        return prefix + "/add";
    }

    /**
     * 新增保存用户
     */
    @RequiresPermissions("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated SysUser user) {
        if (UserConstants.USER_NAME_NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName()))) {
            return error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        } else if (UserConstants.USER_PHONE_NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (UserConstants.USER_EMAIL_NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setSalt(ShiroUtils.randomSalt());
        user.setPassword(passwordService.encryptPassword(user.getUserName(), user.getPassword(), user.getSalt()));
        user.setCreateBy(ShiroUtils.getUserName());
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @GetMapping("/edit/{userId}")
    public String edit(@PathVariable("userId") Long userId, ModelMap mmap) {
        mmap.put("user", userService.selectUserById(userId));
        mmap.put("roles", roleService.selectRolesByUserId(userId));
        mmap.put("posts", postService.selectPostsByUserId(userId));
        return prefix + "/edit";
    }

    /**
     * 修改保存用户
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && SysUser.isAdmin(user.getUserId())) {
            return error("不允许修改超级管理员用户");
        } else if (UserConstants.USER_PHONE_NOT_UNIQUE.equals(userService.checkPhoneUnique(user))) {
            return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        } else if (UserConstants.USER_EMAIL_NOT_UNIQUE.equals(userService.checkEmailUnique(user))) {
            return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(ShiroUtils.getUserName());
        return toAjax(userService.updateUser(user));
    }

    @RequiresPermissions("system:user:resetPwd")
    @Log(title = "重置密码", businessType = BusinessType.UPDATE)
    @GetMapping("/resetPwd/{userId}")
    public String resetPwd(@PathVariable("userId") Long userId, ModelMap mmap) {
        mmap.put("user", userService.selectUserById(userId));
        return prefix + "/resetPwd";
    }

    @RequiresPermissions("system:user:resetPwd")
    @Log(title = "重置密码", businessType = BusinessType.UPDATE)
    @PostMapping("/resetPwd")
    @ResponseBody
    public AjaxResult resetPwdSave(SysUser user) {
        if (StringUtils.isNotNull(user.getUserId()) && SysUser.isAdmin(user.getUserId())) {
            return error("不允许重置超级管理员用户密码");
        }
        user.setSalt(ShiroUtils.randomSalt());
        user.setPassword(passwordService.encryptPassword(user.getUserName(), user.getPassword(), user.getSalt()));
        if (userService.resetUserPwd(user) > 0) {
            if (ShiroUtils.getUserId() == user.getUserId()) {
                ShiroUtils.setSysUser(userService.selectUserById(user.getUserId()));
            }
            return success();
        }
        return error();
    }

    @RequiresPermissions("system:user:bindChannel")
    @Log(title = "绑定渠道", businessType = BusinessType.UPDATE)
    @GetMapping("/bindChannel/{userId}")
    public String bindChannel(@PathVariable("userId") Long userId, ModelMap mmap) {
        SysUser sysUser = userService.selectUserById(userId);
        QueryWrapper<Channel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        List<Channel> channelList = channelService.list(queryWrapper);
        if (StringUtils.isNotEmpty(sysUser.getChannelIds())) {
            List<String> channelIdList = Arrays.asList(sysUser.getChannelIds().split(","));
            for(Channel channel:channelList){
                if(channelIdList.contains(channel.getId().toString())){
                    channel.setSelected(true);
                }
            }
        }
        mmap.put("channel_list", channelList);
        mmap.put("user", sysUser);
        return prefix + "/channel";
    }

    @RequiresPermissions("system:user:bindChannel")
    @Log(title = "绑定渠道", businessType = BusinessType.UPDATE)
    @PostMapping("/bindChannel")
    @ResponseBody
    public AjaxResult bindChannelInfo(SysUser user) {
        if (StringUtils.isNull(user.getUserId())) {
            return error("用户信息错误");
        }
        SysUser sysUser = userService.selectUserById(user.getUserId());
        if(StringUtils.isNotEmpty(sysUser.getHospitalIds())){
            return error("已绑定医院，不能绑定渠道");
        }
        userService.updateUserInfo(user);
        return AjaxResult.success();
    }


    @RequiresPermissions("system:user:bindHospital")
    @Log(title = "绑定医院", businessType = BusinessType.UPDATE)
    @GetMapping("/bindHospital/{userId}")
    public String bindHospital(@PathVariable("userId") Long userId, ModelMap mmap) {
        SysUser sysUser = userService.selectUserById(userId);
        QueryWrapper<Hospital> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        List<Hospital> hospitalList = hospitalService.list(queryWrapper);
        if (StringUtils.isNotEmpty(sysUser.getHospitalIds())) {
            List<String> hospitalIdList = Arrays.asList(sysUser.getHospitalIds().split(","));
            for(Hospital hospital:hospitalList){
                if(hospitalIdList.contains(hospital.getId().toString())){
                    hospital.setSelected(true);
                }
            }
        }
        mmap.put("hospital_list", hospitalList);
        mmap.put("user", sysUser);
        return prefix + "/hospital";
    }

    @RequiresPermissions("system:user:bindHospital")
    @Log(title = "绑定渠道", businessType = BusinessType.UPDATE)
    @PostMapping("/bindHospital")
    @ResponseBody
    public AjaxResult bindHospitalInfo(SysUser user) {
        if (StringUtils.isNull(user.getUserId())) {
            return error("用户信息错误");
        }
        SysUser sysUser = userService.selectUserById(user.getUserId());
        if(StringUtils.isNotEmpty(sysUser.getChannelIds())){
            return error("已绑定渠道，不能绑定医院");
        }
        userService.updateUserInfo(user);
        return AjaxResult.success();
    }

    @RequiresPermissions("system:user:remove")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        try {
            return toAjax(userService.deleteUserByIds(ids));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 校验用户名
     */
    @PostMapping("/checkUserNameUnique")
    @ResponseBody
    public String checkUserNameUnique(SysUser user) {
        return userService.checkUserNameUnique(user.getUserName());
    }

    /**
     * 校验手机号码
     */
    @PostMapping("/checkPhoneUnique")
    @ResponseBody
    public String checkPhoneUnique(SysUser user) {
        return userService.checkPhoneUnique(user);
    }

    /**
     * 校验email邮箱
     */
    @PostMapping("/checkEmailUnique")
    @ResponseBody
    public String checkEmailUnique(SysUser user) {
        return userService.checkEmailUnique(user);
    }

    /**
     * 用户状态修改
     */
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @RequiresPermissions("system:user:edit")
    @PostMapping("/changeStatus")
    @ResponseBody
    public AjaxResult changeStatus(SysUser user) {
        return toAjax(userService.changeStatus(user));
    }
}