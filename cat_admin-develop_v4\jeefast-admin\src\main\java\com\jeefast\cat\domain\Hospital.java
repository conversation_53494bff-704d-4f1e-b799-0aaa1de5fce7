package com.jeefast.cat.domain;

import java.beans.Transient;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 医院表 hospital
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hospital")
public class Hospital extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 医院编码
     */
    @Excel(name = "医院编码")
    private String hospitalCode;
    /**
     * 医院名称
     */
    @Excel(name = "医院名称")
    private String hospitalName;
    /**
     * logo
     */
    @Excel(name = "logo")
    private String logo;
    /**
     * 医院标签
     */
    @Excel(name = "医院标签")
    private String hospitalTags;

    /**
     * 医院标签
     */
    @Excel(name = "医院标签名称")
    @TableField(exist = false)
    private String hospitalTagNames;

    /**
     * 地址
     */
    @Excel(name = "地址")
    private String address;
    /**
     * 医院介绍
     */
    @Excel(name = "医院介绍")
    private String description;
    /**
     * 状态1启用2禁用
     */
    @Excel(name = "状态1启用2禁用")
    private Integer status;
    /**
     * 是否删除0否1是
     */
    @Excel(name = "是否删除0否1是")
    private Integer isDelete;

    /**
     * 推荐科室关联码
     */
    @Excel(name = "推荐科室关联码")
    private String deptRelateCode;

    @TableField(exist = false)
    private Boolean selected;
}
