package com.cat.config;

import cn.hutool.core.util.StrUtil;
import com.getui.push.v2.sdk.ApiHelper;
import com.getui.push.v2.sdk.GtApiConfiguration;
import com.getui.push.v2.sdk.api.PushApi;
import com.getui.push.v2.sdk.api.UserApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class PInit {


    @Autowired
    private GeTuiConfig geTuiConfig;

    @Bean("pushApi")
    public PushApi getPushApi(){
        if(StrUtil.isEmpty(geTuiConfig.getAppId())){
            log.error("====未配置个推appid====");
            return null;
        }
        GtApiConfiguration apiConfiguration = new GtApiConfiguration();
        
        apiConfiguration.setAppId(geTuiConfig.getAppId());
        apiConfiguration.setAppKey(geTuiConfig.getAppKey());
        apiConfiguration.setMasterSecret(geTuiConfig.getMasterSecret());
        
        apiConfiguration.setDomain("https://restapi.getui.com/v2/");
        
        ApiHelper apiHelper = ApiHelper.build(apiConfiguration);
        
        PushApi pushApi = apiHelper.creatApi(PushApi.class);
        return  pushApi;
    }

    @Bean("userApi")
    public UserApi getUserApi() {
        if(StrUtil.isEmpty(geTuiConfig.getAppId())){
            log.error("====未配置个推appid====");
            return null;
        }
        GtApiConfiguration apiConfiguration = new GtApiConfiguration();
        
        apiConfiguration.setAppId(geTuiConfig.getAppId());
        apiConfiguration.setAppKey(geTuiConfig.getAppKey());
        apiConfiguration.setMasterSecret(geTuiConfig.getMasterSecret());
        
        apiConfiguration.setDomain("https://restapi.getui.com/v2/");
        
        ApiHelper apiHelper = ApiHelper.build(apiConfiguration);
        
        UserApi userApi = apiHelper.creatApi(UserApi.class);
        return  userApi;
    }


    public String pageName(){
        return geTuiConfig.getPackageName();
    }
}
