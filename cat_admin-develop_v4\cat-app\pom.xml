<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jeefast</artifactId>
        <groupId>com.jeefast</groupId>
        <version>2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cat-app</artifactId>


    <dependencies>


        <!-- 通用工具-->
        <dependency>
            <groupId>com.jeefast</groupId>
            <artifactId>jeefast-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jeefast</groupId>
            <artifactId>jeefast-system</artifactId>
        </dependency>



        <!-- 腾讯云cos存储 -->
        <dependency>
            <groupId>com.tencent.cloud</groupId>
            <artifactId>cos-sts-java</artifactId>
            <version>3.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>5.6.24</version>
        </dependency>


        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.11.3</version>
        </dependency>

        <dependency>
            <groupId>com.getui.push</groupId>
            <artifactId>restful-sdk</artifactId>
            <version>1.0.0.1</version>
        </dependency>


    </dependencies>


    <repositories>
        <repository>
            <id>bintray-qcloud-maven-repo</id>
            <name>qcloud-maven-repo</name>
            <url>https://dl.bintray.com/qcloud/maven-repo/</url>
            <layout>default</layout>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>


</project>