.m-15 {
  margin: 15px;
}
.mb-3 {
  margin-bottom: 1rem;
}
.p-15 {
  padding: 15px;
}
.flex {
  display: flex;
}
.flex-column {
  flex-direction: column;
}
.flex-center {
  justify-content: center;
  align-items: center;
}
.flex-between {
  justify-content: space-between;
}

input[type=file] {
  display: none;
}

.el-tabs__item.is-active,
.el-link.el-link--primary {
  color: #0071ce !important;
}

.el-button--primary,
.el-radio-button__orig-radio:checked+.el-radio-button__inner,
.el-radio__input.is-checked .el-radio__inner {
  background-color: #0071ce !important;
  border-color: #0071ce !important;
}

.el-scrollbar__wrap {
  /*overflow: unset !important;*/
}

.el-dialog__body {
  padding: 0 20px !important;
}

.avatar-uploader input[type=file]{
  display: none !important;
}

.upload-area input[type=file]{
  display: none !important;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 15px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
}

.upload-area:hover {
  border-color: #1890ff;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #1890ff;
  cursor: pointer;
}

.upload-area-box .el-upload {
  width: 100%;
}

.upload-area-box .el-upload-dragger{
  width: 100%;
  height: 250px;
  background-color: #F7F7F7;
}

.el-upload-hint {
  color: #333333;
  font-size: 15px;
  margin-bottom: 4px;
}

.upload-area-box .el-upload__text {
  margin-bottom: 10px;
}

.tabs-box {
    padding-top: 10px;
}

.tabs-box .el-tabs__nav-scroll {
  padding-left: 15px;
}

.upload-btn {
  padding: 8px 20px;
  font-size: 14px;
}
/* 提示信息区域样式 */
.hint-box {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  margin-top: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}
.hint-box .tip {
  flex: 1;
  padding: 0 15px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-right: 1px solid #e8e8e8;
}
.hint-box .tip:last-child {
  border-right: none;
}
.hint-box .tip-title {
  text-align: left;
  font-size: 14px;
  line-height: 22px;
  color: #262626;
  margin-bottom: 4px;
}
.hint-box .tip-content {
  font-size: 12px;
  text-align: left;
  line-height: 20px;
  color: #8c8c8c;
}

.recover-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #F2F7FF;
  padding: 5px 15px;
  padding-right: 40px;
  color: #262626;
  margin-left: 15px;
  margin-right: 15px;
  border-radius: 4px;
}
.recover-box > div:first-child {
  line-height: 34px;
}

.glyphicon-info-sign {
  color: #326BFC;
}

#articleContent {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 15px;
}

.form-content {
  width: calc(100% - 440px);
  height: calc(100vh - 75px);
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 50px;
  padding-left: 13px;
}

.left-content {
  width: 80%;
}

.label-nol {
  color: #262626;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
}

.tab-label {
  font-size: 16px;
  color: #000;
  margin-bottom: 15px;
}

.reBack {
  font-size: 18px;
  font-weight: bold !important;
  cursor: pointer;
}

.form-group-label {
  width: 130px !important;
}

.moblie-content {
  width: 375px;
  /*height: 667px;*/
  height: 100%;
  border: 1px solid #ddd;
  overflow-y: auto;
  overflow-x: hidden;
  border-radius: 40px;
  padding-top: 0px;
  position: relative;
}

.moblie-content::-webkit-scrollbar {
  display: none;
}

.moblie-content-top {
  position: sticky;
  left: 0;
  top: 0;
  background: #fff;
  z-index: 99;
  margin-bottom: 15px;
  padding: 15px 15px 0 15px;
}

.moblie-content-status-bar {
  width: 100%;
  padding-top: 10px;
}

.moblie-content-status-bar .status-bar-time {
  font-size: 12px;
  color: #000;
}

.status-bar-right img{
  width: 15px;
  height: 15px;
  margin-left: 4px;
}

.moblie-content-header {
  width: 100%;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.moblie-content-header .img-circle {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  margin-left: 8px;
  margin-right: 12px;
}

.moblie-content-header .img-back {
  width: 24px;
  height: 24px;
}

.moblie-content-header i {
  font-size: 18px;
  font-weight: bold;
  color: #000;
}

.moblie-content-header-left,.moblie-content-header-right {
  display: flex;
  align-items: center;
  justify-content: center;
}

.moblie-content-header-right el-icon-more {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  font-size: 18px;
}

.moblie-content-header-left i {
  margin-right: 10px;
}

.moblie-content-header-left span{
  margin-right: 4px;
  display: inline-block;
  width: 96px;
  font-size: 16px;
  color: #1F2229;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.moblie-content-header .followbtn {
  height: 20px;
  line-height: 20px;
  padding: 0px 10px;
  border-radius: 16px;
  color: #4FBACA;
  font-size: 12px;
  color: #4D515C;
  background-color: #F2F3F5;
}

.content-area-title {
  font-size: 18px;
  font-weight: bold;
  color: #1F2229;
  margin-bottom: 6px;
  padding: 0 15px;
}

.content-area-deta {
  font-size: 12px;
  color: #83868F;
  padding: 0 15px;
  margin-bottom: 12px;
}

.rich-text {
  white-space: pre-wrap;
  padding: 0 15px;
}

.rich-text img {
  max-width:100%;
  height:auto;
}

.rich-text p{
  word-wrap: break-word;
}

.image-area {
  position: relative;
  margin-bottom: 16px;
}

.initial-box {
  position: absolute;
  right: 16px;
  bottom: 14px;
  padding: 2px 10px;
  border-radius: 11px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #D7D8DB;
  font-size: 12px;
  z-index: 2;
}

.content-area {
  min-height: 240px;
  border-bottom: 1px solid #F6F6F6;
}

.foot-bar {
  padding: 13px 15px;
}

.foot-bar-left-btn {
  margin-right: 24px;
}

.foot-bar-left-btn:last-child {
  margin-right: 0;
}

.foot-bar-left-btn img{
  width: 16px;
  height: 16px;
}

.foot-bar-left-btn span{
  color: #4D515C;
  font-size: 12px;
  margin-left: 2px;
}

.gap {
  width: 100%;
  height: 16px;
  background-color: #F2F3F5;
}

.shop-goods-area {
  width: 100%;
  border-radius: 6px;
  background-color: #F7F7F7;
  padding: 8px;
  margin-top: 12px;
  margin-bottom: 12px;
  color: #333;
}

.shop-goods-area-left {
  width: 40px;
  height: 40px;
  margin-right: 8px;
  border-radius: 8px;
  overflow: hidden;
}

.shop-goods-area-left img {
  width: 100%;
  height: 100%;
}

.shop-goods-area-right {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.shop-goods-area-right-content {
  font-size: 10px;
}

.review-area {
  margin-top: 10px;
  padding: 0 15px;
}

.review-area i{
  color: #4FBACA;
  line-height: 35px;
  margin-right: 4px;
  font-size: 14px;
}

.review-area-top {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.review-area-top-left {
  font-size: 18px;
  color: #1F2229;
}

.review-area-top-line {
  width: 1px;
  height: 9px;
  background-color: #D7D8DB;
  margin: 0 11px;
}

.review-area-top-right {
  font-size: 14px;
  color: #83868F;
}

.comment-box {
  width: 100%;
  padding: 15px;
  position: sticky;
  left: 0;
  bottom: 0;
  background-color: #fff;
  box-shadow: 0px -1px 0px 0px #f2f3f5;
}

.comment-input {
  width: 100%;
  border-radius: 18px;
  height: 36px;
  line-height: 36px;
  background: #F2F3F5;
  font-size: 14px;
  color: #83868F;
}

.comment-input img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 12px;
}

.comment-btn {
  height: 35px;
  line-height: 35px;
  margin-right: 10px;
  font-size: 12px;
}

.review-list {
  width: 100%;
  display: flex;
  padding: 12px 0px;
}

.review-list-left {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
}

.review-userName {
  color: #1F2229;
  font-size: 14px;
  margin-bottom: 8px;
}

.review-content {
  color: #4D515C;
  font-size: 14px;
  margin-bottom: 6px;
}

.review-time {
  color: #BABCC2;
  font-size: 12px;
}

.review-list-left img{
  width: 100%;
  height: 100%;
}

.review-list-middle {
  flex: 1;
  margin: 0 8px;
}

.review-list-right img{
  width: 12px;
  height: 12px;
}

.review-list-right span{
  color: #83868F;
  font-size: 12px;
  margin-left: 2px;
}

.form-horizontal {
  max-height: 650px;
  overflow-y: auto;
  overflow-x: hidden;
}

.image-box {
  display: flex;
  margin-left: 10px;
  margin-top: 8px;
  margin-bottom: 25px;
}

.upload-btn {
  width: 100px;
  height: 100px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #f3f3f3;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  cursor: pointer;
}

.image-edit-box {
  border: 1px solid #ddd;
  border-radius: 6px;
  height: 150px;
  margin-left: 20px;
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
  overflow-y: auto;
}

.image-box-item {
  width: 100px;
  height: 100px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 6px;
  overflow: hidden;
  cursor:move;
  position: relative;
}

.image-box-item .image-wrapper{
  border-radius: 6px;
  overflow: hidden;
}

.btn-delete {
  position: absolute;
  right: 0;
  top: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 4px 0 4px;
  background: rgba(0, 0, 0, 0.6);
  width: 20px;
  height: 20px;
  display: none;
  color: #fff;
}

.btn-group {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 28px;
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  display: none;
}

.btn-group .btn-edit{
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
  font-size: 11px;
}

.btn-group .line {
  height: 14px;
  width: 1px;
  background: #fff;
}

.btn-group .btn-reupload {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
  font-size: 11px
}

.btn-reupload-upload {
  width: 100%;
  text-align: center;
  margin-top: 10px;
}

.btn-reupload-text {
  color: #0d8ddb;
}

.image-box-item:hover .btn-delete {
  display: flex;
}

.image-box-item:hover .btn-group {
  display: flex;
}

.videoBox {
  margin-bottom: 20px;
}

.videoBox .videoUpload {
  background: #fafafa;
  border-radius: 6px;
  display: flex;
  align-items: center;
  padding: 17px 30px;
  width: 100%;
  margin-bottom: 20px;
}

.videoUpload-left {
  flex: 1;
  color: #000000cc;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
}

.videoUpload-right {
  font-size: 14px;
  color: #ff2442;
  cursor: pointer;
}

.videoUpload-left-operator {
  display: flex;
  margin-top: 6px;
}

.videoSuccess {
  font-size: 14px;
  color: #000000cc;
  display: flex;
}

.videoSuccess i {
  width: 22px;
  height: 22px;
  margin-top: 5px;
  color: #00B848;
}

.videoInfo {
  font-size: 14px;
  color: #00000073;
  margin-left: 24px;
}

.cover-preview {
  width: 100px;
  height: 100px;
  position: relative;
  margin-top: 15px;
  border-radius: 10px;
  overflow: hidden;
}

.cover-preview .preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-preview .cover-plus {
  width: 100%;
  height: 100%;
  position: absolute;
  color: #fff;
  top: 0;
  left: 0;
  backdrop-filter: blur(1px);
  background: rgba(0, 0, 0, .5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.cover-preview .cover-plus i{
  font-size: 18px;
  margin-bottom: 8px;
}

.bottom-btn-box {
  width: 100%;
  padding: 8px 12px;
  position: fixed;
  left: 0;
  bottom: 0;
  background-color: white;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  z-index: 99;
  display: flex;
  justify-content: flex-end;
}

.bottom-btn-box button {
  margin-left: 10px;
}

.input-group-select {
  display: flex;
}

.input-group-select > select {
  width: 100px;
}

.input-group-select > input {
  width: 200px;
  margin-left: 10px;
}

.dateTime {
  display: flex;
  margin-left: 15px;
}

.dateTime .line {
  margin: 0 5px;
  line-height: 34px;
}

.contentEditBox {
  width: 80%;
  margin-left: 15px;
}

.contentBtn {
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: none;
  border-radius: 6px;
  color: #262626;
  cursor: pointer;
  display: flex;
  font-size: 14px;
  height: 28px;
  padding: 4px 12px;
  width: 74px;
  margin-right: 10px;
}

.contentBtn img {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.el-popover,.el-image-viewer__wrapper {
  z-index: 99999 !important;
}

.emojiBox {
  display: flex;
  flex-wrap: wrap;
}

.emojiItem {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s;
}

.emojiItem:hover {
  background-color: #f5f5f5;
  border-radius: 4px;
}

.rightPreviewArea {
  flex: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  height: calc(100vh - 75px);
  overflow-y: auto;
}

.other-box {
  transition: all 0.3s;
}

.topic-input-box {
  width: 100%;
  height: 40px;
  line-height: 40px;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  padding: 0 15px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.topic-item {
  display: inline-flex;
  align-items: center;
  max-width: 100%;
  margin: 2px 0 2px 6px;
  text-overflow: ellipsis;
  background: #f0f2f5;
  color: #909399;
  height: 24px;
  padding: 0 8px;
  line-height: 22px;
  font-size: 12px;
  border-width: 1px;
  border-style: solid;
  border-color: #f0f1f5;
  border-radius: 4px;
  white-space: nowrap;
}

.topic-item:first-child {
  margin-left: 0 !important;
}

.topic-close {
  font-size: 10px;
  height: 14px;
  width: 14px;
  vertical-align: middle;
  cursor: pointer;
  background-color: #C0C4CC;
  color: #FFF;
  border-radius: 50%;
  display: flex !important;
  align-items: center;
  justify-content: center;
  margin-left: 3px;
}

.topic-close:hover {
  background-color: #909399;
}

.topic-placeholder {
  font-size: 14px;
  color: #CDD0D7;
}

.topic-container {
  width: 100%;
  height: 300px;
  display: flex;
  padding-bottom: 40px;
}

.topic-container-left {
  width: 200px;
  overflow-y: auto;
}

.topic-container-right {
  flex: 1;
  overflow-y: auto;
}

.topic-left-item {
  align-items: center;
  padding: 0 20px 0 20px;
  height: 34px;
  line-height: 34px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
  margin: 0 15px;
  margin-bottom: 5px;
  border-radius: 4px;
}

.topic-left-item:hover {
  background-color: #F5F7FA;
}

.topic-lfet-active {
  background-color: #0071ce !important;
  color: #fff !important;
}

.topic-empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/*视频移动端样式*/
.moblie-video-content {
  width: 375px;
  /*height: 667px;*/
  height: 100%;
  overflow-y: auto;
  border-radius: 24px;
  position: relative;
  background-color: black;
}

.moblie-video-content-top {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  width: 100%;
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.7));
  height: 100px;
}

.moblie-video-content-header {
  padding: 5px 15px 15px 15px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.moblie-video-content-header-left i {
  font-size: 15px;
  color: #fff;
}

.moblie-video-content-header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.moblie-video-content-header-right img {
  width: 15px;
  height: 15px;
  margin-left: 10px;
}

.moblie-video-content-body {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 0;
  top: 0;
}

.moblie-video-content-body video {
  width: 100%;
  height: auto !important;
  object-fit: fill;
}

/*全屏按钮*/
.moblie-video-content-body video::-webkit-media-controls-fullscreen-button {
  display: none;
}
/*播放按钮*/
.moblie-video-content-body video::-webkit-media-controls-play-button {
  display: none;
}
/*音量按钮*/
.moblie-video-content-body video::-webkit-media-controls-mute-button {
  display: none;
}
/*音量的控制条*/
.moblie-video-content-body video::-webkit-media-controls-volume-slider {
  display: none;
}
/*观看的当前时间*/
.moblie-video-content-body video::-webkit-media-controls-current-time-display {
  display: none;
}
/*剩余时间*/
.moblie-video-content-body video::-webkit-media-controls-time-remaining-display {
  display: none;
}

.moblie-video-content-body .playBox {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.moblie-video-content-body .video-stop-play {
  width: 56px;
  height: 56px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  cursor: pointer;
}

.moblie-video-content-footer {
  width: calc(100% - 10px);
  min-height: 150px;
  position: absolute;
  left: 10px;
  bottom: 50px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.moblie-video-content-footer .shop-goods-card {
  width: 120px;
  height: 40px;
  background: rgba(31,34,41,0.60);
  border: 1px solid #4d515c;
  border-radius: 8px;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.moblie-video-content-footer .shop-goods-card-left {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  overflow: hidden;
  margin-left: 4px;
  margin-right: 8px;
}

.moblie-video-content-footer .shop-goods-area-right-title {
  font-size: 12px;
  color: #F2F3F5;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.moblie-video-content-footer .user-info {
  width: 80%;
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
}

.moblie-video-content-footer .user-info img {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  margin-right: 12px;
}

.moblie-video-content-footer .user-info-name {
  width: 90px;
  font-size: 14px;
  color: #fff;
  margin-right: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.moblie-video-content-footer .user-info-date {
  font-size: 12px;
  color: #d7d8db;
  margin-top: 3px;
}

.moblie-video-content-footer .user-info-follow {
  width: 56px;
  height: 20px;
  line-height: 20px;
  background: rgba(255,255,255,0.10);
  border: 1px solid rgba(215,216,219,0.50);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #ffffff;
}

.moblie-video-content-title {
  width: 80%;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 12px;
}

.moblie-video-content-title-left {
  white-space: nowrap;
  color: #fff;
}

.moblie-video-content-title-right {
  font-size: 12px;
  color: #333;
  padding: 2px 6px;
  border-radius: 3px;
  background-color: #ededed;
  margin-left: 4px;
}

.moblie-video-content-content {
  width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 控制显示的行数 */
  -webkit-box-orient: vertical;
  color: #fff;
  font-size: 12px;
  padding: 0 !important;
}

.recommended {
  width: 96px;
  height: 24px;
  background: rgba(31,34,41,0.60);
  border: 1px solid #4d515c;
  border-radius: 13px;
  font-size: 12px;
  color: #babcc2;
  margin-top: 12px;
}

.moblie-video-content-right-bar {
  position: absolute;
  right: 14px;
  bottom: 50px;
}

.bar-list {
    margin-bottom: 16px;
}

.bar-list:last-child {
  margin-bottom: 0px;
}

.bar-img {
  width: 44px;
  height: 44px;
}

.bar-text {
  font-size: 12px;
  color: #fff;
  margin-top: 8px;
  text-align: center;
}
/*视频移动端样式*/

/*富文本框样式*/
#editor—wrapper {
  border: 1px solid #DCDFE6;
  z-index: 100;
  border-radius: 4px;
  overflow: hidden;
}
#toolbar-container {
  border-bottom: 1px solid #ccc;
}
#editor-container {
  height: 300px;
  font-size: 16px;
}
#editor-container .w-e-modal button {
  display: flex !important;
  align-items: center !important;
}
#editor—wrapper.w-e-full-screen-container {
  width: calc(100% - 440px) !important;
  height: calc(100% - 56px) !important;
}
.word-counter {
  font-size: 12px;
  text-align: right;
}
/*富文本框样式*/

/* 图片编辑弹窗样式 */
.cropper-box {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 30px 0;
}

.cropper-area {
  width: 600px;
  height: 300px;
}

.cropper-area2 {
  width: 580px;
  height: 300px;
}

#cropperImage {
  width: 100%;
  height: 100%;
}

.cropper-operation {
  flex: 1;
  border-left: 1px solid rgba(0,0,0,0.05); 
}

.cropper-operation-title {
  font-size: 18px;
  padding: 20px;
  padding-top: 0;
  color: #262626;
}

.select-area {
  display: grid;
  grid-template-columns: repeat(2, 50%);
  width: 100%;
  height: 220px;
}

.select-area .option {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-area .option:hover {
  background: rgba(0, 0, 0, 0.03);
}

.graph {
  border: 1.5px solid #8a8f93;
  border-radius: 3px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  max-height: 100px;
  max-width: 100px;
  min-width: 60px;
  min-height: 60px;
}

/* 图片编辑弹窗样式 */

/* 设置封面弹窗样式 */
.cover-container {
  min-width: 540px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0,0,0,0.1);
}
.crop-ratio-list-container {
  margin-top: 12px;
}

.crop-title {
  color: #858585;
  font-size: 12px;
  line-height: 20px;
  margin-bottom: 8px;
  text-align: left;
}

.crop-ratio-list {
  width: 100%;
  display: flex;
  align-items: center;
}

.crop-ratio-item {
  cursor: pointer;
  border: 1.5px solid #8a8f93;
  color: #333;
  margin-right: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 3px;
  background: rgba(216, 224, 227, 0.08);
}

/* 时间轴容器 */
.timeline-controls {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
}

/* 帧预览画布 */
.frame-preview {
  display: block;
  margin: 10px 0;
  border: 2px solid #409EFF;
  border-radius: 4px;
  width: 100px;
  height: 70px;
}

.timeline-container {
  width: 540px;
  margin: 0 auto;
}

.thumbnail-box {
  padding: 5px;
  box-sizing: border-box;
  margin: 14px 0 30px 0;
  background: #f5f8fa;
  border-radius: 4px;
  display: flex;
  position: relative;
  width: max-content;
  /* 居中 */
  left: 50%;
  transform: translateX(-50%);
}

.thumbnail-item img {
  width: 27px;
  user-select: none;
}

.point {
  width: 12px;
  height: 100%;
  line-height: 100%;
  text-align: center;
  background: #fff;
  border: 1px solid #409eff;
  box-sizing: border-box;
  border-radius: 2px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  cursor: pointer;
  user-select: none;
}

.point span {
  position: absolute;
  top: 50%;
  margin-top: -6px;
  display: inline-block;
  width: 2px;
  height: 12px;
  background: #d8d8d8;
}

.point .span1 {
  left: 2px;
}

.point .span2 {
  left: 6px;
}

.point .time {
  position: absolute;
  bottom: -20px;
  left: -12px;
  user-select: none;
  white-space: nowrap;
}

  /* 设置封面弹窗样式 */
.graphActive{
  color: #409EFF;
  border-color: #409EFF;
}

.el-slider__runway {
  /*height: 0 !important;*/
  margin: 0 !important;
}

.cropper-bg {
  background-image: none !important;
}

.cropper-modal {
  background-color: rgba(255,255,255,0.6) !important;
}

.infinite-list {
  overflow:auto;
  max-height: 250px;
}