package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                                                                            import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 用户信息表 user_info
 *
 * <AUTHOR>
 * @date 2019-12-19
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_info")
public class UserInfo extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 用户ID */
    @TableId(value = "user_id", type = IdType.UUID)
    private String userId;
    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;
    /** 手机号 */
    private String mobile;
    /** 性别(1：男、2：女) */
    @Excel(name = "性别(1：男、2：女)")
    private String sex;
    /** 头像 */
    private String avatarUrl;
    /** 等级 */
    @Excel(name = "等级")
    private Integer level;
    /** 年龄 */
    @Excel(name = "年龄")
    private Integer age;
    /** 用户简介 */
    private String profiles;
    /** 省 */
    private String province;
    /** 市 */
    private String city;
    /** 经纬度 */
    private String gps;
    /** 魅力值 */
    @Excel(name = "魅力值")
    private Integer charm;
    /** 罐头克数(g) */
    @Excel(name = "罐头克数(g)")
    private Long canNumber;
    /** 微信ID */
    private String openId;
    /** 是否官V用户(1：官V ，2：未认证) */
    @Excel(name = "是否官V用户(1：官V ，2：未认证)")
    private String approve;
    /** 国家 */
    private String country;
    /** 语言 */
    private String language;
    /** 关注人数 */
    @Excel(name = "关注人数")
    private Integer attentionCount;
    /** 粉丝人数 */
    @Excel(name = "粉丝人数")
    private Integer fansCount;
    /** 动态数 */
    @Excel(name = "动态数")
    private Integer dynamicCount;
    /** 问答数 */
    @Excel(name = "问答数")
    private Integer questionCount;
    /** 获赞数 */
    @Excel(name = "获赞数")
    private Integer praiseCount;

    private String unionId;
    /** 是否禁言：0：否、1：是 */
    private String isMute;
    /** 猫币 */
    private Double catMoney;
    /** 用户号 **/
    private String userNum;
    /**
     * 开始创建时间
     */
    @TableField(exist = false)
    private String beginCreateDate;
    /**
     * 结束创建时间
     */
    @TableField(exist = false)
    private String endCreateDate;
}
