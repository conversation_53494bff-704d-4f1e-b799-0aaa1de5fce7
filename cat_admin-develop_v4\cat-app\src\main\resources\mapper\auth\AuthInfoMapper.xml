<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.auth.mapper.AuthInfoMapper">
    <!--
    <resultMap type="AuthInfo" id="AuthInfoResult">
        <result property="id"    column="id"    />
        <result property="authType"    column="auth_type"    />
        <result property="authDesc"    column="auth_desc"    />
        <result property="email"    column="email"    />
        <result property="mobile"    column="mobile"    />
        <result property="link"    column="link"    />
        <result property="name"    column="name"    />
        <result property="truename"    column="truename"    />
        <result property="telegram"    column="telegram"    />
        <result property="skype"    column="skype"    />
        <result property="createDate"    column="create_date"    />
    </resultMap>
    -->


    <select id="getList" resultType="com.cat.modules.auth.entity.AuthInfo">
        select t1.*,t2.status,t2.user_id,t2.truename auther,t2.description
        from auth_info t1
        left join auth_audit_info t2 on t2.auth_id = t1.id
        where 1=1
        <if test="condi.authType != null and condi.authType != ''">
            and t1.auth_type = #{condi.authType}
        </if>

        <if test="condi.skype != null and condi.skype != ''">
            and t1.skype = #{condi.skype}
        </if>

        <if test="condi.telegram != null and condi.telegram != ''">
            and t1.telegram = #{condi.telegram}
        </if>

        <if test="condi.status != null and condi.status != ''">
            and t2.status = #{condi.status}
        </if>



    </select>

</mapper>

