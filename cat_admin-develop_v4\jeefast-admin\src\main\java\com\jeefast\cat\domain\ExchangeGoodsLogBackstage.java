package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                            import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 兑换商品日志表 exchange_goods_log
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("exchange_goods_log")
public class ExchangeGoodsLogBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /** 价格 */
    @Excel(name = "价格")
    private Double price;
    /** 创建时间 */
    /*@Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;*/
    /** 兑换人id */
    @Excel(name = "兑换人id")
    private String userId;
    /** 兑换商品id */
    @Excel(name = "兑换商品id")
    private String goodsId;
    /** 兑换商品id */
    @Excel(name = "兑换商品名")
    private String goodsName;
    /** 支付方式 */
    @Excel(name = "支付方式")
    private Long payType;
    /** 备注 */
    @Excel(name = "备注")
    private String remark;
}
