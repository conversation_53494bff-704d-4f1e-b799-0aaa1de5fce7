package com.cat.modules.complain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ComplainInfo extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "complain_id",type = IdType.UUID)
    @TableField("complain_id")
    private String complainId;

    
    @TableField("user_id")
    private String userId;

    
    @TableField("business_id")
    private String businessId;

    
    @TableField("business_type")
    private String businessType;

    
    @TableField("type")
    private String type;

    
    @TableField("depict")
    private String depict;

    
    @TableField("phone")
    private String phone;
    
    @TableField("imgs")
    private String imgs;



}
