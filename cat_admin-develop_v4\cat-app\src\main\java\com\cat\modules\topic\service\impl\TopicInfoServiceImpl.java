package com.cat.modules.topic.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.content.entity.ContentTopicRel;
import com.cat.modules.content.service.IContentTopicRelService;
import com.cat.modules.topic.entity.TopicInfo;
import com.cat.modules.topic.mapper.TopicInfoMapper;
import com.cat.modules.topic.service.ITopicInfoService;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Slf4j
@Service

public class TopicInfoServiceImpl extends ServiceImpl<TopicInfoMapper, TopicInfo> implements ITopicInfoService {
    @Autowired
    private TopicInfoMapper topicInfoMapper;
    @Autowired
    private IContentTopicRelService contentTopicRelService;

    @Override
    public List<TopicInfo> getByBusinessTopicList(String businessId, String type) {
        return topicInfoMapper.getByBusinessTopicList(businessId,type);
    }

    @Override
    public List<String> imageList(String topicId) {
        return topicInfoMapper.imageList(topicId);
    }

    @Override
    public boolean addTopicRel(String businessId,String businessType,List<String> topics) {
        boolean flag = false;
        for(int i=0;i<topics.size();i++){
            TopicInfo topicInfo = this.getById(topics.get(i));
            if(topicInfo==null){
                log.error("添加动态话题不存在");
                return false;
            }
            UpdateWrapper<TopicInfo> updateWrapper = new UpdateWrapper<TopicInfo>();
            updateWrapper.setSql("cite_count = cite_count+1");
            updateWrapper.eq("topic_id",topics.get(i));
            flag = this.update(updateWrapper);
            if(!flag){
                log.error("【发布动态话题引用数累加失败】Object="+updateWrapper);
            }

            ContentTopicRel contentTopicRel = new ContentTopicRel();
            contentTopicRel.setBusinessId(businessId);
            contentTopicRel.setBusinessType(businessType);
            contentTopicRel.setTopicId(topics.get(i));
            contentTopicRel.setTopicName(topicInfo.getTopicName());
            flag = contentTopicRelService.save(contentTopicRel);
        }
        return flag;
    }

    @Override
    @Transactional
    public boolean delContentTopic(String businessId, String businessType) {
        boolean flag = false;
        List<ContentTopicRel> topics = contentTopicRelService.list(new QueryWrapper<ContentTopicRel>()
                .eq("business_id",businessId)
                .eq("business_type",businessType)
        );
        contentTopicRelService.remove(new QueryWrapper<ContentTopicRel>()
                .eq("business_id",businessId)
                .eq("business_type",businessType)
        );
        for(int i=0;i<topics.size();i++){
            UpdateWrapper<TopicInfo> updateWrapper = new UpdateWrapper<TopicInfo>();
            updateWrapper.setSql("cite_count = cite_count-1");
            updateWrapper.eq("topic_id",topics.get(i));
            flag = this.update(updateWrapper);
            if(!flag){
                log.error("【发布动态话题引用数累加失败】Object="+updateWrapper);
            }
        }
        return flag;
    }
}
