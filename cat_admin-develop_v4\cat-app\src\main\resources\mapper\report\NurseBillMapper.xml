<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeefast.cat.mapper.NurseBillMapper">
    <!--
    <resultMap type="DynamicInfoBackstage" id="DynamicInfoBackstageResult">
        <result property="dynamicId"    column="dynamic_id"    />
        <result property="petId"    column="pet_id"    />
        <result property="userId"    column="user_id"    />
        <result property="content"    column="content"    />
        <result property="addr"    column="addr"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="praiseCount"    column="praise_count"    />
        <result property="commentCount"    column="comment_count"    />
        <result property="type"    column="type"    />
        <result property="source"    column="source"    />
        <result property="createDate"    column="create_date"    />
        <result property="coverImage"    column="cover_image"    />
        <result property="height"    column="height"    />
        <result property="width"    column="width"    />
        <result property="mediaCount"    column="media_count"    />
        <result property="weight"    column="weight"    />
    </resultMap>
    -->


    <select id="nurseBillList" resultType="cn.hutool.core.map.CamelCaseMap">
        select ele.*,
               group_concat(alias.alias_name)    as alias_name_list,
               group_concat(alias.project_label) as project_alias
        from nurse_bill_ele as ele
                 left join nurse_bill_ele_alias as alias on ele.ele_id = alias.ele_id
        group by ele.ele_id
        having ${ew.sqlSegment}
    </select>

    <update id="moveEleDetail">
        update nurse_bill_detail set ele_id =#{eleId}, ele_name= #{eleName}
        <where>
            ${ew.sqlSegment}
        </where>
    </update>


    <update id="moveEleAliasToNewEle">
        update nurse_bill_ele_alias set ele_id=#{eleId}
        <where>
            ${ew.sqlSegment}
        </where>
    </update>

    <insert id="insertEleAlias">
        insert into nurse_bill_ele_alias (alias_name, project_label, ele_id)
        values (#{aliasName}, #{projectLabel}, #{eleId})
    </insert>

    <select id="selectAliasByEleId" resultType="com.jeefast.cat.common.EleAliasInfo">
        select
            GROUP_CONCAT(DISTINCT alias_name SEPARATOR ';')    AS aliasName,
            GROUP_CONCAT(DISTINCT project_label SEPARATOR ';') AS projectLabel
        from nurse_bill_ele_alias
        where ele_id = #{eleId}
    </select>

    <delete id="deleteAliasByEleId">
        delete from nurse_bill_ele_alias where ele_id = #{eleId}
    </delete>
</mapper>

