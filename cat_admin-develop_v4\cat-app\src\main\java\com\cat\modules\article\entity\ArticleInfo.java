package com.cat.modules.article.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ArticleInfo extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "article_id",type = IdType.UUID)
    @TableField("article_id")
    private String articleId;

    
    @TableField("cover_image")
    private String coverImage;

    
    @TableField("title")
    private String title;

    
    @TableField("type")
    private String type;

    
    @TableField("state")
    private String state;

    
    @TableField("summary")
    private String summary;

    
    @TableField("content")
    private String content;

    
    @TableField("visit_count")
    private Integer visitCount;

    
    @TableField("comment_count")
    private Integer commentCount;

    @TableField("praise_count")
    private Integer praiseCount;

    
    @TableField("is_recommend")
    private String isRecommend;

    
    @TableField("sort_on")
    private Integer sortOn;

    
    @TableField("source_url")
    private String sourceUrl;

    
    @TableField("author")
    private String author;

    
    @TableField("user_id")
    private String userId;

    
    @TableField("weight")
    private Integer weight;

    
    @TableField("width")
    private Integer width;
    
    @TableField("height")
    private Integer height;


    
    @TableField("is_check")
    private String isCheck;

}
