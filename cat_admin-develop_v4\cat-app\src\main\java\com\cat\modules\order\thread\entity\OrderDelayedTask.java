package com.cat.modules.order.thread.entity;

import java.io.Serializable;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

public class OrderDelayedTask implements Delayed, Serializable {

    private String orderJson ;
    private long start = System.currentTimeMillis();
    private long time ;


    public OrderDelayedTask(String orderJson, long time) {
        super();
        this.orderJson = orderJson;
        this.time = time;
    }

    public String getOrderJson() {
        return orderJson;
    }

    public void setOrderJson(String orderJson) {
        this.orderJson = orderJson;
    }

    @Override
    public String toString() {
        return "OrderDelayedTask [orderJson=" + orderJson + ", start=" + start + ", time=" + time + "]";
    }



    
    @Override
    public long getDelay(TimeUnit unit) {
        return unit.convert((start+time) - System.currentTimeMillis(),TimeUnit.MILLISECONDS);
    }

    
    @Override
    public int compareTo(Delayed o) {
        OrderDelayedTask o1 = (OrderDelayedTask) o;
        return (int) (this.getDelay(TimeUnit.MILLISECONDS) - o.getDelay(TimeUnit.MILLISECONDS));
    }

}
