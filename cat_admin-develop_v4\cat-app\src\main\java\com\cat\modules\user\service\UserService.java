package com.cat.modules.user.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cat.modules.user.entity.User;
import com.cat.util.DataRow;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;

import java.util.Date;
import java.util.List;


public interface UserService extends IService<User> {

    
    public String getUserId();

    
    List<String> randUserAvatar(int i);

    
    String getRandPetUserId();

    
    String getByOpenId(String toUserId);


    
    DataRow getByIdPushInfo(String userId);

    Integer updateFamilyUserNewDynamicNum(String familyUserId, Integer diffNum, Date dynamicDate);
}
