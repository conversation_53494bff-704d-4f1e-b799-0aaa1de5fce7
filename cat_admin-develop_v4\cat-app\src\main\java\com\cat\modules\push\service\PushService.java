package com.cat.modules.push.service;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cat.modules.push.entity.PushUniDoing;
import com.getui.push.v2.sdk.ApiHelper;
import com.getui.push.v2.sdk.GtApiConfiguration;
import com.getui.push.v2.sdk.api.PushApi;
import com.getui.push.v2.sdk.api.UserApi;
import com.getui.push.v2.sdk.common.ApiResult;
import com.getui.push.v2.sdk.dto.req.Audience;
import com.getui.push.v2.sdk.dto.req.Settings;
import com.getui.push.v2.sdk.dto.req.Strategy;
import com.getui.push.v2.sdk.dto.req.message.PushChannel;
import com.getui.push.v2.sdk.dto.req.message.PushDTO;
import com.getui.push.v2.sdk.dto.req.message.PushMessage;
import com.getui.push.v2.sdk.dto.req.message.android.AndroidDTO;
import com.getui.push.v2.sdk.dto.req.message.android.GTNotification;
import com.getui.push.v2.sdk.dto.req.message.android.ThirdNotification;
import com.getui.push.v2.sdk.dto.req.message.android.Ups;
import com.getui.push.v2.sdk.dto.req.message.ios.Alert;
import com.getui.push.v2.sdk.dto.req.message.ios.Aps;
import com.getui.push.v2.sdk.dto.req.message.ios.IosDTO;
import com.getui.push.v2.sdk.dto.res.CidStatusDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class PushService {


    public Map<String, Object> push(PushUniDoing push){
        Map<String, Object> map = new HashMap<>();
        Boolean userOnline = getUserOnline(push.getUserApi(),push.getCid());
        if (ObjectUtil.isNull(userOnline)){
            log.error("请求个推服务器发送错误");
        }
        if (userOnline){ 
            map= gtPushTransmission(push);
        }else { 
            map=  BusinessPush(push);
        }
        return map;
    }

    
    public Boolean getUserOnline(UserApi userApi, String cid){
        Set<String> cids = new HashSet<String>();
        cids.add(cid);
        
        ApiResult<Map<String, CidStatusDTO>> apiResult = userApi.queryUserStatus(cids);
        if (apiResult.isSuccess()) {
            Map<String, CidStatusDTO> data = apiResult.getData();
            CidStatusDTO cidStatusDTO = data.get(cid);
            log.info("当前cid:{}查询在线状态",cidStatusDTO.getStatus());
            return cidStatusDTO.getStatus().equals("online")?true:false;
        }
        log.error("当前cid:{}查询在线状态出现错误 ,code:{},msg:",cid,apiResult.getCode(),apiResult.getMsg());
        return  null;
    }

    
    public Map<String, Object> BusinessPush(PushUniDoing pushUni){
        
        PushDTO<Audience> pushDTO = new PushDTO<Audience>();
        
        pushDTO.setRequestId(System.currentTimeMillis() + "");
        
        
        Strategy strategy=new Strategy();
        strategy.setDef(1);
        Settings settings=new Settings();
        settings.setStrategy(strategy);
        pushDTO.setSettings(settings);
        settings.setTtl(3600000);
        PushChannel pushChannel = new PushChannel();
        if ("ios".equals(pushUni.getClientType())){
            
            Alert alert=new Alert();
            alert.setTitle(pushUni.getTitle());
            alert.setBody(pushUni.getContent());
            Aps aps = new Aps();
            
            
            aps.setContentAvailable(0);
            aps.setSound("default");
            aps.setAlert(alert);
            IosDTO iosDTO = new IosDTO();
            iosDTO.setAps(aps);
            iosDTO.setType("notify");
            iosDTO.setPayload(pushUni.getPayload());
            pushChannel.setIos(iosDTO);
        }else {
            AndroidDTO androidDTO = new AndroidDTO();
            Ups ups = new Ups();
            ThirdNotification notification1 = new ThirdNotification();;
            ups.setNotification(notification1);
            notification1.setTitle(pushUni.getTitle());
            notification1.setBody(pushUni.getContent());
            notification1.setClickType("intent");
            notification1.setIntent("intent:#Intent;action=android.intent.action.oppopush;launchFlags=0x14000000;"
                    +"component=" +pushUni.getPackageName() +"/io.dcloud.PandoraEntry;S.UP-OL-SU=true;S.title="+pushUni.getTitle()
                    +";S.content="+pushUni.getContent()
                    +";S.payload="+ pushUni.getPayload()
                    +";end");
            
            ups.addOption("HW", "/message/android/notification/badge/class", "io.dcloud.PandoraEntry");
            ups.addOption("HW", "/message/android/notification/badge/add_num", 1);
            ups.addOption("HW", "/message/android/notification/importance", "HIGH");
            ups.addOption("VV","classification",1);
            androidDTO.setUps(ups);
            pushChannel.setAndroid(androidDTO);
        }
        pushDTO.setPushChannel(pushChannel);
        
        PushMessage pushMessage = new PushMessage();
        pushDTO.setPushMessage(pushMessage);
        pushMessage.setTransmission(pushUni.getPayload());
        
        Audience audience = new Audience();
        pushDTO.setAudience(audience);
        audience.addCid(pushUni.getCid());
        
        ApiResult<Map<String, Map<String, String>>> apiResult = pushUni.getPushApi().pushToSingleByCid(pushDTO);
        return getMapValue(apiResult,pushUni.getCid());
    }

    
    public  Map<String, Object>  gtPush(PushUniDoing pushUni){
        PushDTO<Audience> pushDTO = new PushDTO<Audience>();
        
        pushDTO.setRequestId(System.currentTimeMillis() + "");
        PushMessage pushMessage = new PushMessage();
        pushDTO.setPushMessage(pushMessage);
        GTNotification notification = new GTNotification();
        pushMessage.setNotification(notification);
        notification.setTitle(pushUni.getTitle());
        notification.setBody(pushUni.getContent());
        notification.setClickType("payload");
        notification.setPayload(pushUni.getPayload());
        Audience audience = new Audience();
        pushDTO.setAudience(audience);
        audience.addCid(pushUni.getCid());
        
        ApiResult<Map<String, Map<String, String>>> apiResult = pushUni.getPushApi().pushToSingleByCid(pushDTO);
        return  getMapValue(apiResult,pushUni.getCid());
    }


    
    public  Map<String, Object> gtPushTransmission(PushUniDoing pushUni){
        PushDTO<Audience> pushDTO = new PushDTO<Audience>();
        
        pushDTO.setRequestId(System.currentTimeMillis() + "");
        PushMessage pushMessage = new PushMessage();
        pushDTO.setPushMessage(pushMessage);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("title",pushUni.getTitle());
        jsonObject.put("content",pushUni.getContent());
        jsonObject.put("extras",pushUni.getPayload());
        pushMessage.setTransmission(JSON.toJSONString(jsonObject));
        Audience audience = new Audience();
        pushDTO.setAudience(audience);
        audience.addCid(pushUni.getCid());
        
        ApiResult<Map<String, Map<String, String>>> apiResult = pushUni.getPushApi().pushToSingleByCid(pushDTO);
        return  getMapValue(apiResult,pushUni.getCid());
    }


    
    private Map<String, Object> getMapValue(ApiResult<Map<String, Map<String, String>>> apiResult,String cid){
        Map<String, Object> map = MapUtil.newHashMap();
        if (apiResult.isSuccess()) {
            Map<String, Map<String, String>> data = apiResult.getData();
            Optional<Map.Entry<String, Map<String, String>>> first = data.entrySet().stream().findFirst();
            Map.Entry<String, Map<String, String>> stringMapEntry = first.get();
            Map<String, String> value = stringMapEntry.getValue();
            String result = value.get(cid);
            map.put("code",200);
            map.put("result",result);
        } else {
            map.put("code",apiResult.getCode());
            map.put("result",apiResult.getMsg());
        }
        return map;
    }

}
