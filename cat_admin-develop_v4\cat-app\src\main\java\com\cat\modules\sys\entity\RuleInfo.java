package com.cat.modules.sys.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;


 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rule_info")
public class RuleInfo extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    
    @Excel(name = "id")
    @TableId(value = "rule_id",type = IdType.UUID)
    private String ruleId;
    
    @Excel(name = "标题")
    private String title;
    
    private String content;
}
