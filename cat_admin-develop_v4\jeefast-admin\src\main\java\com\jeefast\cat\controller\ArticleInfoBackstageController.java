package com.jeefast.cat.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cat.modules.content.entity.ContentTopicRel;
import com.cat.modules.content.service.IContentTopicRelService;
import com.cat.modules.topic.entity.TopicInfo;
import com.cat.modules.topic.service.ITopicInfoService;
import com.cat.util.BlankUtil;
import com.jeefast.common.exception.BusinessException;
import com.jeefast.common.utils.file.FileUploadUtils;
import com.jeefast.common.utils.file.UploadCloudFileUtil;
import com.jeefast.system.domain.SysPost;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.ArticleInfoBackstage;
import com.jeefast.cat.service.IArticleInfoBackstageService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文章Controller
 * 
 * <AUTHOR>
 * @date 2020-08-30
 */
@Controller
@RequestMapping("/cat/articleInfoBackstage")
public class ArticleInfoBackstageController extends BaseController {
    private String prefix = "cat/articleInfoBackstage";

    @Autowired
    private IArticleInfoBackstageService articleInfoBackstageService;
    @Autowired
    private ITopicInfoService topicInfoService;
    @Autowired
    private IContentTopicRelService contentTopicRelService;


    @Autowired
    private UploadCloudFileUtil uploadCloudFileUtil;

    @RequiresPermissions("cat:articleInfoBackstage:view")
    @GetMapping()
    public String articleInfoBackstage() {
        return prefix + "/articleInfoBackstage";
    }

    /**
     * 查询文章列表
     */
    @RequiresPermissions("cat:articleInfoBackstage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ArticleInfoBackstage req) {
        QueryWrapper<ArticleInfoBackstage> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getTitle())) {
    		queryWrapper.like("title", req.getTitle());
    	} 
    	if(StringUtils.isNotEmpty(req.getType())) {
    		queryWrapper.like("type", req.getType());
    	}
        if(StringUtils.isNotEmpty(req.getState())) {
            queryWrapper.like("state", req.getState());
        }
        if(StringUtils.isNotEmpty(req.getIsRecommend())) {
            queryWrapper.like("is_recommend", req.getIsRecommend());
        }
        if(StringUtils.isNotEmpty(req.getIsRecommend())) {
            queryWrapper.like("is_recommend", req.getIsRecommend());
        }
        if(StringUtils.isNotEmpty(req.getIsCheck())) {
            queryWrapper.like("is_check", req.getIsCheck());
        }
        if (BlankUtil.isNotEmpty(req.getParams().get("beginCreateDate"))) {
            queryWrapper.ge("create_date", req.getParams().get("beginCreateDate"));
        }
        if (BlankUtil.isNotEmpty(req.getParams().get("endCreateDate"))) {
            queryWrapper.le("create_date", req.getParams().get("endCreateDate"));
        }
		// 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        return getDataTable(articleInfoBackstageService.list(queryWrapper));
    }

    /**
     * 导出文章列表
     */
    @RequiresPermissions("cat:articleInfoBackstage:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ArticleInfoBackstage articleInfoBackstage) {
        List<ArticleInfoBackstage> list = articleInfoBackstageService.list(new QueryWrapper<>());
        ExcelUtil<ArticleInfoBackstage> util = new ExcelUtil<ArticleInfoBackstage>(ArticleInfoBackstage.class);
        return util.exportExcel(list, "articleInfoBackstage");
    }

    /**
     * 新增文章
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存文章
     */
    @RequiresPermissions("cat:articleInfoBackstage:add")
    @Log(title = "文章", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestParam("file") MultipartFile multipartFile, ArticleInfoBackstage articleInfoBackstage) throws IOException {
        //上传文件支持本地云端
        String cloudPath = FileUploadUtils.uploadExt(multipartFile);
        articleInfoBackstage.setCoverImage(cloudPath);
        articleInfoBackstage.setUserId("000000000");
        articleInfoBackstage.setCreateDate(new Date());
        return toAjax(articleInfoBackstageService.save(articleInfoBackstage));
    }

    /**
     * 修改文章
     */
    @GetMapping("/edit/{articleId}")
    public String edit(@PathVariable("articleId") String articleId, ModelMap mmap) {
        ArticleInfoBackstage articleInfoBackstage = articleInfoBackstageService.getById(articleId);
        mmap.put("articleInfoBackstage", articleInfoBackstage);
        return prefix + "/edit";
    }

    /**
     * 修改保存文章
     */
    @RequiresPermissions("cat:articleInfoBackstage:edit")
    @Log(title = "文章", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestParam("file") MultipartFile multipartFile,ArticleInfoBackstage articleInfoBackstage) throws IOException {
        if(multipartFile!=null&& StrUtil.isNotEmpty(multipartFile.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            articleInfoBackstage.setCoverImage(cloudPath);
        }
        return toAjax(articleInfoBackstageService.updateById(articleInfoBackstage));
    }

    /**
     * 删除文章
     */
    @RequiresPermissions("cat:articleInfoBackstage:remove")
    @Log(title = "文章", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(articleInfoBackstageService.delete(Arrays.asList(Convert.toStrArray(ids))));
    }


    /**
     * 绑定话题列表
     * @param mmap
     * @return
     */
    @GetMapping("/bindTopicList")
    public String bindTopicList(ModelMap mmap,String id) {
        List<TopicInfo> topicInfos = topicInfoService.list();
        mmap.put("topics",topicInfos);

        List<ContentTopicRel> contentTopicRelList = contentTopicRelService.list(new QueryWrapper<ContentTopicRel>().eq("business_id",id).eq("business_type","2"));
        mmap.put("contentTopicRelList",contentTopicRelList);


        for (TopicInfo topicInfo : topicInfos)
        {
            for (ContentTopicRel rel : contentTopicRelList)
            {
                if (topicInfo.getTopicId().equals(rel.getTopicId()))
                {
                    topicInfo.setFlag(true);
                    break;
                }
            }
        }
        return prefix + "/profile/bindTopic";
    }

    /**
     * 绑定话题列表
     * @return
     */
    @PostMapping("/bindTopic")
    @ResponseBody
    public AjaxResult bindTopic(String articleIds,String topicIds) {
        if(StrUtil.isEmpty(topicIds)){
            throw new BusinessException("话题不能为空");
        }
        if(StrUtil.isEmpty(articleIds)){
            throw new BusinessException("文章id不能为空");
        }
        //先全部删除再添加
        boolean flag = contentTopicRelService.remove(new UpdateWrapper<ContentTopicRel>().eq("business_id",articleIds).eq("business_type","2"));
        String[] topicList = topicIds.split(",");
        for (String topicId : topicList) {
            TopicInfo topicInfo = topicInfoService.getById(topicId);
            ContentTopicRel contentTopicRel = new ContentTopicRel();
            contentTopicRel.setBusinessId(articleIds);
            contentTopicRel.setBusinessType("2");
            contentTopicRel.setTopicId(topicId);
            contentTopicRel.setTopicName(topicInfo.getTopicName());
            contentTopicRelService.save(contentTopicRel);
        }
        return toAjax(1);
    }


}
