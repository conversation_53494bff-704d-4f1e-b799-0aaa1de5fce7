<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeefast.cat.mapper.ExchangeGoodsLogBackstageMapper">
    <!--
    <resultMap type="ExchangeGoodsLogBackstage" id="ExchangeGoodsLogBackstageResult">
        <result property="id"    column="id"    />
        <result property="price"    column="price"    />
        <result property="createDate"    column="create_date"    />
        <result property="userId"    column="user_id"    />
        <result property="goodsId"    column="goods_id"    />
        <result property="payType"    column="pay_type"    />
        <result property="remark"    column="remark"    />
    </resultMap>
    -->


    <select id="logList" resultType="cn.hutool.core.map.CamelCaseMap">
        select t1.*,t2.user_name
        from exchange_goods_log t1
        left join user_info t2 on t1.user_id=t2.user_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>

