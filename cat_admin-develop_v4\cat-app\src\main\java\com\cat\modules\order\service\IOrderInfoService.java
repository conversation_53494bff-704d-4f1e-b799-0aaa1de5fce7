package com.cat.modules.order.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cat.modules.order.entity.OrderInfo;
import com.cat.modules.order.entity.OrderLog;
import com.cat.modules.order.thread.entity.OrderPrimary;

import java.util.List;


public interface IOrderInfoService extends IService<OrderInfo> {

    List<CamelCaseMap> listExt(QueryWrapper<OrderInfo> queryWrapper);

    
    boolean ship(OrderInfo orderInfo);

    
    OrderPrimary selectByPrimaryKey(String orderId);

    
    boolean receipt(OrderLog orderLog);

    
    boolean cancel(OrderLog orderLog);

    
    Boolean singTimeOut();

    
    Boolean cancelTimeOut();
}
