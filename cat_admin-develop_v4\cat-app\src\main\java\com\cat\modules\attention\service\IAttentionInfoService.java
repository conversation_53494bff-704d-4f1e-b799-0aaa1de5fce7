package com.cat.modules.attention.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cat.modules.attention.entity.AttentionInfo;

import java.util.Date;
import java.util.List;


public interface IAttentionInfoService extends IService<AttentionInfo> {


    Integer updateToUserNewDynamicNum(String toUserId, Integer diffNum, Date dynamicDate);
}
