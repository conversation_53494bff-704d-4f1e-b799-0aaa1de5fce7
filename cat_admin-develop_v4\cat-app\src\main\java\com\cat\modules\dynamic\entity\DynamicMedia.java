package com.cat.modules.dynamic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class DynamicMedia extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "dynamic_media_id",type = IdType.UUID)
    @TableField("dynamic_media_id")
    private String dynamicMediaId;

    
    @TableField("dynamic_id")
    private String dynamicId;

    
    @TableField("pet_id")
    private String petId;

    
    @TableField("media_url")
    private String mediaUrl;

    
    @TableField("small_url")
    private String smallUrl;

    
    @TableField("type")
    private String type;

    
    @TableField("sort_no")
    private Integer sortNo;

    
    @TableField("width")
    private Integer width;

    
    @TableField("height")
    private Integer height;

    @TableField("ext")
    private String ext;

    @TableField("file_id")
    private String fileId;
    @TableField("stream_url")
    private String streamUrl;
    @TableField("file_info")
    private String fileInfo;

}
