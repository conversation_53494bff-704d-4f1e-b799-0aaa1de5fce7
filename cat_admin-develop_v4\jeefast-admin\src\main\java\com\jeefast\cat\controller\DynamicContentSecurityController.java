package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.DynamicContentSecurity;
import com.jeefast.cat.service.IDynamicContentSecurityService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 动态内容安全审核记录Controller
 * 
 * <AUTHOR>
 * @date 2025-03-14
 */
@Controller
@RequestMapping("/cat/security")
public class DynamicContentSecurityController extends BaseController {
    private String prefix = "cat/security";

    @Autowired
    private IDynamicContentSecurityService dynamicContentSecurityService;

    @RequiresPermissions("cat:security:view")
    @GetMapping()
    public String security() {
        return prefix + "/security";
    }

    /**
     * 查询动态内容安全审核记录列表
     */
//    @RequiresPermissions("cat:security:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(DynamicContentSecurity dynamicContentSecurity) {
        QueryWrapper<DynamicContentSecurity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dynamic_id",dynamicContentSecurity.getDynamicId());
        if(StringUtils.isNotEmpty(dynamicContentSecurity.getStatus())) {
            queryWrapper.like("status", dynamicContentSecurity.getStatus());
        }
        if(StringUtils.isNotEmpty(dynamicContentSecurity.getSuggestion())) {
            queryWrapper.like("suggestion", dynamicContentSecurity.getSuggestion());
        }
        if(StringUtils.isNotEmpty(dynamicContentSecurity.getAuditStatus())) {
            queryWrapper.like("audit_status", dynamicContentSecurity.getAuditStatus());
        }
        startPage();
        return getDataTable(dynamicContentSecurityService.list(queryWrapper));
    }

    /**
     * 导出动态内容安全审核记录列表
     */
    @RequiresPermissions("cat:security:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(DynamicContentSecurity dynamicContentSecurity) {
        List<DynamicContentSecurity> list = dynamicContentSecurityService.list(new QueryWrapper<>());
        ExcelUtil<DynamicContentSecurity> util = new ExcelUtil<DynamicContentSecurity>(DynamicContentSecurity.class);
        return util.exportExcel(list, "security");
    }
//
//    /**
//     * 新增动态内容安全审核记录
//     */
//    @GetMapping("/add")
//    public String add() {
//        return prefix + "/add";
//    }
//
//    /**
//     * 新增保存动态内容安全审核记录
//     */
//    @RequiresPermissions("cat:security:add")
//    @Log(title = "动态内容安全审核记录", businessType = BusinessType.INSERT)
//    @PostMapping("/add")
//    @ResponseBody
//    public AjaxResult addSave(DynamicContentSecurity dynamicContentSecurity) {
//        return toAjax(dynamicContentSecurityService.save(dynamicContentSecurity));
//    }
//
//    /**
//     * 修改动态内容安全审核记录
//     */
//    @GetMapping("/edit/{id}")
//    public String edit(@PathVariable("id") String id, ModelMap mmap) {
//        DynamicContentSecurity dynamicContentSecurity = dynamicContentSecurityService.getById(id);
//        mmap.put("dynamicContentSecurity", dynamicContentSecurity);
//        return prefix + "/edit";
//    }
//
//    /**
//     * 修改保存动态内容安全审核记录
//     */
//    @RequiresPermissions("cat:security:edit")
//    @Log(title = "动态内容安全审核记录", businessType = BusinessType.UPDATE)
//    @PostMapping("/edit")
//    @ResponseBody
//    public AjaxResult editSave(DynamicContentSecurity dynamicContentSecurity) {
//        return toAjax(dynamicContentSecurityService.updateById(dynamicContentSecurity));
//    }

//    /**
//     * 删除动态内容安全审核记录
//     */
//    @RequiresPermissions("cat:security:remove")
//    @Log(title = "动态内容安全审核记录", businessType = BusinessType.DELETE)
//    @PostMapping( "/remove")
//    @ResponseBody
//    public AjaxResult remove(String ids) {
//        return toAjax(dynamicContentSecurityService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
//    }
}
