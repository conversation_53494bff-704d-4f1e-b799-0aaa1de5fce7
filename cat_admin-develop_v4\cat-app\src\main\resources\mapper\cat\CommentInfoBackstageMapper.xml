<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeefast.cat.mapper.CommentInfoBackstageMapper">
    <!--
    <resultMap type="CommentInfoBackstage" id="CommentInfoBackstageResult">
        <result property="commentId"    column="comment_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="parentIds"    column="parent_ids"    />
        <result property="content"    column="content"    />
        <result property="businessId"    column="business_id"    />
        <result property="businessType"    column="business_type"    />
        <result property="userId"    column="user_id"    />
        <result property="praiseCount"    column="praise_count"    />
        <result property="replyCount"    column="reply_count"    />
        <result property="createDate"    column="create_date"    />
    </resultMap>
    -->

    <select id="getList" resultType="cn.hutool.core.map.CamelCaseMap">
        select t1.*,t2.user_name
        from comment_info t1
        left join user_info t2 on t1.user_id=t2.user_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>

