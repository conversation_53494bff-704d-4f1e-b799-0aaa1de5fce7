package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.cat.modules.sys.entity.RuleInfo;
import com.cat.modules.sys.service.IRuleInfoService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 规则说明Controller
 * 
 * <AUTHOR>
 * @date 2020-12-04
 */
@Controller
@RequestMapping("/cat/ruleInfo")
public class RuleInfoController extends BaseController {
    private String prefix = "cat/ruleInfo";

    @Autowired
    private IRuleInfoService ruleInfoService;

    @RequiresPermissions("cat:ruleInfo:view")
    @GetMapping()
    public String ruleInfo() {
        return prefix + "/ruleInfo";
    }

    /**
     * 查询规则说明列表
     */
    @RequiresPermissions("cat:ruleInfo:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(RuleInfo req) {
        QueryWrapper<RuleInfo> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getTitle())) {
    		queryWrapper.like("title", req.getTitle());
    	} 
		// 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endCreateDate")), "create_date", params.get("endCreateDate"));
		}
        startPage();
        return getDataTable(ruleInfoService.list(queryWrapper));
    }

    /**
     * 导出规则说明列表
     */
    @RequiresPermissions("cat:ruleInfo:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(RuleInfo ruleInfo) {
        List<RuleInfo> list = ruleInfoService.list(new QueryWrapper<>());
        ExcelUtil<RuleInfo> util = new ExcelUtil<RuleInfo>(RuleInfo.class);
        return util.exportExcel(list, "ruleInfo");
    }

    /**
     * 新增规则说明
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存规则说明
     */
    @RequiresPermissions("cat:ruleInfo:add")
    @Log(title = "规则说明", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(RuleInfo ruleInfo) {
        return toAjax(ruleInfoService.save(ruleInfo));
    }

    /**
     * 修改规则说明
     */
    @GetMapping("/edit/{ruleId}")
    public String edit(@PathVariable("ruleId") String ruleId, ModelMap mmap) {
        RuleInfo ruleInfo = ruleInfoService.getById(ruleId);
        mmap.put("ruleInfo", ruleInfo);
        return prefix + "/edit";
    }

    /**
     * 修改保存规则说明
     */
    @RequiresPermissions("cat:ruleInfo:edit")
    @Log(title = "规则说明", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(RuleInfo ruleInfo) {
        return toAjax(ruleInfoService.updateById(ruleInfo));
    }

    /**
     * 删除规则说明
     */
    @RequiresPermissions("cat:ruleInfo:remove")
    @Log(title = "规则说明", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(ruleInfoService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
