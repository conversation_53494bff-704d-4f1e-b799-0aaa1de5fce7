package com.jeefast.cat.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 礼物信息表 gift_info
 *
 * <AUTHOR>
 * @date 2020-10-18
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("gift_info")
public class GiftInfo extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /** 礼物名称 */
    @Excel(name = "礼物名称")
    private String name;
    /** 礼物图标 */
    @Excel(name = "礼物图标")
    private String iconUrl;
    /** 消耗罐头克数 */
    @Excel(name = "消耗罐头克数")
    private Integer canNumber;
    /** 消耗金额 */
    @Excel(name = "消耗金额")
    private Double money;
    /** 排序值 */
    @Excel(name = "排序值")
    private Integer sortNo;
    /** 状态:1-下架,2-上架 */
    @Excel(name = "状态:1-下架,2-上架")
    private String status;
    /** 创建时间 */
    /*@Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;*/
    /** 备注 */
    @Excel(name = "备注")
    private String remark;
}
