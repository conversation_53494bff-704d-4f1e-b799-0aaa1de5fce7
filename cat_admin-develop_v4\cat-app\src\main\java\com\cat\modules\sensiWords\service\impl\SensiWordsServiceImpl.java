package com.cat.modules.sensiWords.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.sensiWords.entity.SensiWords;
import com.cat.modules.sensiWords.mapper.SensiWordsMapper;
import com.cat.modules.sensiWords.service.ISensiWordsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class SensiWordsServiceImpl extends ServiceImpl<SensiWordsMapper, SensiWords> implements ISensiWordsService {
    @Autowired
    private SensiWordsMapper sensiWordsMapper;



}
