package com.cat.modules.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.product.entity.ProductSku;
import com.cat.modules.product.mapper.ProductSkuMapper;
import com.cat.modules.product.req.ProductSkuAddExtReq;
import com.cat.modules.product.service.IProductSkuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service

public class ProductSkuServiceImpl extends ServiceImpl<ProductSkuMapper, ProductSku> implements IProductSkuService {
    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Override
    @Transactional
    public boolean updExt(ProductSkuAddExtReq productSku) {
        boolean flag = false;
        
        String productId = productSku.getProductId();
        flag = this.update(new UpdateWrapper<ProductSku>()
                .set("is_delete","1")
                .eq("product_id",productId)
        );
        
        List<ProductSku> skuList = productSku.getSkuList();
        for (ProductSku item : skuList) {
            item.setProductId(productId);
            flag = this.save(item);
        }
        return flag;
    }

    @Override
    public boolean addStock(String productSkuId, int quantity) {
        return productSkuMapper.addStock(productSkuId,quantity)>0?true:false;
    }
}