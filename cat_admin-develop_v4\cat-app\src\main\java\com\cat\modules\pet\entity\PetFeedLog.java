package com.cat.modules.pet.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PetFeedLog extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "feed_id",type = IdType.UUID)
    @TableField("feed_id")
    private String feedId;

    
    @TableField("pet_id")
    private String petId;

    
    @TableField("user_id")
    private String userId;



}
