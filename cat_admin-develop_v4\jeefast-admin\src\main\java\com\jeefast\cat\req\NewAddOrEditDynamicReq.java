package com.jeefast.cat.req;

import com.cat.modules.common.entity.MediaInfo;
import com.cat.modules.dynamic.entity.DynamicMedia;
import com.jeefast.cat.domain.DynamicInfoBackstage;
import lombok.Data;

import java.util.List;

/**
 * 详述: 添加动态
 * 开发人员：jingwei.huang
 * 创建时间：2021/2/25 下午8:14
 */
@Data
public class NewAddOrEditDynamicReq extends DynamicInfoBackstage {

    /**
     * 动态媒体url地址
     */
    List<DynamicMedia> dynamicMediaList;
    /**
     * 话题id
     */
    private List<String> topic;


}
