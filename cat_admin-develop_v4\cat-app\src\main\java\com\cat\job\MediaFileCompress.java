package com.cat.job;


import cn.hutool.core.img.Img;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONObject;
import com.cat.job.entity.MediaQueue;
import com.cat.modules.sys.entity.SysLog;
import com.cat.modules.sys.service.ISysLogService;
import com.cat.util.FFMpegUtil;
import com.cat.util.HttpFile;
import com.jeefast.common.config.QcloudConfig;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.file.MimeTypeUtils;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;


@Component
@Slf4j
public class MediaFileCompress {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private HttpFile httpFile;

    @Autowired
    private QcloudConfig qcloudConfig;

    @Autowired
    private ISysLogService sysLogService;
    @Autowired
    private FFMpegUtil ffMpegUtil;


    @Value("${fileTempPath}")
    private String fileTempPath;


    public void execute(String content){
        
        List<MediaQueue> mediaQueueList = JSONObject.parseArray(content, MediaQueue.class);
        for(MediaQueue media:mediaQueueList){
            String uploadPath = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.upload.path");
            String mediaUrl = media.getMediaUrl();
            String mediaId = media.getDynamicMediaId();
            String fileName =mediaUrl.substring(mediaUrl.lastIndexOf("/")+1,mediaUrl.length());
            
            if("1".equals(media.getType())){
                try {
                    File localFile = httpFile.getImageFromNetByUrl(mediaUrl);
                    File uploadFile = FileUtil.file(fileTempPath+fileName);
                    long fileSize = localFile.length()/1024;
                    
                    if(fileSize<=500){
                        Img.from(localFile).scale((float) 0.6).setQuality(0.8).write(uploadFile);
                    }else if(fileSize<=1024){
                        Img.from(localFile).scale((float) 0.5).setQuality(0.8).write(uploadFile);
                    }else {
                        
                        Img.from(localFile).scale((float) 0.4).setQuality(0.6).write(uploadFile);
                    }
                    
                    long uploadSize = uploadFile.length()/1024;
                    if(uploadSize>200){
                        Img.from(uploadFile).scale((float) 0.6).setQuality(0.8).write(uploadFile);
                        log.info("压缩完大于200KB继续压缩一次");
                    }

                    
                    String uploadType = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.cat.upload.type");
                    if("2".equals(uploadType)){
                        
                        uploadPath = "/small/"+fileName;
                        PutObjectRequest putObjectRequest = new PutObjectRequest(qcloudConfig.getBucketName(), uploadPath, uploadFile);
                        COSClient cosClient = getCosClient();
                        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
                    }else{
                        uploadPath += "/small/"+fileName;
                        FileUtil.writeFromStream(new FileInputStream(uploadFile), uploadPath);
                    }

                    
                    log.info("====压缩图片上传完成===");
                }catch (Exception e){
                    log.error("",e);
                }
            }else if("2".equals(media.getType())){
                
                try {
                    File localFile = httpFile.getImageFromNetByUrl(mediaUrl);
                    String coverImage = ffMpegUtil.processImg(localFile.getPath(),1);
                    File uploadFile = FileUtil.file(coverImage);


                    
                    String uploadType = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.cat.upload.type");
                    if("2".equals(uploadType)){
                        
                        uploadPath = "/small/"+uploadFile.getName();
                        PutObjectRequest putObjectRequest = new PutObjectRequest(qcloudConfig.getBucketName(), uploadPath, uploadFile);
                        COSClient cosClient = getCosClient();
                        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
                    }else{
                        uploadPath += "/small/"+uploadFile.getName();
                        FileUtil.writeFromStream(new FileInputStream(uploadFile), uploadPath);
                    }
                } catch (Exception e) {
                    log.error("视频封面图生成失败：",e);
                    SysLog sysLog = new SysLog();
                    sysLog.setContent(content);
                    sysLog.setLeve("error");
                    sysLogService.save(sysLog);
                }
            }
        }
    }





    
    


    public COSClient getCosClient(){
        
        COSCredentials cred = new BasicCOSCredentials(qcloudConfig.getSecretId(), qcloudConfig.getSecretKey());
        
        
        ClientConfig clientConfig = new ClientConfig(new Region(qcloudConfig.getRegion()));
        
        COSClient cosClient = new COSClient(cred, clientConfig);
        
        
        return cosClient;
    }

    public void run(String content) {
        Thread thread = new Thread(){
            public void run(){
                execute(content);
            }
        };
        thread.start();
    }
}
