package com.cat.modules.product.mapper;

import cn.hutool.core.map.CamelCaseLinkedMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cat.modules.product.entity.ProductGroupVal;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface ProductGroupValMapper extends BaseMapper<ProductGroupVal> {

    List<CamelCaseLinkedMap> listExt(@Param("ew") QueryWrapper<ProductGroupVal> qw);
}