package com.jeefast.cat.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.UserExtractLogBackstage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户提现记录 数据层
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
public interface UserExtractLogBackstageMapper extends BaseMapper<UserExtractLogBackstage> {

    List<CamelCaseMap<String, Object>> infoList(@Param("ew") Wrapper queryWrapper);
}