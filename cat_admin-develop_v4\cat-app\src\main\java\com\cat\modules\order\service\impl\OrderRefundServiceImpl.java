package com.cat.modules.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.order.entity.OrderInfo;
import com.cat.modules.order.entity.OrderItem;
import com.cat.modules.order.entity.OrderRefund;
import com.cat.modules.order.mapper.OrderItemMapper;
import com.cat.modules.order.mapper.OrderMapper;
import com.cat.modules.order.mapper.OrderRefundMapper;
import com.cat.modules.order.service.IOrderRefundService;
import com.cat.modules.product.entity.ProductSku;
import com.cat.modules.product.mapper.ProductSkuMapper;
import com.jeefast.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;


@Slf4j
@Service
public class OrderRefundServiceImpl extends ServiceImpl<OrderRefundMapper, OrderRefund> implements IOrderRefundService {
    @Autowired
    private OrderRefundMapper orderRefundMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderItemMapper orderItemMapper;
    @Autowired
    private ProductSkuMapper productSkuMapper;

    @Override
    @Transactional
    public boolean returnMoney(OrderRefund req) {
        String id = req.getId();
        OrderRefund orderRefund = this.getById(id);
        if(4 == orderRefund.getRefundSts()){
            throw new BusinessException("该订单已退款");
        }
        req.setRefundSts(4);
        req.setReturnMoneySts(1);
        req.setRefundDate(new Date());
        req.setHandelDate(new Date());
        this.updateById(req);
        OrderItem orderItem = orderItemMapper.selectById(orderRefund.getOrderItemId());
        
        if(2==orderRefund.getApplyType()){
            int goodsNumber = orderRefund.getGoodsNum();
            productSkuMapper.update(null,new UpdateWrapper<ProductSku>()
                    .setSql("stock=stock+"+goodsNumber).eq("id",orderItem.getProductSkuId())
            );
        }
        
        orderItemMapper.update(null,new UpdateWrapper<OrderItem>()
                .set("refund_sts",4).set("seller_msg",req.getSellerMsg())
                .eq("id",orderRefund.getOrderItemId())
        );
        


        return true;
    }

}
