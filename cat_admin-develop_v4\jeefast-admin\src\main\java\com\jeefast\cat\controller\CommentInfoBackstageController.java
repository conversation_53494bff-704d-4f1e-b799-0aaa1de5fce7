package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.StrUtil;
import com.cat.util.BlankUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.CommentInfoBackstage;
import com.jeefast.cat.service.ICommentInfoBackstageService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 评论管理Controller
 * 
 * <AUTHOR>
 * @date 2020-08-17
 */
@Controller
@RequestMapping("/cat/commentInfoBackstage")
public class CommentInfoBackstageController extends BaseController {
    private String prefix = "cat/commentInfoBackstage";

    @Autowired
    private ICommentInfoBackstageService commentInfoBackstageService;

    @RequiresPermissions("cat:commentInfoBackstage:view")
    @GetMapping()
    public String commentInfoBackstage() {
        return prefix + "/commentInfoBackstage";
    }

    /**
     * 查询评论管理列表
     */
    @RequiresPermissions("cat:commentInfoBackstage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(CommentInfoBackstage commentInfoBackstage) {
        QueryWrapper<CommentInfoBackstage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("1","1");
        if(StrUtil.isNotEmpty(commentInfoBackstage.getParentId())){
            queryWrapper.eq("t1.parent_id",commentInfoBackstage.getParentId());
        }
        if(StrUtil.isNotEmpty(commentInfoBackstage.getBusinessId())){
            queryWrapper.eq("t1.business_id",commentInfoBackstage.getBusinessId());
        }
        if(StrUtil.isNotEmpty(commentInfoBackstage.getBusinessType())){
            queryWrapper.eq("t1.business_type",commentInfoBackstage.getBusinessType());
        }
        if(StrUtil.isNotEmpty(commentInfoBackstage.getUserId())){
            queryWrapper.eq("t1.user_id",commentInfoBackstage.getUserId());
        }
        if (BlankUtil.isNotEmpty(commentInfoBackstage.getParams().get("beginCreateDate"))) {
            queryWrapper.ge("t1.create_date", commentInfoBackstage.getParams().get("beginCreateDate"));
        }
        if (BlankUtil.isNotEmpty(commentInfoBackstage.getParams().get("endCreateDate"))) {
            queryWrapper.le("t1.create_date", commentInfoBackstage.getParams().get("endCreateDate"));
        }
    	// 需要根据页面查询条件进行组装
    	/*if(StringUtils.isNotEmpty(sysDemo.getLoginName())) {
    		queryWrapper.like("login_name", sysDemo.getLoginName());
    	} 
    	if(StringUtils.isNotEmpty(sysDemo.getUserName())) {
    		queryWrapper.like("user_name", sysDemo.getUserName());
    	}*/
		// 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        queryWrapper.orderByDesc("t1.create_date");
        return getDataTable(commentInfoBackstageService.getList(queryWrapper));
    }

    /**
     * 导出评论管理列表
     */
    @RequiresPermissions("cat:commentInfoBackstage:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(CommentInfoBackstage commentInfoBackstage) {
        List<CommentInfoBackstage> list = commentInfoBackstageService.list(new QueryWrapper<>());
        ExcelUtil<CommentInfoBackstage> util = new ExcelUtil<CommentInfoBackstage>(CommentInfoBackstage.class);
        return util.exportExcel(list, "commentInfoBackstage");
    }

    /**
     * 新增评论管理
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存评论管理
     */
    @RequiresPermissions("cat:commentInfoBackstage:add")
    @Log(title = "评论管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(CommentInfoBackstage commentInfoBackstage) {
        return toAjax(commentInfoBackstageService.save(commentInfoBackstage));
    }

    /**
     * 修改评论管理
     */
    @GetMapping("/edit/{commentId}")
    public String edit(@PathVariable("commentId") String commentId, ModelMap mmap) {
        CommentInfoBackstage commentInfoBackstage = commentInfoBackstageService.getById(commentId);
        mmap.put("commentInfoBackstage", commentInfoBackstage);
        return prefix + "/edit";
    }

    /**
     * 修改保存评论管理
     */
    @RequiresPermissions("cat:commentInfoBackstage:edit")
    @Log(title = "评论管理", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(CommentInfoBackstage commentInfoBackstage) {
        if(StrUtil.isEmpty(commentInfoBackstage.getParentId())){
            commentInfoBackstage.setParentId(null);
        }
        if(StrUtil.isEmpty(commentInfoBackstage.getParentIds())){
            commentInfoBackstage.setParentIds(null);
        }
        return toAjax(commentInfoBackstageService.updateById(commentInfoBackstage));
    }

    /**
     * 删除评论管理
     */
    @RequiresPermissions("cat:commentInfoBackstage:remove")
    @Log(title = "评论管理", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        boolean falg = commentInfoBackstageService.delete(ids);
        return toAjax(falg);
    }
}
