package com.jeefast.cat.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.DynamicInfoBackstage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 动态内容 数据层
 *
 * <AUTHOR>
 * @date 2020-08-08
 */
public interface DynamicInfoBackstageMapper extends BaseMapper<DynamicInfoBackstage> {

    List<CamelCaseMap<String, Object>> dynamicList(@Param("ew") Wrapper queryWrapper);
}