package com.cat.modules.user.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cat.modules.user.entity.UserSpreadLog;

import java.util.List;


public interface IUserSpreadLogService extends IService<UserSpreadLog> {


    List<CamelCaseMap<String, Object>> logList(QueryWrapper qw);

}
