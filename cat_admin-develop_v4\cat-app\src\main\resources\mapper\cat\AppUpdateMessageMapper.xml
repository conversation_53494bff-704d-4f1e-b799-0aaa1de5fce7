<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.sys.mapper.AppUpdateMessageMapper">

    <resultMap type="com.cat.modules.sys.entity.AppUpdateMessage" id="AppUpdateMessageBackstageResult">
        <result property="id"    column="id"    />
        <result property="version"    column="version"    />
        <result property="versionName"    column="version_name"    />
        <result property="forceUpdate"    column="force_update"    />
        <result property="updateType"    column="update_type"    />
        <result property="upDesc"    column="up_desc"    />
        <result property="apkUrl"    column="apk_url"    />
        <result property="client"    column="client"    />
        <result property="remark"    column="remark"    />
        <result property="createDate"    column="create_date"    />
        <result property="fileSize"    column="file_size"    />
        <result property="minVersion"    column="min_version"    />
        <result property="apkEntireUrl"    column="apk_entire_url"    />
        <result property="popStr"    column="is_pop"    />
    </resultMap>



    <select id="refreshVersionCache" resultMap="AppUpdateMessageBackstageResult" resultType="com.cat.modules.sys.entity.AppUpdateMessage">
        select * from app_update_message where client=#{client} order by version desc limit 1
    </select>
</mapper>

