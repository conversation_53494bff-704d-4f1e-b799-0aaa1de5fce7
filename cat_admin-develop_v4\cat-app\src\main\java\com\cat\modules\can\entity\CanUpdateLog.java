package com.cat.modules.can.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CanUpdateLog extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "can_update_id",type = IdType.UUID)
    @TableField("can_update_id")
    private String canUpdateId;

    
    @TableField("user_id")
    private String userId;

    
    @TableField("type")
    private String type;

    
    @TableField("ago")
    private Integer ago;

    
    @TableField("later")
    private Integer later;

    
    @TableField("update_value")
    private Integer updateValue;

    
    @TableField("remark")
    private String remark;


    
    @TableField("operation")
    private String operation;



}
