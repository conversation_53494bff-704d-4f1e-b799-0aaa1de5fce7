package com.jeefast.cat.domain;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.jeefast.common.core.entity.CatBaseEntity;

/**
 * 动态内容表 dynamic_info
 *
 * <AUTHOR>
 * @date 2020-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dynamic_info")
public class DynamicInfoBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "dynamic_id", type = IdType.UUID)
    private String dynamicId;
    /**
     * 所属宠物ID
     */
    private String petId;
    /**
     * 用户ID
     */
    @Excel(name = "用户ID")
    private String userId;
    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;
    /**
     * 内容
     */
    @Excel(name = "内容")
    private String content;
    /**
     * 所在位置
     */
    private String addr;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 点赞数
     */
    @Excel(name = "点赞数")
    private Integer praiseCount;
    /**
     * 评论数
     */
    @Excel(name = "评论数")
    private Integer commentCount;
    /**
     * 动态类型（1：文字、2：图文、3：视频）
     */
    @Excel(name = "动态类型", readConverterExp = "1=：文字、2：图文、3：视频、4：直播")
    private String type;
    /**
     * 动态来源（微博、知乎、百度等）
     */
    @Excel(name = "动态来源", readConverterExp = "微=博、知乎、百度等")
    private String source;
    /**
     * 封面图
     */
    @Excel(name = "封面图")
    private String coverImage;
    /**
     * 资源高度px
     */
    private Integer height;
    /**
     * 资源宽度px
     */
    private Integer width;
    /**
     * 媒体数
     */
    @Excel(name = "媒体数")
    private Integer mediaCount;
    /**
     * 权重
     */
    @Excel(name = "权重")
    private Integer weight;
    /**
     * 权重系数
     */
    @Excel(name = "权重系数")
    private Double scale;
    /**
     * 是否后台审核通过
     */
    @Excel(name = "是否后台审核通过")
    private String isCheck;
    /**
     * 是否删除
     */
    @Excel(name = "是否删除")
    private String isDelete;
    /**
     * 驳回说明
     */
    @Excel(name = "驳回说明")
    private String reasons;

    /**
     * 用户名称 查询字段用
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 手机号
     */
    @TableField(exist = false)
    private String mobile;

    /**
     * 圈子id
     **/
    @TableField("circle_id")
    private String circleId;
    /**
     * 权重
     */
    @Excel(name = "类型")
    private String articleType;

    /**
     * 浏览量
     */
    private Integer viewCount;

    /**
     * 虚拟浏览量
     */
    private Integer visualViewCount;

    private Integer isPublic;
    private String isTop;
    /**
     * 是否需要查置顶，0不需要 1需要
     */
    @TableField(exist = false)
    private String needQueryTop="1";
    /**
     * 直播时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private Date liveTime;


    /**
     *  动态分类id
     */
    private String dynamicCategoryId;
    /**
     *  后台用户id
     */
    private Long sysUserId;
    /**
     *  是否需要内容安全审核 0否 1是
     */
    private String needSecurity;

    /**
     *  连接类型 1 商品 2店铺 3群聊
     */
    private String thirdType;
    /**
     *  链接地址
     */
    private String thirdUrl;
    /**
     *  是否立即发布0否（定时发布） 1是（立即发布）
     */
    private String releaseType;
    /**
     *  发布时间
     */
    private Date releaseTime;
    /**
     *  是否推广0否1是
     */
    private String beExtend;
    /**
     *  推广开始时间
     */
    private Date extendBeginTime;
    /**
     *  推广结束时间
     */
    private Date extendEndTime;
    /**
     *  最终审核状态 0审核中 1通过 2不通过 3待复审
     */
    private String auditStatus;
}
