package com.cat.modules.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.order.entity.OrderItem;
import com.cat.modules.order.mapper.OrderItemMapper;
import com.cat.modules.order.service.IOrderItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class OrderItemServiceImpl extends ServiceImpl<OrderItemMapper, OrderItem> implements IOrderItemService {
    @Autowired
    private OrderItemMapper orderItemMapper;


}
