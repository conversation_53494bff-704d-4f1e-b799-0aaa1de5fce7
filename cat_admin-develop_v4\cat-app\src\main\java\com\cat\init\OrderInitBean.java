package com.cat.init;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.cat.modules.order.service.IOrderInfoService;
import com.cat.modules.order.thread.OrderDelayQueueThread;
import com.cat.modules.order.thread.entity.OrderDelayedTask;
import com.jeefast.common.enums.CachEnum;
import com.jeefast.common.utils.RedisUtil;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;



public class OrderInitBean  {

    public static OrderDelayQueueThread OrderDelayQueueThread = null;
    @Autowired
    private IOrderInfoService orderService;
    @Autowired
    private RedisUtil redisUtil;

    @SuppressWarnings("unchecked")
    
    public void afterPropertiesSet() throws Exception {
        Map<String, String> entries = redisUtil.hgetall(CachEnum.ORDER_DELAY_QUEUE.getCode());
        if (OrderDelayQueueThread == null) {
            OrderDelayQueueThread = new OrderDelayQueueThread(orderService, redisUtil);
            Thread thread = new Thread(OrderDelayQueueThread);
            thread.start();
        }
        if (ObjectUtil.isNotNull(entries) && entries.size()>0) {
            for (String key : entries.keySet()) {
                OrderDelayedTask delayQueueOrder = JSON.parseObject(entries.get(key),OrderDelayedTask.class);
                OrderDelayQueueThread.putDelayQueueForRedis(delayQueueOrder);
            }
        }
    }
}
