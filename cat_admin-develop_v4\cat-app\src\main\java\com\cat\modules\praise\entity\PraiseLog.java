package com.cat.modules.praise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PraiseLog extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "praise_id",type = IdType.UUID)
    @TableField("praise_id")
    private String praiseId;

    
    @TableField("user_id")
    private String userId;

    
    @TableField("business_id")
    private String businessId;

    
    @TableField("business_type")
    private String businessType;


    
    @TableField("operation_type")
    private String operationType;



}
