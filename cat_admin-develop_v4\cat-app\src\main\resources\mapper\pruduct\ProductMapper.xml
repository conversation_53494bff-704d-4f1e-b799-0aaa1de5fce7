<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.product.mapper.ProductMapper">
    <!--
    <resultMap type="Product" id="ProductResult">
        <result property="id"    column="id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="name"    column="name"    />
        <result property="subtitle"    column="subtitle"    />
        <result property="mainimage"    column="mainimage"    />
        <result property="subimages"    column="subimages"    />
        <result property="detail"    column="detail"    />
        <result property="price"    column="price"    />
        <result property="originalPrice"    column="original_price"    />
        <result property="stock"    column="stock"    />
        <result property="totalSales"    column="total_sales"    />
        <result property="isRecommend"    column="is_recommend"    />
        <result property="status"    column="status"    />
        <result property="skuName"    column="sku_name"    />
        <result property="skuVal"    column="sku_val"    />
        <result property="postage"    column="postage"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
        <result property="shopId"    column="shop_id"    />
    </resultMap>
    -->


    <select id="listExt" resultType="cn.hutool.core.map.CamelCaseMap">
        select t1.id,t1.category_id,t1.name,t1.subtitle,t1.mainimage,t1.subimages,t1.price,t1.original_price
        ,t1.stock,t1.total_sales,t1.is_recommend,t1.status,t1.postage,t1.create_date,t1.update_date,t1.shop_id
        ,t2.name as category_name
        from product t1
        left join category t2 on t1.category_id=t2.id
        ${ew.customSqlSegment}
    </select>
</mapper>

