package com.cat.modules.dynamic.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.dynamic.entity.DynamicMedia;
import com.cat.modules.dynamic.mapper.DynamicMediaMapper;
import com.cat.modules.dynamic.service.IDynamicMediaService;
import com.jeefast.common.utils.file.VodUploadCloudFileUtil;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.vod.v20180717.models.AdaptiveDynamicStreamingInfoItem;
import com.tencentcloudapi.vod.v20180717.models.DescribeMediaInfosResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;


@Slf4j
@Service

public class DynamicMediaServiceImpl extends ServiceImpl<DynamicMediaMapper, DynamicMedia> implements IDynamicMediaService {
    @Autowired
    private DynamicMediaMapper dynamicMediaMapper;
    @Autowired
    private VodUploadCloudFileUtil vodUploadCloudFileUtil;


    @Override
    public List<CamelCaseMap> getDynamicMedia(String dynamicId, String type) {
        return dynamicMediaMapper.getDynamicMedia(dynamicId,type);
    }

    /**
     * 视频转码信息查询任务
     */
    @Override
    public void adaptiveDynamicStreamingInfoQueryJob() {
        List<DynamicMedia> noFinishVideoTaskList = list(new LambdaQueryWrapper<DynamicMedia>().
                eq(DynamicMedia::getType, "2")
                .isNull(DynamicMedia::getStreamUrl)
                .isNotNull(DynamicMedia::getFileId)
        );
        log.info("视频转码信息查询任务数量为:{}", noFinishVideoTaskList.size());
        for (DynamicMedia dynamicMedia : noFinishVideoTaskList) {
            DescribeMediaInfosResponse infosResponse = vodUploadCloudFileUtil.queryDescribeMediaInfos(dynamicMedia.getFileId());
            if (infosResponse != null && infosResponse.getMediaInfoSet()[0].getAdaptiveDynamicStreamingInfo() != null) {
                List<AdaptiveDynamicStreamingInfoItem> streamInfoList = Arrays.asList(infosResponse.getMediaInfoSet()[0].getAdaptiveDynamicStreamingInfo().getAdaptiveDynamicStreamingSet());
                if (CollectionUtil.isNotEmpty(streamInfoList)) {
                    update(new LambdaUpdateWrapper<DynamicMedia>()
                            .eq(DynamicMedia::getDynamicMediaId, dynamicMedia.getDynamicMediaId())
                            .set(DynamicMedia::getStreamUrl, streamInfoList.get(0).getUrl())
                            .set(DynamicMedia::getFileInfo, AbstractModel.toJsonString(infosResponse))

                    );
                }
            }
        }
    }
}
