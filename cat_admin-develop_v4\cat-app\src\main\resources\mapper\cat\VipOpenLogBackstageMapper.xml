<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jeefast.cat.mapper.VipOpenLogBackstageMapper">
    <!--
    <resultMap type="VipOpenLogBackstage" id="VipOpenLogBackstageResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="configId"    column="config_id"    />
        <result property="vipStartDate"    column="vip_start_date"    />
        <result property="vipEndDate"    column="vip_end_date"    />
        <result property="createDate"    column="create_date"    />
        <result property="remark"    column="remark"    />
    </resultMap>
    -->


    <select id="userList" resultType="cn.hutool.core.map.CamelCaseMap">
        select t1.id,t1.user_id,t1.config_id,t1.vip_start_date,t1.vip_end_date,t1.create_date,t1.remark
                ,t2.name config_name,t3.user_name
        from vip_open_log t1
        left join vip_open_config t2 on t1.config_id=t2.id
        left join user_info t3 on t1.user_id=t3.user_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>


</mapper>

