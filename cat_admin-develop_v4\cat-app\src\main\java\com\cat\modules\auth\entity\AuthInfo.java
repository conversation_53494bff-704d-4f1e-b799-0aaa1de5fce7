package com.cat.modules.auth.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                            import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;


 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("auth_info")
public class AuthInfo extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id", type = IdType.AUTO)
    private String id;
    
    @Excel(name = "认证类型 1个人认证 2机构认证")
    private String authType;
    
    @Excel(name = "认证描述")
    private String authDesc;
    
    @Excel(name = "电子邮箱")
    private String email;
    
    @Excel(name = "手机号")
    private String mobile;
    
    @Excel(name = "网站链接")
    private String link;
    
    @Excel(name = "名称")
    private String name;
    
    @Excel(name = "机构认真公司负责人，个人认证真实姓名")
    private String truename;
    
    @Excel(name = "文本框内容")
    private String telegram;
    
    @Excel(name = "文本框内容")
    private String skype;


    @Excel(name = "机构地址")
    private String address;

    @Excel(name = "申请人id")
    private String applyUserId;

    @Excel(name = "申请人姓名")
    private String applyTruename;



    @TableField(exist = false)
    private String status;
    @TableField(exist = false)
    private String userId;
    @TableField(exist = false)
    private String auther;
    @TableField(exist = false)
    private String description;
}
