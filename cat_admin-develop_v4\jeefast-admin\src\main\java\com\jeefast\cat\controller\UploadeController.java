package com.jeefast.cat.controller;


import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.file.UploadCloudFileUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;


/**
 * 文件上传相关
 */
@Controller
@RequestMapping("/uploade")
public class UploadeController extends BaseController {

    @Autowired
    private UploadCloudFileUtil uploadCloudFileUtil;



    @PostMapping("/file")
    @ResponseBody
    public AjaxResult file(File file){

        return success();
    }


}
