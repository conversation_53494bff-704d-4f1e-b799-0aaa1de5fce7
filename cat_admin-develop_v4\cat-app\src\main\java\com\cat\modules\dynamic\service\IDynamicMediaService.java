package com.cat.modules.dynamic.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cat.modules.dynamic.entity.DynamicMedia;

import java.util.List;


public interface IDynamicMediaService extends IService<DynamicMedia> {

    List<CamelCaseMap> getDynamicMedia(String dynamicId, String type);


    /**
     * 视频转码信息查询任务
     */
    void adaptiveDynamicStreamingInfoQueryJob();
}
