package com.cat.modules.product.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.product.entity.ProductGroupKey;
import com.cat.modules.product.mapper.ProductGroupKeyMapper;
import com.cat.modules.product.service.IProductGroupKeyService;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;


@Service

public class ProductGroupKeyServiceImpl extends ServiceImpl<ProductGroupKeyMapper, ProductGroupKey> implements IProductGroupKeyService {

}