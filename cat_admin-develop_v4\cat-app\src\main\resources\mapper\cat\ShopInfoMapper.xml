<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.shop.mapper.ShopInfoMapper">
    <!--
    <resultMap type="ShopInfo" id="ShopInfoResult">
        <result property="id"    column="id"    />
        <result property="shopName"    column="shop_name"    />
        <result property="userId"    column="user_id"    />
        <result property="shopType"    column="shop_type"    />
        <result property="intro"    column="intro"    />
        <result property="shopNotice"    column="shop_notice"    />
        <result property="shopIndustry"    column="shop_industry"    />
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="shopOwner"    column="shop_owner"    />
        <result property="mobile"    column="mobile"    />
        <result property="tel"    column="tel"    />
        <result property="score"    column="score"    />
        <result property="avgPrice"    column="avg_price"    />
        <result property="shopLat"    column="shop_lat"    />
        <result property="shopLng"    column="shop_lng"    />
        <result property="shopAddress"    column="shop_address"    />
        <result property="province"    column="province"    />
        <result property="city"    column="city"    />
        <result property="area"    column="area"    />
        <result property="pcaCode"    column="pca_code"    />
        <result property="shopLogo"    column="shop_logo"    />
        <result property="shopPhotos"    column="shop_photos"    />
        <result property="openTime"    column="open_time"    />
        <result property="shopStatus"    column="shop_status"    />
        <result property="applyDate"    column="apply_date"    />
        <result property="successDate"    column="success_date"    />
        <result property="failMessage"    column="fail_message"    />
        <result property="failDate"    column="fail_date"    />
        <result property="transportType"    column="transport_type"    />
        <result property="fixedFreight"    column="fixed_freight"    />
        <result property="fullFreeShipping"    column="full_free_shipping"    />
        <result property="praiseCount"    column="praise_count"    />
        <result property="starCount"    column="star_count"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
        <result property="restaurantId"    column="restaurant_id"    />
    </resultMap>
    -->

</mapper>

