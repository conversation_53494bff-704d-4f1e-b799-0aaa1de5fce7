package com.cat.job;

import cn.hutool.core.img.Img;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cat.job.entity.MediaQueue;
import com.cat.job.entity.ReduceMediaQueue;
import com.cat.modules.dynamic.entity.DynamicInfo;
import com.cat.modules.dynamic.entity.DynamicMedia;
import com.cat.modules.dynamic.service.IDynamicInfoService;
import com.cat.modules.dynamic.service.IDynamicMediaService;
import com.cat.modules.pet.entity.PetRecord;
import com.cat.modules.pet.entity.PetRecordMedia;
import com.cat.modules.pet.service.IPetRecordMediaService;
import com.cat.modules.pet.service.IPetRecordService;
import com.cat.modules.sys.entity.SysLog;
import com.cat.modules.sys.service.ISysLogService;
import com.cat.util.FFMpegUtil;
import com.cat.util.HttpFile;
import com.jeefast.common.config.QcloudConfig;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.file.UploadCloudFileUtil;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileInputStream;
import java.util.List;


@Slf4j
@Component
public class ReduceMediaFile {


    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private HttpFile httpFile;

    @Autowired
    private QcloudConfig qcloudConfig;

    @Autowired
    private ISysLogService sysLogService;
    @Autowired
    private FFMpegUtil ffMpegUtil;


    @Value("${fileTempPath}")
    private String fileTempPath;
    @Autowired
    private IDynamicInfoService dynamicInfoService;
    @Autowired
    private IDynamicMediaService dynamicMediaService;
    @Autowired
    private IPetRecordService petRecordService;
    @Autowired
    private IPetRecordMediaService petRecordMediaService;



    public void execute(String content){
        
        List<ReduceMediaQueue> mediaQueueList = JSONObject.parseArray(content, ReduceMediaQueue.class);
        for(ReduceMediaQueue media:mediaQueueList){
            String uploadPath = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.upload.path");
            
            String uploadType = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.cat.upload.type");
            String mediaUrl = media.getUrl();
            String busId = media.getBusId();
            String busType = media.getBusType();
            String fileName =mediaUrl.substring(mediaUrl.lastIndexOf("/")+1,mediaUrl.length());
            
            if("1".equals(media.getType())){
                try {
                    File localFile = httpFile.getImageFromNetByUrl(mediaUrl);
                    File uploadFile = FileUtil.file(fileTempPath+fileName);
                    long fileSize = localFile.length()/1024;
                    
                    if(fileSize<=500){
                        Img.from(localFile).scale((float) 0.6).setQuality(0.8).write(uploadFile);
                    }else if(fileSize<=1024){
                        Img.from(localFile).scale((float) 0.5).setQuality(0.8).write(uploadFile);
                    }else {
                        
                        Img.from(localFile).scale((float) 0.4).setQuality(0.6).write(uploadFile);
                    }
                    
                    long uploadSize = uploadFile.length()/1024;
                    if(uploadSize>200){
                        Img.from(uploadFile).scale((float) 0.6).setQuality(0.8).write(uploadFile);
                        log.info("压缩完大于200KB继续压缩一次");
                    }
                    if("2".equals(uploadType)){
                        
                        uploadPath = "/small/"+fileName;
                        PutObjectRequest putObjectRequest = new PutObjectRequest(qcloudConfig.getBucketName(), uploadPath, uploadFile);
                        
                    }else{
                        uploadPath += "/small/"+fileName;
                        FileUtil.writeFromStream(new FileInputStream(uploadFile), uploadPath);
                    }

                    
                    log.info("====压缩图片上传完成===");
                }catch (Exception e){
                    log.error("",e);
                }
            }else if("2".equals(media.getType())){
                
                try {
                    
                    if("4".equals(uploadType)){
                        
                        mediaUrl = mediaUrl+"?x-oss-process=video/snapshot,t_1600,f_jpg,w_0,h_0,m_fast,ar_auto";
                        File localFile = httpFile.getImageFromNetByUrl(mediaUrl);
                        String url = UploadCloudFileUtil.dCloudAliYunUp(localFile);
                        
                        upData(busId,busType,url);
                        return;
                    }

                    File localFile = httpFile.getImageFromNetByUrl(mediaUrl);
                    String coverImage = ffMpegUtil.processImg(localFile.getPath(),1);
                    File uploadFile = FileUtil.file(coverImage);
                    
                    uploadType = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.cat.upload.type");
                    if("2".equals(uploadType)){
                        
                        uploadPath = "/small/"+uploadFile.getName();
                        UploadCloudFileUtil.uploadFile(uploadFile,uploadPath);
                    }else{
                        uploadPath += "/small/"+uploadFile.getName();
                        FileUtil.writeFromStream(new FileInputStream(uploadFile), uploadPath);
                    }
                } catch (Exception e) {
                    log.error("视频封面图生成失败：",e);
                    SysLog sysLog = new SysLog();
                    sysLog.setContent(content);
                    sysLog.setLeve("error");
                    sysLogService.save(sysLog);
                }
            }
        }
    }


    
    public boolean upData(String busId,String busType,String url){
        boolean flag = false;
        
        if("1".equals(busType)){
            flag = dynamicInfoService.update(new UpdateWrapper<DynamicInfo>()
                    .set("cover_image",url)
                    .eq("dynamic_id",busId)
            );
            flag = dynamicMediaService.update(new UpdateWrapper<DynamicMedia>()
                    .set("small_url",url)
                    .eq("dynamic_id",busId)
            );
        }else if("2".equals(busType)){

        }else if("3".equals(busType)){
            flag = petRecordService.update(new UpdateWrapper<PetRecord>()
                    .set("cover_image",url)
                    .eq("id",busId)
            );
            flag = petRecordMediaService.update(new UpdateWrapper<PetRecordMedia>()
                    .set("small_url",url)
                    .eq("record_id",busId)
            );
        }
        return flag;
    }


}
