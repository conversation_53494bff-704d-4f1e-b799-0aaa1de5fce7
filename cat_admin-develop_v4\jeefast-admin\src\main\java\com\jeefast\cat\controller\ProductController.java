package com.jeefast.cat.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.cat.modules.product.entity.Product;
import com.cat.modules.product.req.ProductListReq;
import com.cat.modules.product.service.IProductService;
import com.jeefast.common.utils.file.FileUploadUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 商品Controller
 * 
 * <AUTHOR>
 * @date 2020-12-19
 */
@Controller
@RequestMapping("/cat/product")
public class ProductController extends BaseController {
    private String prefix = "cat/product";

    @Autowired
    private IProductService productService;

    @RequiresPermissions("cat:product:view")
    @GetMapping()
    public String product() {
        return prefix + "/product";
    }

    /**
     * 查询商品列表
     */
    @RequiresPermissions("cat:product:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProductListReq req) {
        QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getCategoryId())) {
    		queryWrapper.eq("t1.category_id", req.getCategoryId());
    	}
        if(StringUtils.isNotEmpty(req.getCategoryName())) {
            queryWrapper.like("t2.name", req.getCategoryName());
        }
        if(StringUtils.isNotEmpty(req.getName())) {
    		queryWrapper.like("t1.name", req.getName());
    	}
        if(ObjectUtil.isNotNull(req.getStatus())) {
            queryWrapper.eq("t1.status", req.getStatus());
        }
        if(StringUtils.isNotEmpty(req.getShopId())) {
            queryWrapper.eq("t1.shop_id", req.getShopId());
        }
		// 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endCreateDate")), "create_date", params.get("endCreateDate"));
		}
        startPage();
        return getDataTable(productService.listExt(queryWrapper));
    }

    /**
     * 导出商品列表
     */
    @RequiresPermissions("cat:product:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Product product) {
        List<Product> list = productService.list(new QueryWrapper<>());
        ExcelUtil<Product> util = new ExcelUtil<Product>(Product.class);
        return util.exportExcel(list, "product");
    }

    /**
     * 新增商品
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存商品
     */
    @RequiresPermissions("cat:product:add")
    @Log(title = "商品", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestParam("file") MultipartFile multipartFile, Product product) throws IOException {
        if(multipartFile!=null&& StrUtil.isNotEmpty(multipartFile.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            product.setMainimage(cloudPath);
        }
        product.setUpdateDate(new Date());
        return toAjax(productService.save(product));
    }

    /**
     * 修改商品
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        Product product = productService.getById(id);
        mmap.put("product", product);
        return prefix + "/edit";
    }

    /**
     * 修改保存商品
     */
    @RequiresPermissions("cat:product:edit")
    @Log(title = "商品", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestParam("file") MultipartFile multipartFile,Product product) throws IOException {
        if(multipartFile!=null&& StrUtil.isNotEmpty(multipartFile.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            product.setMainimage(cloudPath);
        }
        product.setUpdateDate(new Date());
        return toAjax(productService.updateById(product));
    }

    /**
     * 删除商品
     */
    @RequiresPermissions("cat:product:remove")
    @Log(title = "商品", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(productService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }

    @GetMapping("/addSku")
    public String addSku(@RequestParam("productId") String productId,ModelMap mmap){
        mmap.put("productId",productId);
        return prefix+"/sku/spec";
    }

}
