package com.cat.modules.user.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cat.modules.user.entity.UserLogoutLog;

import java.util.List;


public interface IUserLogoutLogService extends IService<UserLogoutLog> {

	List<CamelCaseMap> listExt(QueryWrapper<UserLogoutLog> queryWrapper);

	
	boolean clearUserInfo(UserLogoutLog userLogoutLog);

	
	void delRedisUserData(String userId);

}
