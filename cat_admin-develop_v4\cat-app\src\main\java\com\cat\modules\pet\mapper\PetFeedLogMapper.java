package com.cat.modules.pet.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cat.modules.pet.entity.PetFeedLog;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;



public interface PetFeedLogMapper extends BaseMapper<PetFeedLog> {

    
    int getByUserIdDayLog(@Param("userId") String userId, @Param("petId") String petId, @Param("days") String days);

}
