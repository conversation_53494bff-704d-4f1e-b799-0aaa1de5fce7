package com.cat.modules.attention.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.attention.entity.AttentionLog;
import com.cat.modules.attention.mapper.AttentionLogMapper;
import com.cat.modules.attention.service.IAttentionLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class AttentionLogServiceImpl extends ServiceImpl<AttentionLogMapper, AttentionLog> implements IAttentionLogService {
    @Autowired
    private AttentionLogMapper attentionLogMapper;



}
