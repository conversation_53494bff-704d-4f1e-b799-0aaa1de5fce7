package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.ObjectUtil;
import com.cat.modules.order.entity.OrderLog;
import com.cat.modules.order.service.IOrderLogService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 订单日志Controller
 * 
 * <AUTHOR>
 * @date 2021-04-26
 */
@Controller
@RequestMapping("/cat/orderLog")
public class OrderLogController extends BaseController {
    private String prefix = "cat/orderLog";

    @Autowired
    private IOrderLogService orderLogService;

    @RequiresPermissions("cat:orderLog:view")
    @GetMapping()
    public String orderLog() {
        return prefix + "/orderLog";
    }

    /**
     * 查询订单日志列表
     */
    @RequiresPermissions("cat:orderLog:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(OrderLog req) {
        QueryWrapper<OrderLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("1","1");
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getUserId())) {
    		queryWrapper.eq("user_id", req.getUserId());
    	} 
    	if(StringUtils.isNotEmpty(req.getOrderId())) {
    		queryWrapper.eq("order_id", req.getOrderId());
    	}
        if(ObjectUtil.isNotNull(req.getStatus())) {
            queryWrapper.eq("status", req.getStatus());
        }
		// 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endCreateDate")), "create_date", params.get("endCreateDate"));
		}
        startPage();
        queryWrapper.orderByDesc("create_date");
        return getDataTable(orderLogService.list(queryWrapper));
    }

    /**
     * 导出订单日志列表
     */
    @RequiresPermissions("cat:orderLog:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(OrderLog orderLog) {
        List<OrderLog> list = orderLogService.list(new QueryWrapper<>());
        ExcelUtil<OrderLog> util = new ExcelUtil<OrderLog>(OrderLog.class);
        return util.exportExcel(list, "orderLog");
    }

    /**
     * 新增订单日志
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存订单日志
     */
    @RequiresPermissions("cat:orderLog:add")
    @Log(title = "订单日志", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(OrderLog orderLog) {
        return toAjax(orderLogService.save(orderLog));
    }

    /**
     * 修改订单日志
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        OrderLog orderLog = orderLogService.getById(id);
        mmap.put("orderLog", orderLog);
        return prefix + "/edit";
    }

    /**
     * 修改保存订单日志
     */
    @RequiresPermissions("cat:orderLog:edit")
    @Log(title = "订单日志", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(OrderLog orderLog) {
        return toAjax(orderLogService.updateById(orderLog));
    }

    /**
     * 删除订单日志
     */
    @RequiresPermissions("cat:orderLog:remove")
    @Log(title = "订单日志", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(orderLogService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
