package com.cat.modules.user.service.impl;

import cn.hutool.core.map.CamelCaseMap;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.user.entity.User;
import com.cat.modules.user.entity.UserSpreadLog;
import com.cat.modules.user.mapper.UserSpreadLogMapper;
import com.cat.modules.user.service.IUserSpreadLogService;
import com.cat.modules.user.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


@Slf4j
@Service
public class UserSpreadLogServiceImpl extends ServiceImpl<UserSpreadLogMapper, UserSpreadLog> implements IUserSpreadLogService {

    @Autowired
    private UserSpreadLogMapper userSpreadLogMapper;

    private static final String SPREAD_CONFIG = "sys_config:sys.cat.spread.config";

    @Override
    public List<CamelCaseMap<String, Object>> logList(QueryWrapper queryWrapper) {
        return userSpreadLogMapper.logList(queryWrapper);
    }

}
