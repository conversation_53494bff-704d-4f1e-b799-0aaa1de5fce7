package com.cat.modules.stats.service;

import cn.hutool.core.date.DateTime;
import com.cat.modules.stats.entity.StatsAppVisit;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cat.util.DataRow;

import java.util.Date;
import java.util.List;


public interface IStatsAppVisitService extends IService<StatsAppVisit> {

    
    StatsAppVisit nowVisit();

    
    DataRow realActiveBetweenDay(String event,Date start,Date end);

    
    List<DataRow> platformPieChart(Date start, Date end);

    
    List<DataRow> versionPieChart(DateTime start, DateTime end);

    
    List<DataRow> brandPieChart(DateTime start, DateTime end);

    
    List<DataRow> channelPieChart(DateTime start, DateTime end);

    
    List<DataRow> userTrendChart(DateTime start, DateTime end);

    List<DataRow> registerUser(DateTime start, DateTime end);
}
