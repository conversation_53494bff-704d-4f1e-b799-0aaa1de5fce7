package com.cat.modules.pet.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.util.Date;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("pet_info")
public class PetInfo extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "pet_id",type = IdType.UUID)
    @TableField("pet_id")
    private String petId;

    
    @TableField(value = "avatar_url",insertStrategy = FieldStrategy.NOT_EMPTY,updateStrategy = FieldStrategy.NOT_EMPTY)
    private String avatarUrl;

    
    @TableField("pet_name")
    private String petName;

    
    @TableField("pet_type")
    private String petType;

    
    @TableField("variety_type")
    private String varietyType;

    
    @TableField("birth_date")
    private Date birthDate;

    
    @TableField("home_date")
    private Date homeDate;


    
    @TableField("sex")
    private String sex;

    @TableField("profiles")
    private String profiles;

    
    @TableField("color")
    private String color;

    
    @TableField("hait")
    private String hait;

    
    @TableField("weight")
    private int weight;

    
    @TableField("is_sterilization")
    private String isSterilization;

    
    @TableField("is_married")
    private String isMarried;

    
    @TableField("is_yumiao")
    private String isYumiao;

    
    @TableField("user_id")
    private String userId;


    
    @TableField("praise_count")
    private Integer praiseCount;

    
    @TableField("fans_count")
    private Integer fansCount;

    @TableField("food_number")
    private int foodNumber;

    
    @TableField(exist = false)
    private String birthDateStr;

    
    @TableField(exist = false)
    private String homeDateStr;

    
    @TableField("id_number")
    private Integer idNumber;

}
