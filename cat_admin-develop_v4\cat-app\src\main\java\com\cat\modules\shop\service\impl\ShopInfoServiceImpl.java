package com.cat.modules.shop.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cat.modules.shop.entity.ShopInfo;
import com.cat.modules.shop.mapper.ShopInfoMapper;
import com.cat.modules.shop.service.IShopEvaluateService;
import com.cat.modules.shop.service.IShopInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import org.springframework.beans.factory.annotation.*;
import org.springframework.transaction.annotation.*;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
public class ShopInfoServiceImpl extends ServiceImpl<ShopInfoMapper, ShopInfo> implements IShopInfoService {
    @Autowired
    private ShopInfoMapper shopInfoMapper;
    @Autowired
    private IShopEvaluateService shopEvaluateService;


    @Override
    public double updateAvgScore(String shopId) {
        double avgScore = shopEvaluateService.avgScore(shopId);
        BigDecimal b = new BigDecimal(avgScore);
        
        avgScore = b.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
        this.update(new UpdateWrapper<ShopInfo>()
                .set("score",avgScore)
                .eq("id",shopId)
        );
        return avgScore;
    }

}
