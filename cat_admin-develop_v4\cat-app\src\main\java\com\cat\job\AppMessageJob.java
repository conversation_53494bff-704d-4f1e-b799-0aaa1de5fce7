package com.cat.job;

import com.alibaba.fastjson.JSONObject;
import com.cat.config.PInit;
import com.cat.modules.push.entity.PushUniDoing;
import com.cat.modules.push.entity.PushVo;
import com.cat.modules.push.service.PushService;
import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class AppMessageJob {

    @Autowired
    private PushService pushService;
    @Autowired
    private PInit pInit;


    public void execute(String content){
        try{
            
            PushVo pushVo = JSONObject.parseObject(content, PushVo.class);

            PushUniDoing pushUniDoing = new PushUniDoing();
            pushUniDoing.setCid(pushVo.getCid());
            pushUniDoing.setClientType(pushVo.getClientType());
            pushUniDoing.setTitle(pushVo.getTitle());
            pushUniDoing.setContent(pushVo.getContent());
            pushUniDoing.setPayload(JSONObject.toJSONString(pushVo.getPayload()));
            pushUniDoing.setPackageName(pInit.pageName());
            pushUniDoing.setPushApi(pInit.getPushApi());
            pushUniDoing.setUserApi(pInit.getUserApi());
            Map<String, Object> result = pushService.push(pushUniDoing);
            if(Integer.valueOf(result.get("code").toString())!=200){
                log.error("推送失败：",result.get("result"));
            }
        }catch (Exception e){
            log.error("APP消息推送失败：",e);
        }
    }


    public void run(String content) {
        Thread thread = new Thread(){
            public void run(){
                execute(content);
            }
        };
        thread.start();
    }
}
