package com.cat.job;

import com.cat.modules.dynamic.entity.DynamicMedia;
import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.cat.modules.dynamic.service.IDynamicMediaService;
import java.util.List;


@Slf4j
@Component("FilePublicCode")
public class FilePublicCode {

    @Autowired
    private RedisUtil redisUtil;

    private static final String FilePublicCode = "sys_config:sys.file_public_code";

    @Autowired
    private IDynamicMediaService dynamicMediaService;

    public void filePublicCode(){
        List<DynamicMedia> dynamicMediaList = dynamicMediaService.list();
        log.info("dynamicMediaList_count->{}", dynamicMediaList.size());
        for (DynamicMedia dynamicMedia: dynamicMediaList){
            String smallUrl = dynamicMedia.getSmallUrl();
            String smallFileName = StringUtils.substringAfterLast(smallUrl, "/");
            String smallExtension = FilenameUtils.getExtension(smallFileName);
            String smallCode = smallFileName.replace("."+ smallExtension, "");

            String mediaUrl = dynamicMedia.getMediaUrl();
            String mediaFileName = StringUtils.substringAfterLast(mediaUrl, "/");
            String mediaExtension = FilenameUtils.getExtension(mediaFileName);
            String mediaCode = mediaFileName.replace("."+ mediaExtension, "");
            redisUtil.sadd(FilePublicCode, smallCode, mediaCode);
        }
        log.info("添加数据成功");
    }
}

