package com.cat.modules.topic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cat.modules.topic.entity.TopicInfo;

import java.util.List;


public interface ITopicInfoService extends IService<TopicInfo> {

    List<String> imageList(String topicId);

    boolean addTopicRel(String businessId,String businessType,List<String> topics);

    boolean delContentTopic(String businessId,String businessType);

    List<TopicInfo> getByBusinessTopicList(String businessId, String s);


}
