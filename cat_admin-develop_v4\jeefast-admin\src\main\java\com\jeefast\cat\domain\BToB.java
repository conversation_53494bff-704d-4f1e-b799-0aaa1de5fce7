package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                            import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;


/**
 * 外部调用表 b_to_b
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("b_to_b")
public class BToB extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date writeDate;
    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String writeUid;
    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String createUid;
    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String other;
    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Integer isDelete;
    /** $column.columnComment */
    @TableId(value = "user_id", type = IdType.UUID)
    private String userId;
    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String sign;
    /** 渠道功能配置 */
    @Excel(name = "渠道功能配置")
    private String functionConfig;
    /** 扩展字段1 */
    @Excel(name = "扩展字段1")
    private String ext1;
    /** 扩展字段2 */
    @Excel(name = "扩展字段2")
    private String ext2;

    @Excel(name = "渠道id")
    private Integer channelId;
    @Excel(name = "渠道id")
    private String channelName;
    @Excel(name = "医院id")
    private Integer hospitalId;
    @Excel(name = "医院名称")
    private String hospitalName;
}
