package com.cat.modules.audit.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.audit.entity.AuthAuditInfo;
import com.cat.modules.audit.mapper.AuthAuditInfoMapper;
import com.cat.modules.audit.service.IAuthAuditInfoService;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;


@Service

public class AuthAuditInfoServiceImpl extends ServiceImpl<AuthAuditInfoMapper, AuthAuditInfo> implements IAuthAuditInfoService {

}