package com.cat.modules.order.service.impl;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.init.OrderInitBean;
import com.cat.modules.order.entity.OrderInfo;
import com.cat.modules.order.entity.OrderItem;
import com.cat.modules.order.entity.OrderLog;
import com.cat.modules.order.mapper.OrderItemMapper;
import com.cat.modules.order.mapper.OrderLogMapper;
import com.cat.modules.order.mapper.OrderMapper;
import com.cat.modules.order.service.IOrderInfoService;
import com.cat.modules.order.service.IOrderItemService;
import com.cat.modules.order.thread.entity.OrderPrimary;
import com.cat.modules.product.service.IProductSkuService;
import com.cat.modules.user.service.UserService;
import com.jeefast.common.enums.CachEnum;
import com.jeefast.common.exception.BusinessException;
import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.swing.*;
import java.util.Date;
import java.util.List;


@Slf4j
@Service
public class OrderInfoServiceImpl extends ServiceImpl<OrderMapper, OrderInfo> implements IOrderInfoService {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private OrderItemMapper orderItemMapper;
    @Autowired
    private OrderLogMapper orderLogMapper;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IProductSkuService productSkuService;


    @Override
    public List<CamelCaseMap> listExt(QueryWrapper<OrderInfo> qw) {
        return orderMapper.listExt(qw);
    }

    @Override
    @Transactional
    public boolean ship(OrderInfo orderInfo) {
        String orderId = orderInfo.getId();
        String logistics = orderInfo.getLogistics();
        String logisticsNum = orderInfo.getLogisticsNum();
        OrderInfo saveOrderInfo = this.getById(orderId);
        if(40 == saveOrderInfo.getStatus()){
            throw new BusinessException("该订单已过发货");
        }

        boolean flag = this.update(new UpdateWrapper<OrderInfo>()
                .set("logistics",logistics)
                .set("logistics_num",logisticsNum)
                .set("status",40)
                .set("send_date",new Date())
                .eq("id",orderId)
        );
        orderItemMapper.update(null,new UpdateWrapper<OrderItem>()
                .set("logistics",logistics)
                .set("logistics_num",logisticsNum)
                .set("send_date",new Date())
                .eq("order_id",orderId)
        );
        OrderLog orderLog = new OrderLog();
        orderLog.setOrderId(orderId);
        orderLog.setUserId(saveOrderInfo.getUserId());
        orderLog.setBusinessId(saveOrderInfo.getBusinessId());
        orderLog.setBusinessType(saveOrderInfo.getBusinessType());
        orderLog.setStatus(40);
        orderLogMapper.insert(orderLog);

        
        
        
        
        
        return flag;
    }

    @Override
    public OrderPrimary selectByPrimaryKey(String orderId) {
        return orderMapper.selectByPrimaryKey(orderId);
    }

    @Override
    @Transactional
    public boolean receipt(OrderLog orderLog) {
        int i = this.count(new QueryWrapper<OrderInfo>().eq("id",orderLog.getOrderId()));
        if(i==0){
            log.error("订单不存在{}",orderLog.getOrderId());
        }
        UpdateWrapper<OrderInfo> ew = new UpdateWrapper<OrderInfo>();
        ew.set("status",50);
        ew.set("sign_date",new Date());
        ew.set("end_date",new Date());
        ew.eq("id",orderLog.getOrderId());
        boolean flag = this.update(ew);
        
        orderLog.setBusinessId(orderLog.getOrderId());
        orderLog.setBusinessType("1");
        orderLog.setOrderId(orderLog.getOrderId());
        orderLog.setStatus(50);
        orderLogMapper.insert(orderLog);
        return flag;
    }

    @Override
    @Transactional
    public boolean cancel(OrderLog orderLog) {
        String orderId = orderLog.getOrderId();
        int i = this.count(new QueryWrapper<OrderInfo>().eq("id",orderId));
        if(i==0){
            log.error("订单不存在{}",orderId);
        }
        UpdateWrapper<OrderInfo> ew = new UpdateWrapper<OrderInfo>();
        ew.set("status",0);
        ew.eq("id",orderId);
        boolean flag = this.update(ew);

        List<OrderItem> orderItemList = orderItemMapper.selectList(new QueryWrapper<OrderItem>().eq("order_id",orderId));
        if(orderItemList!=null){
            
            for (OrderItem orderItem : orderItemList) {
                
                productSkuService.addStock(orderItem.getProductSkuId(),orderItem.getQuantity());
            }
        }
        
        orderLog.setBusinessId(orderId);
        orderLog.setBusinessType("1");
        orderLog.setOrderId(orderId);
        orderLog.setStatus(0);
        orderLogMapper.insert(orderLog);
        return false;
    }

    
    @Override
    public Boolean singTimeOut(){
        
        Long signTimeOut = Long.valueOf(redisUtil.get(CachEnum.SYS_CONFIG.getCode()+"sys.cat.order.signTimeOut"));
        long time = new Date().getTime(); 
        long errorTime = 1 * 24 * 60 * 60 * 1000;
        Date startDate = new Date(time - ((signTimeOut * 60 * 1000)+errorTime));
        Date endDate = new Date(time - signTimeOut * 60 * 1000);

        List<OrderInfo> orderList = this.list(new QueryWrapper<OrderInfo>()
                .eq("status",40).ge("send_date",startDate).le("send_date",endDate)
        );
        for (OrderInfo orderInfo : orderList) {
            UpdateWrapper<OrderInfo> ew = new UpdateWrapper<OrderInfo>();
            ew.set("status",50);
            ew.set("sign_date",new Date());
            ew.set("end_date",new Date());
            ew.eq("id",orderInfo.getId());
            boolean flag = this.update(ew);
            OrderLog orderLog = new OrderLog();
            
            orderLog.setBusinessId(orderInfo.getId());
            orderLog.setBusinessType("1");
            orderLog.setOrderId(orderInfo.getId());
            orderLog.setStatus(50);
            orderLog.setRemark("收货超时系统自动收货");
            orderLogMapper.insert(orderLog);
        }
        return true;
    }

    @Override
    public Boolean cancelTimeOut() {
        
        Long cancelTimeOut = Long.valueOf(redisUtil.get(CachEnum.SYS_CONFIG.getCode()+"sys.cat.order.cancelTimeOut"));
        long time = new Date().getTime(); 
        long errorTime = 1 * 24 * 60 * 60 * 1000;
        Date startDate = new Date(time - ((cancelTimeOut * 60 * 1000)+errorTime));
        Date endDate = new Date(time - cancelTimeOut * 60 * 1000);

        List<OrderInfo> orderList = this.list(new QueryWrapper<OrderInfo>()
                .eq("status",10).ge("send_date",startDate).le("send_date",endDate)
        );
        for (OrderInfo orderInfo : orderList) {
            UpdateWrapper<OrderInfo> ew = new UpdateWrapper<OrderInfo>();
            ew.set("status",0);
            ew.eq("id",orderInfo.getId());
            boolean flag = this.update(ew);

            List<OrderItem> orderItemList = orderItemMapper.selectList(new QueryWrapper<OrderItem>().eq("order_id",orderInfo.getId()));
            if(orderItemList!=null){
                
                for (OrderItem orderItem : orderItemList) {
                    
                    productSkuService.addStock(orderItem.getProductSkuId(),orderItem.getQuantity());
                }
            }
        }

        return true;
    }


}
