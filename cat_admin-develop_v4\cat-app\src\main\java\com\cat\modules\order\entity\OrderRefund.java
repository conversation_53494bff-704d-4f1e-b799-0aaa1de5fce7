package com.cat.modules.order.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class OrderRefund extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId("id")
    @TableField("id")
    private String id;

    
    @TableField("order_id")
    private String orderId;

    
    @TableField("order_no")
    private String orderNo;

    
    @TableField("order_amount")
    private BigDecimal orderAmount;

    
    @TableField("order_item_id")
    private String orderItemId;

    
    @TableField("refund_no")
    private String refundNo;

    
    @TableField("out_trade_no")
    private String outTradeNo;

    
    @TableField("out_refund_no")
    private String outRefundNo;

    
    @TableField("payplat_form")
    private Integer payplatForm;

    
    @TableField("user_id")
    private String userId;

    
    @TableField("goods_num")
    private Integer goodsNum;

    
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    
    @TableField("apply_type")
    private Integer applyType;

    
    @TableField("refund_sts")
    private Integer refundSts;

    
    @TableField("return_money_sts")
    private Integer returnMoneySts;

    
    @TableField("handel_date")
    private Date handelDate;

    
    @TableField("refund_date")
    private Date refundDate;

    
    @TableField("photo_files")
    private String photoFiles;

    
    @TableField("buyer_msg")
    private String buyerMsg;

    
    @TableField("seller_msg")
    private String sellerMsg;

    
    @TableField("logistics")
    private String logistics;

    
    @TableField("logistics_num")
    private String logisticsNum;

    
    @TableField("ship_date")
    private Date shipDate;

    
    @TableField("receive_date")
    private Date receiveDate;

    
    @TableField("receive_message")
    private String receiveMessage;

    
    @TableField("shop_id")
    private String shopId;

    
    @TableField("update_date")
    private Date updateDate;



}
