package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jeefast.cat.domain.HotSearch;
import com.jeefast.common.enums.YesNoEnum;
import com.jeefast.common.exception.BusinessException;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.DynamicCategory;
import com.jeefast.cat.service.IDynamicCategoryService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 动态分类Controller
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Controller
@RequestMapping("/cat/category")
public class DynamicCategoryController extends BaseController {
    private String prefix = "cat/category";

    @Autowired
    private IDynamicCategoryService dynamicCategoryService;

    @RequiresPermissions("cat:category:view")
    @GetMapping()
    public String category() {
        return prefix + "/category";
    }

    /**
     * 查询动态分类列表
     */
    @RequiresPermissions("cat:category:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(DynamicCategory dynamicCategory) {
        QueryWrapper<DynamicCategory> queryWrapper = getDynamicCategoryQueryWrapper(dynamicCategory);
        startPage();
        return getDataTable(dynamicCategoryService.list(queryWrapper));
    }

    private static QueryWrapper<DynamicCategory> getDynamicCategoryQueryWrapper(DynamicCategory dynamicCategory) {
        QueryWrapper<DynamicCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(dynamicCategory.getTitle()), "title", dynamicCategory.getTitle());
        queryWrapper.eq(StringUtils.isNotEmpty(dynamicCategory.getBeDefault()), "be_default", dynamicCategory.getBeDefault());
        queryWrapper.eq(StringUtils.isNotEmpty(dynamicCategory.getBeDisabled()), "be_disabled", dynamicCategory.getBeDisabled());
        // 特殊查询时条件需要进行单独组装
        Map<String, Object> params = dynamicCategory.getParams();
        if (StringUtils.isNotEmpty(params)) {
            queryWrapper.ge(StringUtils.isNotEmpty((String) params.get("beginCreateDate")), "create_date", params.get("beginCreateDate") + " 00:00:00");
            queryWrapper.le(StringUtils.isNotEmpty((String) params.get("endCreateDate")), "create_date", params.get("endCreateDate") + " 23:59:59");
        }
        queryWrapper.orderByDesc("be_default","sort_no");
        return queryWrapper;
    }

    /**
     * 导出动态分类列表
     */
    @RequiresPermissions("cat:category:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(DynamicCategory dynamicCategory) {
        QueryWrapper<DynamicCategory> queryWrapper = getDynamicCategoryQueryWrapper(dynamicCategory);
        List<DynamicCategory> list = dynamicCategoryService.list(queryWrapper);
        ExcelUtil<DynamicCategory> util = new ExcelUtil<DynamicCategory>(DynamicCategory.class);
        return util.exportExcel(list, "动态分类");
    }

    /**
     * 新增动态分类
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存动态分类
     */
    @RequiresPermissions("cat:category:add")
    @Log(title = "动态分类", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(DynamicCategory dynamicCategory) {
        int sameSortNO = dynamicCategoryService.count(new LambdaQueryWrapper<DynamicCategory>()
                .eq(DynamicCategory::getSortNo, dynamicCategory.getSortNo()));
        if (sameSortNO > 0) {
            throw new BusinessException("排序号重复，请检查");
        }
        return toAjax(dynamicCategoryService.save(dynamicCategory));
    }

    /**
     * 修改动态分类
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        DynamicCategory dynamicCategory = dynamicCategoryService.getById(id);
        mmap.put("dynamicCategory", dynamicCategory);
        return prefix + "/edit";
    }

    /**
     * 修改保存动态分类
     */
    @RequiresPermissions("cat:category:edit")
    @Log(title = "动态分类", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(DynamicCategory dynamicCategory) {
        int sameSortNO = dynamicCategoryService.list(new LambdaQueryWrapper<DynamicCategory>()
                .eq(DynamicCategory::getSortNo, dynamicCategory.getSortNo())
                .ne(DynamicCategory::getId, dynamicCategory.getId())).size();
        if (sameSortNO > 0) {
            throw new BusinessException("排序号重复，请检查");
        }
        return toAjax(dynamicCategoryService.updateById(dynamicCategory));
    }

    /**
     * 删除动态分类
     */
    @RequiresPermissions("cat:category:remove")
    @Log(title = "动态分类", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(dynamicCategoryService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
