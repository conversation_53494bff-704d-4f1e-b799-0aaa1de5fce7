package com.cat.modules.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class Shopping extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id",type = IdType.UUID)
    @TableField("id")
    private String id;

    

    @TableField("user_id")
    private String userId;

    

    @TableField("order_id")
    private String orderId;

    

    @TableField("user_name")
    private String userName;

    

    @TableField("mobile")
    private String mobile;

    

    @TableField("longitude")
    private BigDecimal longitude;

    

    @TableField("latitude")
    private BigDecimal latitude;

    

    @TableField("province")
    private String province;

    

    @TableField("province_code")
    private String provinceCode;

    

    @TableField("city")
    private String city;

    

    @TableField("city_code")
    private String cityCode;

    

    @TableField("district")
    private String district;

    

    @TableField("district_code")
    private String districtCode;


    
    @TableField("street")
    private String street;

    
    @TableField("street_code")
    private String streetCode;

    
    @TableField("adcode")
    private String adcode;


    
    @TableField("regions")
    private String regions;


    

    @TableField("address")
    private String address;

    
    @TableField("logistics")
    private String logistics;

    
    @TableField("logistics_num")
    private String logisticsNum;

    

    @TableField("update_date")
    private Date updateDate;



}
