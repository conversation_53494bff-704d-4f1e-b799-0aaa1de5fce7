package com.cat.job;

import cn.hutool.core.img.Img;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cat.modules.dynamic.entity.DynamicInfo;
import com.cat.modules.dynamic.service.IDynamicInfoService;
import com.cat.modules.pet.entity.PetInfo;
import com.cat.modules.pet.service.IPetInfoService;
import com.cat.modules.user.entity.User;
import com.cat.modules.user.service.UserService;
import com.cat.util.HttpFile;
import com.jeefast.common.config.QcloudConfig;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.SpringContextHolder;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.transfer.TransferManager;
import com.qcloud.cos.transfer.Upload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


@Slf4j
@Component("catDataJob")
public class CatDataJob {

    @Autowired
    private QcloudConfig qcloudConfig;
    @Autowired
    private UserService userService;
    @Autowired
    private IDynamicInfoService dynamicInfoService;
    @Autowired
    private IPetInfoService petInfoService;
    @Autowired
    private HttpFile httpFile;

    private static String url = "https://wecat.maolaile.cn/api/v1/cat/";

    private static String bucketName = "chongyou-1251683186";

    
    private static String getYm = "https://chongyou-1251683186.cos.ap-guangzhou.myqcloud.com";


    
    public void main(Integer page,Integer countPage,String token){
        int pageSize=10;
        int pageNum=1;
        while(true){
            if(pageNum==countPage){
                break;
            }
            pageNum++;
            Map<String,String> headers = new HashMap<String,String>(){{
                put("token",token);
            }};
            String result =  HttpRequest.get(url+(page+1)).addHeaders(headers).execute().body();
            JSONObject resultJson = JSONObject.parseObject(result);
            String falg = resultJson.getString("error_code");
            if("0".equals(falg)){
                
                String userId = userService.getRandPetUserId();
                if(StrUtil.isEmpty(userId)){
                    log.info("没有满足要求的用户绑定！");
                    break;
                }

                
                JSONObject catInfo = resultJson.getJSONObject("data").getJSONObject("cat_info");
                saveCatInfo(catInfo,userId);
            }else{
                log.error("接口调用异常："+resultJson);
            }
            page++;
        }
        log.info("==============程序运行完成批次id="+page+"================");
    }


    
    public void saveCatInfo(JSONObject catInfo,String userId){

        String catName = catInfo.getString("name");
        String headimg = catInfo.getString("headimg");
        
        String sex = catInfo.getString("sex");
        if("-1".equals(sex)){
            sex="2";
        }
        String desc = catInfo.getString("desc");
        
        Date shengri = catInfo.getDate("shengri");
        Date homeDate = shengri;
        
        String is_sterilized = catInfo.getString("is_sterilized");
        if("-1".equals(is_sterilized)){
            is_sterilized="0";
        }
        
        double weight = catInfo.getDouble("weight");
        int weightInt = Integer.valueOf((int) (weight*1000));
        
        String varietyType = catInfo.getJSONObject("variety").getString("id");

        
        String avatarUrl ="";
        try{
            avatarUrl = uploadImage(headimg);
        }catch (Exception e){
            log.error("");
        }
        
        Calendar c = Calendar.getInstance();
        c.setTime(shengri);
        c.add(Calendar.DATE, -65);
        
        

        
        PetInfo petInfo = new PetInfo();
        petInfo.setIsSterilization(is_sterilized);
        petInfo.setPetType("1");
        petInfo.setPetName(catName);
        petInfo.setAvatarUrl(avatarUrl);
        petInfo.setSex(sex);
        petInfo.setWeight(weightInt);
        petInfo.setProfiles(desc);
        petInfo.setBirthDate(c.getTime());
        petInfo.setHomeDate(homeDate);
        petInfo.setVarietyType(varietyType);
        petInfo.setUserId(userId);
        petInfo.setCreateDate(new Date());
        petInfoService.save(petInfo);
    }




    public List<String> uploadImage(JSONArray picsList){
        if(picsList==null){
            return new ArrayList();
        }
        
        
        ExecutorService threadPool = Executors.newFixedThreadPool(2);
        
        TransferManager transferManager = new TransferManager(getCosClient(), threadPool);
        List<String> images = new ArrayList();
        for(int k=0;k<picsList.size();k++){
            String imageUrl = picsList.getJSONObject(k).getJSONObject("large").getString("url");
            
            String key=imageUrl.substring(imageUrl.lastIndexOf("/"),imageUrl.length());
            File localFile = httpFile.getImageFromNetByUrl(imageUrl);
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, localFile);
            
            Upload upload = transferManager.upload(putObjectRequest);
            
            try {
                
                upload.waitForCompletion();
                String saveImageUrl = getYm+key;
                images.add(saveImageUrl);
            } catch (InterruptedException e) {
                log.error("",e);
            }
        }
        
        transferManager.shutdownNow();
        return images;
    }



    public String uploadImage(String imageUrl){
        RedisUtil redisUtil = SpringContextHolder.getBean(RedisUtil.class);
        String path = redisUtil.get(RedisEnum.SYS_CONFIG.getCode()+"sys.upload.path");
        String key=imageUrl.substring(imageUrl.lastIndexOf("/"),imageUrl.length());
        File localFile = httpFile.getImageFromNetByUrl(imageUrl);
        File uploadFile = FileUtil.file(path+key);
        
        Img.from(localFile).setQuality(0.8).write(uploadFile);
        String uploadPath = "/cat"+key;
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, uploadPath, uploadFile);
        COSClient cosClient = getCosClient();
        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
        return getYm+uploadPath;
    }

    public COSClient getCosClient(){
        
        COSCredentials cred = new BasicCOSCredentials(qcloudConfig.getSecretId(), qcloudConfig.getSecretKey());
        
        
        ClientConfig clientConfig = new ClientConfig(new Region("ap-guangzhou"));
        
        COSClient cosClient = new COSClient(cred, clientConfig);
        
        
        return cosClient;
    }


    
    public boolean isExistContent(String txt,String userName){
        if(StrUtil.isEmpty(txt)){
            return true;
        }
        User user = userService.getOne(new QueryWrapper<User>()
                .eq("user_name",userName)
        );
        if(user==null){
            return true;
        }else{
            String userId = user.getUserId();
            DynamicInfo dynamicInfo = dynamicInfoService.getOne(new QueryWrapper<DynamicInfo>()
                    .eq("user_id", userId)
                    .eq("content", txt)
            );
            if(dynamicInfo==null){
                return true;
            }
        }
        return false;
    }


    
    public User existUser(String userName, String userImageUrl, String profiles){
        User user = userService.getOne(new QueryWrapper<User>()
                .eq("user_name",userName)
        );
        String userId = null;
        if(user==null){
            
            userImageUrl = uploadImage(userImageUrl);
            
            Random random = new Random();
            
            String gender = String.valueOf(random.nextInt(1));
            user = new User();
            user.setProvince(profiles);
            user.setSex(gender);
            user.setAvatarUrl(userImageUrl);
            user.setUserName(userName);
            userService.save(user);
        }
        return user;
    }

    public static LocalDate date2LocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        
        return instant.atZone(zoneId).toLocalDate();
    }


}
