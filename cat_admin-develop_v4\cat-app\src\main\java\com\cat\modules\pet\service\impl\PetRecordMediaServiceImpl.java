package com.cat.modules.pet.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.pet.entity.PetRecordMedia;
import com.cat.modules.pet.mapper.PetRecordMediaMapper;
import com.cat.modules.pet.service.IPetRecordMediaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class PetRecordMediaServiceImpl extends ServiceImpl<PetRecordMediaMapper, PetRecordMedia> implements IPetRecordMediaService {
    @Autowired
    private PetRecordMediaMapper petRecordMediaMapper;

}
