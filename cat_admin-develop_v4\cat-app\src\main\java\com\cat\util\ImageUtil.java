package com.cat.util;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

@Slf4j
public class ImageUtil {


    public static int[]  getWidHigh(String path){
        
        BufferedImage bufferedImage = null;
        int[] retult = new int[2];
        try {
            bufferedImage = ImageIO.read(new File(path));
            int width = bufferedImage.getWidth();
            int height = bufferedImage.getHeight();
            retult[0] = width;
            retult[1] = height;
        } catch (IOException e) {
            log.error("",e);
        }
        return retult;
    }

}
