package com.jeefast.cat.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * AI分诊渠道表 channel
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("channel")
public class Channel extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 渠道编码
     */
    @Excel(name = "渠道编码")
    private String channelCode;
    /**
     * 渠道名称
     */
    @Excel(name = "渠道名称")
    private String channelName;
    /**
     * 状态 1启用 2停止
     */
    @Excel(name = "状态 1启用 2停止")
    private Integer status;
    /**
     * 是否删除 1是0否
     */
    @Excel(name = "是否删除 1是0否")
    private Integer isDelete;

    @TableField(exist = false)
    private Boolean selected;
}
