package com.jeefast.cat.job;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jeefast.cat.req.AddDynamicReq;
import com.jeefast.cat.service.IDynamicInfoBackstageService;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 拉取微信公众号最新的博文文章
 */
@Slf4j
@Component
public class WeChatBlogPullJob {
    @Autowired
    private IDynamicInfoBackstageService dynamicInfoBackstageService;

    @Autowired
    private RedisUtil redisUtil;

    private final String WECHAT_BLOG_CONF = "wechat.blog.conf";

    private static final String WECHAT_BLOG_ACCESS_TOKEN_KEY = "sys.cat.wechat.blog.token";


    public void pull() {
        String wechatConfJsonStr = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + WECHAT_BLOG_CONF);
        JSONObject wechatConf = JSON.parseObject(wechatConfJsonStr);
        String accessToken = getAccessToken();
        if (StrUtil.isEmpty(accessToken)) {
            log.error("无法获取到微信accessToken，退出...");
            return;
        }
        String getBlogUrl = wechatConf.getString("getBlogUrl") + "?access_token=" + accessToken;
        Map<String, Object> param = new HashMap<>();
        param.put("offset", 0);
        param.put("count", 20);
        // 不返回html内容
        param.put("no_content", 1);
        String blogResult = HttpRequest.post(getBlogUrl)
                .body(JSON.toJSONString(param))
                .execute().body();
        log.info("微信博文返回：blogResult->{}", blogResult);
        JSONObject blogJson = JSON.parseObject(blogResult);
        JSONArray items = blogJson.getJSONArray("item");
        LocalDateTime yesterdayStart = LocalDate.now().minusDays(1).atTime(LocalTime.MIN);
        LocalDateTime yesterdayEnd = LocalDate.now().minusDays(1).atTime(LocalTime.MAX);
        List<JSONObject> list = new ArrayList<>();
        for (int i = 0; i < items.size(); i++) {
            JSONObject item = items.getJSONObject(i);
            Long updateTime = item.getLong("update_time");
            LocalDateTime blogTime = Instant.ofEpochSecond(updateTime).atZone(ZoneId.systemDefault()).toLocalDateTime();
            if (blogTime.isAfter(yesterdayStart) && blogTime.isBefore(yesterdayEnd)) {
                JSONArray newsItems = item.getJSONObject("content").getJSONArray("news_item");
                for (int k = 0; k < newsItems.size(); k++) {
                    JSONObject newsItem = newsItems.getJSONObject(k);
                    list.add(newsItem);
                }
            } else {
                log.info("博文日期->{}不符合抽取时间，跳过...", blogTime);
            }
        }
        if (list.isEmpty()) {
            log.warn("没有需要添加的博文...");
            return;
        }
        for (JSONObject item : list) {
            AddDynamicReq dynamicReq = new AddDynamicReq();
            dynamicReq.setArticleType("wechat");
            dynamicReq.setUserId(item.getString("author"));
            dynamicReq.setSource("4");
            dynamicReq.setType("1");
            dynamicReq.setTitle(item.getString("title"));
            dynamicReq.setContent(item.getString("url"));
            dynamicReq.setCoverImage(item.getString("thumb_url"));
            dynamicReq.setIsCheck("1");
            dynamicInfoBackstageService.addSave(dynamicReq);
        }
    }

    private String getAccessToken() {
        if (redisUtil.exists(WECHAT_BLOG_ACCESS_TOKEN_KEY)) {
            log.info("缓存取出微信AccessToken...");
            return redisUtil.get(WECHAT_BLOG_ACCESS_TOKEN_KEY);
        } else {
            String wechatConfJsonStr = redisUtil.get(RedisEnum.SYS_CONFIG.getCode() + WECHAT_BLOG_CONF);
            JSONObject wechatConf = JSON.parseObject(wechatConfJsonStr);
            String getTokenUrl = wechatConf.getString("getTokenUrl");
            String appId = wechatConf.getString("appId");
            String appSecret = wechatConf.getString("appSecret");
            String accessJsonStr = "";
            try {
                Map<String, Object> getTokenParam = new HashMap<>();
                getTokenParam.put("grant_type", "client_credential");
                getTokenParam.put("appid", appId);
                getTokenParam.put("secret", appSecret);
                accessJsonStr = HttpUtil.get(getTokenUrl, getTokenParam);
                log.info("微信响应：{}", accessJsonStr);
            } catch (Exception e) {
                log.error("无法获取微信AccessToken! accessJsonStr->{}", accessJsonStr);
                return null;
            }
            JSONObject accessJson = JSON.parseObject(accessJsonStr);
            String accessToken = accessJson.getString("access_token");
            Integer expiresIn = accessJson.getInteger("expires_in");
            if (StrUtil.isEmpty(accessToken) || expiresIn == null) {
                log.error("无法获取微信AccessToken!");
                return null;
            }
            redisUtil.set(WECHAT_BLOG_ACCESS_TOKEN_KEY, accessToken);
            redisUtil.expire(WECHAT_BLOG_ACCESS_TOKEN_KEY, expiresIn);
            return accessToken;
        }
    }

//    public static void main(String[] args) {
//        String blogResult = "{\n" +
//                "\t\"item\": [\n" +
//                "\t\t{\n" +
//                "\t\t\t\"article_id\": \"8sHibCxmPJCyiHUf9Gkp5h8uwKiZVo28KPmefb6o5CZabOuF6dxTYi487d4LlHvQ\",\n" +
//                "\t\t\t\"content\": {\n" +
//                "\t\t\t\t\"news_item\": [\n" +
//                "\t\t\t\t\t{\n" +
//                "\t\t\t\t\t\t\"title\": \"测试程序获取图文文章\",\n" +
//                "\t\t\t\t\t\t\"author\": \"lshuai\",\n" +
//                "\t\t\t\t\t\t\"digest\": \"\",\n" +
//                "\t\t\t\t\t\t\"content_source_url\": \"\",\n" +
//                "\t\t\t\t\t\t\"thumb_media_id\": \"\",\n" +
//                "\t\t\t\t\t\t\"show_cover_pic\": 0,\n" +
//                "\t\t\t\t\t\t\"url\": \"http://mp.weixin.qq.com/s?__biz=MzIyOTg5NDQwOQ==&mid=2247483662&idx=1&sn=6d33105a96eaa3d470ddd1652d72fdb2&chksm=e8baf168dfcd787ece9c3b1f6002a1ac8bd723cf136e41c12924089e92cbbd0f82591f9d8d4c#rd\",\n" +
//                "\t\t\t\t\t\t\"thumb_url\": \"https://mmbiz.qpic.cn/sz_mmbiz_jpg/knZjibOib5ZiaOxZb1UsCJUhz5cyH0k5GLlx7P2ujlaYxYuDMtZEAIwz4fVevngq3TruwUOJcF35d6oor8JE0zc8w/0?wx_fmt=jpeg\",\n" +
//                "\t\t\t\t\t\t\"need_open_comment\": 1,\n" +
//                "\t\t\t\t\t\t\"only_fans_can_comment\": 0,\n" +
//                "\t\t\t\t\t\t\"is_deleted\": false\n" +
//                "\t\t\t\t\t}\n" +
//                "\t\t\t\t],\n" +
//                "\t\t\t\t\"create_time\": 1728368607,\n" +
//                "\t\t\t\t\"update_time\": 1728368645\n" +
//                "\t\t\t},\n" +
//                "\t\t\t\"update_time\": 1728446400\n" +
//                "\t\t}\n" +
//                "\t],\n" +
//                "\t\"total_count\": 1,\n" +
//                "\t\"item_count\": 1\n" +
//                "}";
//        JSONObject blogJson = JSON.parseObject(blogResult);
//        JSONArray items = blogJson.getJSONArray("item");
//        LocalDateTime yesterdayStart = LocalDate.now().minusDays(1).atTime(LocalTime.MIN);
//        LocalDateTime yesterdayEnd = LocalDate.now().minusDays(1).atTime(LocalTime.MAX);
//        List<JSONObject> list = new ArrayList<>();
//        for (int i = 0; i < items.size(); i++) {
//            JSONObject item = items.getJSONObject(i);
//            Long updateTime = item.getLong("update_time");
//            LocalDateTime blogTime = Instant.ofEpochSecond(updateTime).atZone(ZoneId.systemDefault()).toLocalDateTime();
//            if (blogTime.isAfter(yesterdayStart) && blogTime.isBefore(yesterdayEnd)) {
//                JSONArray newsItems = item.getJSONObject("content").getJSONArray("news_item");
//                for (int k = 0; k < newsItems.size(); k++) {
//                    JSONObject newsItem = newsItems.getJSONObject(k);
//                    list.add(newsItem);
//                }
//            } else {
//                log.info("博文日期->{}不符合抽取时间，跳过...", blogTime);
//            }
//        }
//        for (JSONObject item : list) {
//            AddDynamicReq dynamicReq = new AddDynamicReq();
//            dynamicReq.setArticleType("微信公众号");
//            dynamicReq.setUserId(item.getString("author"));
//            dynamicReq.setSource("4");
//            dynamicReq.setType("1");
//            dynamicReq.setTitle("title");
//            dynamicReq.setContent(item.getString("url"));
//            DynamicInfoBackstageServiceImpl service = new DynamicInfoBackstageServiceImpl();
//            service.addSave(dynamicReq);
//            //dynamicInfoBackstageService.addSave(dynamicReq);
//        }
//    }
}
