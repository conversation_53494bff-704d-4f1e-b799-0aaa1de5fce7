package com.cat.modules.article.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ArticleViewLog extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "article_view_id")
    @TableField("article_view_id")
    private String articleViewId;

    
    @TableField("article_id")
    private String articleId;

    
    @TableField("user_id")
    private String userId;



}
