package com.cat.util;

import java.util.Collection;
import java.util.Map;


public enum BlankUtil {
    ;

    
    public static boolean isEmpty(Collection collection) {
        if (collection == null) {
            return true;
        } else if (collection.size() <= 0) {
            return true;
        }
        return false;
    }

    
    public static boolean isEmpty(String str) {
        if (str == null) {
            return true;
        } else if (str.trim().length() <= 0) {
            return true;
        }
        return false;
    }

    
    public static <T extends Map> boolean isEmpty(T t) {
        if (t == null) {
            return true;
        } else if (t.keySet().size() <= 0) {
            return true;
        }
        return false;
    }

    
    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }
        if (obj instanceof String) {
            return isEmpty((String) obj);
        }

        return false;
    }

    
    public static boolean isEmpty(Object[] objs) {
        if (objs == null) {
            return true;
        }
        if (objs.length <= 0) {
            return true;
        }

        return false;
    }

    
    public static boolean isExistsEmpty(Object...objs) {
        for(Object obj:objs){
            if(isEmpty(obj)){
                return true;
            }
        }
        return false;
    }

    
    public static boolean isNotEmpty(Collection collection) {
        return !isEmpty(collection);
    }

    
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    
    public static <T extends Map> boolean isNotEmpty(T t) {
        return !isEmpty(t);
    }

    
    public static boolean isNotEmpty(Object obj) {
        return !isEmpty(obj);
    }

    
    public static boolean isNotEmpty(Object[] objs) {
        return !isEmpty(objs);
    }
}
