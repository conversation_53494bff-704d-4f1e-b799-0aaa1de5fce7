package com.cat.modules.attention.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cat.modules.attention.entity.AttentionInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


public interface AttentionInfoMapper extends BaseMapper<AttentionInfo> {

    Integer updateToUserNewDynamicNum(@Param("toUserId") String toUserId,@Param("diffNum") Integer diffNum,@Param("dynamicDate") Date dynamicDate);
}
