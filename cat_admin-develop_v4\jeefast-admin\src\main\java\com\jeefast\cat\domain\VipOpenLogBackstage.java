package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                            import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * vip开通记录表 vip_open_log
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("vip_open_log")
public class VipOpenLogBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /** 用户id */
    @Excel(name = "用户id")
    private String userId;
    /** vip开通配置id */
    @Excel(name = "vip开通配置id")
    private String configId;
    /** vip开始时间 */
    @Excel(name = "vip开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date vipStartDate;
    /** vip到期时间 */
    @Excel(name = "vip到期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date vipEndDate;
    /** 创建时间 */
    /*@Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;*/
    /** 备注 */
    @Excel(name = "备注")
    private String remark;
}
