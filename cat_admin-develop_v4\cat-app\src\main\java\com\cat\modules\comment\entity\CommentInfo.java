package com.cat.modules.comment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CommentInfo extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId(value = "comment_id",type = IdType.UUID)
    @TableField("comment_id")
    private String commentId;

    
    @TableField("parent_id")
    private String parentId;

    
    @TableField("parent_ids")
    private String parentIds;

    
    @TableField("content")
    private String content;

    
    @TableField("business_type")
    private String businessType;

    
    @TableField("business_id")
    private String businessId;

    
    @TableField("user_id")
    private String userId;

    
    @TableField("praise_count")
    private Integer praiseCount;

    
    @TableField("is_check")
    private String isCheck;

}
