<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.product.mapper.ProductSkuMapper">
    <!--
    <resultMap type="ProductSku" id="ProductSkuResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="groupKeyId"    column="group_key_id"    />
        <result property="groupValId"    column="group_val_id"    />
        <result property="groupSpace"    column="group_space"    />
        <result property="price"    column="price"    />
        <result property="stock"    column="stock"    />
        <result property="totalSales"    column="total_sales"    />
        <result property="mainimage"    column="mainimage"    />
        <result property="status"    column="status"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
        <result property="shopId"    column="shop_id"    />
    </resultMap>
    -->


    <update id="addStock">
        update product_sku set stock=stock+#{quantity},status=if(stock+#{quantity}<![CDATA[ <= ]]>0,4,1)
        where id=#{productSkuId}
    </update>
</mapper>

