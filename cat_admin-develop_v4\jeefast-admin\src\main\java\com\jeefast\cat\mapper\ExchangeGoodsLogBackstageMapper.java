package com.jeefast.cat.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.ExchangeGoodsLogBackstage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 兑换商品日志 数据层
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
public interface ExchangeGoodsLogBackstageMapper extends BaseMapper<ExchangeGoodsLogBackstage> {

    List<CamelCaseMap<String, Object>> logList(@Param("ew") Wrapper queryWrapper);
}