package com.cat.modules.order.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.order.entity.Shopping;
import com.cat.modules.order.mapper.ShoppingMapper;
import com.cat.modules.order.service.IShoppingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class ShoppingServiceImpl extends ServiceImpl<ShoppingMapper, Shopping> implements IShoppingService {
    @Autowired
    private ShoppingMapper shoppingMapper;

}
