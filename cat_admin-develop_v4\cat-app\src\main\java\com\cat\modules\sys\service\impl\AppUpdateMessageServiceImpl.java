package com.cat.modules.sys.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.sys.entity.AppUpdateMessage;
import com.cat.modules.sys.mapper.AppUpdateMessageMapper;
import com.cat.modules.sys.service.IAppUpdateMessageService;
import com.jeefast.common.utils.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service

public class AppUpdateMessageServiceImpl extends ServiceImpl<AppUpdateMessageMapper, AppUpdateMessage> implements IAppUpdateMessageService {

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private AppUpdateMessageMapper dao;

    private static String androidKey = "we_chat:max_version:android";
    private static String iosKey = "we_chat:max_version:ios";

    @Override
    public boolean refreshVersionCache() {
        AppUpdateMessage androidUpdate = dao.refreshVersionCache(1);
        AppUpdateMessage iosUpdate = dao.refreshVersionCache(2);
        if(ObjectUtil.isNotNull(androidUpdate)){
            redisUtil.set(androidKey, JSONObject.toJSONString(androidUpdate));
        }
        if(ObjectUtil.isNotNull(iosUpdate)){
            redisUtil.set(iosKey, JSONObject.toJSONString(iosUpdate));
        }
        return false;
    }
}