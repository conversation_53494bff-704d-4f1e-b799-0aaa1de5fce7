package com.cat.modules.product.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                            import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;


 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_group_val")
public class ProductGroupVal extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    
    @Excel(name = "商品id")
    private String productId;
    
    @Excel(name = "属性名称")
    private String value;
    
    @Excel(name = "排序值")
    private Integer sortOn;
    
    private String keyId;
    
    
    
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;
    
    @Excel(name = "店铺id")
    private String shopId;
}
