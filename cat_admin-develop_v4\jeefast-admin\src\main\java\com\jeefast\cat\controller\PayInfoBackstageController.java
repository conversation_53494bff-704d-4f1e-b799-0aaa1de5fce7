package com.jeefast.cat.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.PayInfoBackstage;
import com.jeefast.cat.service.IPayInfoBackstageService;
import com.jeefast.common.annotation.Log;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.core.page.TableDataInfo;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 支付信息Controller
 * 
 * <AUTHOR>
 * @date 2020-09-28
 */
@Controller
@RequestMapping("/cat/PayInfoBackstage")
public class PayInfoBackstageController extends BaseController {
    private String prefix = "cat/PayInfoBackstage";

    @Autowired
    private IPayInfoBackstageService payInfoBackstageService;

    @RequiresPermissions("cat:PayInfoBackstage:view")
    @GetMapping()
    public String PayInfoBackstage() {
        return prefix + "/PayInfoBackstage";
    }

    /**
     * 查询支付信息列表
     */
    @RequiresPermissions("cat:PayInfoBackstage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(PayInfoBackstage req) {
        QueryWrapper<PayInfoBackstage> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getOrderNo())) {
    		queryWrapper.like("order_no", req.getOrderNo());
    	} 
    	if(StringUtils.isNotEmpty(req.getUserId())) {
    		queryWrapper.eq("user_id", req.getUserId());
    	}
        if(ObjectUtil.isNotNull(req.getPayplatForm())) {
            queryWrapper.eq("payplat_form", req.getPayplatForm());
        }
        if(StringUtils.isNotEmpty(req.getPlatforMnumber())) {
            queryWrapper.eq("platfor_mnumber", req.getPlatforMnumber());
        }
        if(ObjectUtil.isNotNull(req.getMoney())) {
            queryWrapper.ge("money", req.getMoney());
        }
        if(ObjectUtil.isNotNull(req.getPlatformStatus())) {
            queryWrapper.ge("platform_status", req.getPlatformStatus());
        }
		// 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endCreateDate")), "create_date", params.get("endCreateDate"));

            queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginUpdateDate")), "update_date", params.get("beginUpdateDate"));
            queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endUpdateDate")), "update_date", params.get("endUpdateDate"));
		}
        queryWrapper.orderByDesc("create_date");
        startPage();
        return getDataTable(payInfoBackstageService.list(queryWrapper));
    }

    /**
     * 导出支付信息列表
     */
    @RequiresPermissions("cat:PayInfoBackstage:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(PayInfoBackstage payInfoBackstage) {
        List<PayInfoBackstage> list = payInfoBackstageService.list(new QueryWrapper<>());
        ExcelUtil<PayInfoBackstage> util = new ExcelUtil<PayInfoBackstage>(PayInfoBackstage.class);
        return util.exportExcel(list, "PayInfoBackstage");
    }

    /**
     * 新增支付信息
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存支付信息
     */
    @RequiresPermissions("cat:PayInfoBackstage:add")
    @Log(title = "支付信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(PayInfoBackstage payInfoBackstage) {
        return toAjax(payInfoBackstageService.save(payInfoBackstage));
    }

    /**
     * 修改支付信息
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        PayInfoBackstage payInfoBackstage = payInfoBackstageService.getById(id);
        mmap.put("payInfoBackstage", payInfoBackstage);
        return prefix + "/edit";
    }

    /**
     * 修改保存支付信息
     */
    @RequiresPermissions("cat:PayInfoBackstage:edit")
    @Log(title = "支付信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(PayInfoBackstage payInfoBackstage) {
        return toAjax(payInfoBackstageService.updateById(payInfoBackstage));
    }

    /**
     * 删除支付信息
     */
    @RequiresPermissions("cat:PayInfoBackstage:remove")
    @Log(title = "支付信息", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(payInfoBackstageService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
