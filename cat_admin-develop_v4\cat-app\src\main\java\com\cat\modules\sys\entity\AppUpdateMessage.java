package com.cat.modules.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_update_message")
public class AppUpdateMessage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    
    @Excel(name = "版本号")
    private Integer version;
    
    @Excel(name = "版本名称")
    private String versionName;
    
    @Excel(name = "强制更新")
    private Integer forceUpdate;
    
    @Excel(name = "更新类型")
    private Integer updateType;
    
    @Excel(name = "更新说明")
    private String upDesc;
    
    @Excel(name = "下载地址")
    private String apkUrl;
    
    @Excel(name = "客户端")
    private Integer client;
    
    @Excel(name = "摘要")
    private String remark;
    
    @Excel(name = "文件大小(kb)")
    private String fileSize;

    
    @TableField("min_version")
    private Integer minVersion;

    
    @TableField(exist = false)
    private String minVersionName;

    
    @TableField("apk_entire_url")
    private String apkEntireUrl;

     @TableField("is_pop")
     private Integer popStr;
    
}
