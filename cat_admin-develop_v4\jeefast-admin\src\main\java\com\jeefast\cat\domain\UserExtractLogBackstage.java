package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                            import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 用户提现记录表 user_extract_log
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_extract_log")
public class UserExtractLogBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /** 用户表id */
    @Excel(name = "用户表id")
    private String userId;
    /** 标题 */
    @Excel(name = "标题")
    private String title;
    /** 提款平台 */
    @Excel(name = "提款平台")
    private String extractForm;
    /** 卡账号 */
    @Excel(name = "卡账号")
    private String cardNum;
    /** 卡名称 */
    @Excel(name = "卡名称")
    private String cardName;
    /** 卡持人 */
    @Excel(name = "卡持人")
    private String cardUserName;
    /** 金额 */
    @Excel(name = "金额")
    private Double money;
    /** 提现状态:1-审核中,2-进行中,3-完成,4-失败,5-驳回 */
    @Excel(name = "提现状态")
    private String status;
    /** 创建时间 */
    /*@Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;*/
    /** 备注 */
    @Excel(name = "备注")
    private String remark;
    /** 备注 */
    @Excel(name = "驳回原因")
    private String reason;
}
