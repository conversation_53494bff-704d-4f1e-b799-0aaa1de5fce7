package com.cat.modules.user.service.impl;

import cn.hutool.core.map.CamelCaseMap;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.attention.entity.AttentionInfo;
import com.cat.modules.attention.service.IAttentionInfoService;
import com.cat.modules.user.entity.User;
import com.cat.modules.user.entity.UserLogoutLog;
import com.cat.modules.user.mapper.UserLogoutLogMapper;
import com.cat.modules.user.service.IUserLogoutLogService;
import com.cat.modules.user.service.UserService;
import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Service
public class UserLogoutLogServiceImpl extends ServiceImpl<UserLogoutLogMapper, UserLogoutLog> implements IUserLogoutLogService {
    @Autowired
    private UserLogoutLogMapper userLogoutLogMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private IAttentionInfoService attentionInfoService;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public List<CamelCaseMap> listExt(QueryWrapper<UserLogoutLog> qw) {
        return userLogoutLogMapper.listExt(qw);
    }

    @Override
    @Transactional
    public boolean clearUserInfo(UserLogoutLog userLogoutLog) {
        boolean flag = this.updateById(userLogoutLog);
        if(flag){
            String userId = userLogoutLog.getUserId();
            userService.update(new LambdaUpdateWrapper<User>()
                    .set(User::getIsDelete,"1")
                    .eq(User::getUserId,userId)
            );
            this.delRedisUserData(userId);
        }
        return flag;
    }

    @Override
    public void delRedisUserData(String userId) {
        
        User user = userService.getById(userId);
        String openId = user.getOpenId();
        String mobile = user.getMobile();
        String unionId = user.getUnionId();
        String userNum = user.getUserNum();
        if(StrUtil.isNotEmpty(openId)){
            
            String token = redisUtil.get("we_chat:open_id:"+openId);
            redisUtil.del("we_chat:open_id:"+openId);
            redisUtil.del("we_chat:token:"+token);
        }
        if(StrUtil.isNotEmpty(mobile)){
            
            String token = redisUtil.get("we_chat:open_id:"+mobile);
            redisUtil.del("we_chat:open_id:"+mobile);
            redisUtil.del("we_chat:token:"+token);
        }
        if(StrUtil.isNotEmpty(unionId)){
            
            String token = redisUtil.get("we_chat:open_id:"+unionId);
            redisUtil.del("we_chat:open_id:"+unionId);
            redisUtil.del("we_chat:token:"+token);
        }
        if(StrUtil.isNotEmpty(userNum)){
            
            String token = redisUtil.get("we_chat:open_id:"+userNum);
            redisUtil.del("we_chat:open_id:"+userNum);
            redisUtil.del("we_chat:token:"+token);
        }
        
        
        List<AttentionInfo> likeList = attentionInfoService.list(new LambdaQueryWrapper<AttentionInfo>().eq(AttentionInfo::getUserId,userId));
        
        List<AttentionInfo> fansList = attentionInfoService.list(new LambdaQueryWrapper<AttentionInfo>().eq(AttentionInfo::getToUserId,userId).eq(AttentionInfo::getToType,"1"));
        List<String> likeUserIdList = likeList.stream().map(AttentionInfo::getToUserId).collect(Collectors.toList());
        List<String> fansUserIdList = fansList.stream().map(AttentionInfo::getUserId).collect(Collectors.toList());
        
        if(likeUserIdList.size()>0){
            userService.update(new LambdaUpdateWrapper<User>().setSql("fans_count = fans_count - 1").in(User::getUserId,likeUserIdList));
        }
        if(fansUserIdList.size()>0){
            userService.update(new LambdaUpdateWrapper<User>().setSql("attention_count = attention_count - 1").in(User::getUserId,fansUserIdList));
        }
        
        attentionInfoService.remove(new LambdaQueryWrapper<AttentionInfo>().eq(AttentionInfo::getUserId,userId));
        attentionInfoService.remove(new LambdaQueryWrapper<AttentionInfo>().eq(AttentionInfo::getToUserId,userId).eq(AttentionInfo::getToType,"1"));

    }

}
