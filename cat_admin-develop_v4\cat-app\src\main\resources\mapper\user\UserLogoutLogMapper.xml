<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.user.mapper.UserLogoutLogMapper">

    <!-- 通用查询映射结果  配置了autoMapping=true,其他的无需配置，将会自动映射-->
    <resultMap id="BaseResultMap" type="com.cat.modules.user.entity.UserLogoutLog" autoMapping="true" />


    <select id="listExt" resultType="cn.hutool.core.map.CamelCaseMap">
        select t1.*,t2.user_name,t2.avatar_url
        from user_logout_log t1
        left join user_info t2 on t2.user_id=t1.user_id
        ${ew.customSqlSegment}
    </select>

</mapper>
