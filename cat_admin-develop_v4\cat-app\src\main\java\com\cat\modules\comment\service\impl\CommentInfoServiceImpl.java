package com.cat.modules.comment.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.comment.entity.CommentInfo;
import com.cat.modules.comment.mapper.CommentInfoMapper;
import com.cat.modules.comment.service.ICommentInfoService;
import com.cat.modules.user.service.UserService;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service

public class CommentInfoServiceImpl extends ServiceImpl<CommentInfoMapper, CommentInfo> implements ICommentInfoService {


    @Autowired
    private CommentInfoMapper commentInfoMapper;

    @Autowired
    private UserService userService;

    @Override
    public String getParentIds(String id) {
        return commentInfoMapper.getParentIds(id);
    }

}
