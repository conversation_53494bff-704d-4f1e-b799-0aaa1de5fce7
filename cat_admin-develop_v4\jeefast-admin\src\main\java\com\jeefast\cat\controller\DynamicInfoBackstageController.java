package com.jeefast.cat.controller;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cat.modules.dynamic.entity.DynamicMedia;
import com.cat.modules.dynamic.service.IDynamicMediaService;
import com.cat.modules.topic.entity.TopicInfo;
import com.cat.modules.topic.service.ITopicInfoService;
import com.cat.util.BlankUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.jeefast.cat.domain.UserInfo;
import com.jeefast.cat.req.AddDynamicReq;
import com.jeefast.cat.req.BatchEditDynamicCategoryReq;
import com.jeefast.cat.req.EditDynamicReq;
import com.jeefast.cat.req.NewAddOrEditDynamicReq;
import com.jeefast.cat.service.IUserInfoService;
import com.jeefast.common.enums.AuditEnum;
import com.jeefast.common.enums.YesNoEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.framework.util.ShiroUtils;
import com.jeefast.system.domain.SysConfig;
import com.jeefast.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.ClientHttpRequest;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.DynamicInfoBackstage;
import com.jeefast.cat.service.IDynamicInfoBackstageService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 动态内容Controller
 *
 * <AUTHOR>
 * @date 2020-08-08
 */
@Controller
@RequestMapping("/cat/dynamicBackstage")
@Slf4j
public class DynamicInfoBackstageController extends BaseController {
    private String prefix = "cat/dynamicBackstage";
    @Autowired
    private RedisUtil redisUtil;
    private static final String TARGET_ADDR = "sys_config:sys.proxy.path";

    @Autowired
    private IDynamicInfoBackstageService dynamicInfoBackstageService;

    @Autowired
    private IDynamicMediaService dynamicMediaService;

    @Autowired
    private ISysConfigService configService;
    @Autowired
    private ITopicInfoService topicInfoService;
    @Autowired
    private IUserInfoService userInfoService;

    @RequiresPermissions("cat:dynamicBackstage:view")
    @GetMapping()
    public String dynamicBackstage(String circleId, String topicId, ModelMap mmap) {
        mmap.put("circleId", circleId);
        mmap.put("topicId", topicId);
        return prefix + "/dynamicBackstage";
    }

    /**
     * 查询动态内容列表
     */
    @RequiresPermissions(value = {"cat:dynamicBackstage:list","cat:dynamicBackstage:drafts",
            "cat:dynamicBackstage:audit:ai","cat:dynamicBackstage:audit:manual","cat:dynamicBackstage:audit:approved","cat:dynamicBackstage:add:page"}, logical = Logical.OR)
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(DynamicInfoBackstage req) {
        QueryWrapper<DynamicInfoBackstage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("1", "1");
        // 需要根据页面查询条件进行组装
        if (StringUtils.isNotEmpty(req.getDynamicId())) {
            queryWrapper.eq("t1.dynamic_id", req.getDynamicId());
        }
        if (StringUtils.isNotEmpty(req.getUserName())) {
            queryWrapper.like("t2.user_name", req.getUserName());
        }
        if (StringUtils.isNotEmpty(req.getUserId())) {
            queryWrapper.eq("t2.user_id", req.getUserId());
        }
        if (StringUtils.isNotNull(req.getSysUserId())) {
            queryWrapper.eq("t1.sys_user_id", req.getSysUserId());
        }
        if (StringUtils.isNotEmpty(req.getMobile())) {
            queryWrapper.like("t2.mobile", req.getMobile());
        }
        if (StringUtils.isNotEmpty(req.getTitle())) {
            queryWrapper.like("t1.title", req.getTitle());
        }
        if (StringUtils.isNotEmpty(req.getContent())) {
            queryWrapper.like("t1.content", req.getContent());
        }
        if (StringUtils.isNotEmpty(req.getArticleType())) {
            queryWrapper.like("t1.article_type", req.getArticleType());
        }
        if (BlankUtil.isNotEmpty(req.getType())) {
            queryWrapper.eq("t1.type", req.getType());
        }
        if (BlankUtil.isNotEmpty(req.getSource())) {
            queryWrapper.eq("t1.source", req.getSource());
        }
        if (ObjectUtil.isNotNull(req.getParams())) {
            if (BlankUtil.isNotEmpty(req.getParams().get("beginCreateDate"))) {
                queryWrapper.ge("t1.create_date", req.getParams().get("beginCreateDate"));
            }
            if (BlankUtil.isNotEmpty(req.getParams().get("endCreateDate"))) {
                queryWrapper.le("t1.create_date", req.getParams().get("endCreateDate"));
            }
            if (BlankUtil.isNotEmpty(req.getParams().get("circleId"))) {
                queryWrapper.eq("t1.circle_id", req.getParams().get("circleId"));
            }
        }
        if (BlankUtil.isNotEmpty(req.getDynamicCategoryId())) {
            queryWrapper.eq("t1.dynamic_category_id", req.getDynamicCategoryId());
        }
        if (BlankUtil.isNotEmpty(req.getIsCheck())) {
            queryWrapper.eq("t1.is_check", req.getIsCheck());
        }
        if (BlankUtil.isNotEmpty(req.getAuditStatus())) {
            queryWrapper.eq("t1.audit_status", req.getAuditStatus());
        }
        if (BlankUtil.isNotEmpty(req.getIsPublic())) {
            queryWrapper.eq("t1.is_public", req.getIsPublic());
        }
    	/*if(StringUtils.isNotEmpty(sysDemo.getUserName())) {
    		queryWrapper.like("user_name", sysDemo.getUserName());
    	}*/
        // 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        List resultPinned = new ArrayList<>();
        if (YesNoEnum.YES.getCode().equals(req.getNeedQueryTop())) {
            QueryWrapper<DynamicInfoBackstage> queryWrapperPinned = new QueryWrapper<>();
            queryWrapperPinned.eq("t1.is_top", YesNoEnum.YES.getCode());
            resultPinned = dynamicInfoBackstageService.dynamicPinnedList(queryWrapperPinned);

            queryWrapper.eq("t1.is_top", YesNoEnum.NO.getCode());
        }
        startPage();
        //queryWrapper.orderByDesc("t1.create_date");
        List<?> result = dynamicInfoBackstageService.dynamicList(queryWrapper);
        TableDataInfo dataTable = getDataTable(result);
        dataTable.getRows().addAll(0, resultPinned);
        return dataTable;
    }

    /**
     * 导出动态内容列表
     */
    @RequiresPermissions("cat:dynamicBackstage:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(DynamicInfoBackstage dynamicInfoBackstage) {
        List<DynamicInfoBackstage> list = dynamicInfoBackstageService.list(new QueryWrapper<>());
        ExcelUtil<DynamicInfoBackstage> util = new ExcelUtil<DynamicInfoBackstage>(DynamicInfoBackstage.class);
        return util.exportExcel(list, "dynamicBackstage");
    }

    /**
     * 新增动态内容
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存动态内容
     */
    @RequiresPermissions("cat:dynamicBackstage:add")
    @Log(title = "动态内容", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestBody AddDynamicReq req) throws IOException, URISyntaxException {
        boolean flag = dynamicInfoBackstageService.addSave(req);
        String targetAddr = redisUtil.get(TARGET_ADDR);
        log.info("发送请求地址：{}", targetAddr);
        String target = targetAddr + "/proxy/recommend/recommend/add";
        URI newUri = new URI(target);
        HttpMethod httpMethod = HttpMethod.resolve("POST");
        Map<String, Object> reqData = new HashMap<>();
        reqData.put("dynamic_id", req.getDynamicId());
        reqData.put("title", req.getTitle());
        reqData.put("content", req.getContent());
        ObjectMapper mapper = new ObjectMapper();
        String jsonStr = mapper.writeValueAsString(reqData);
        ClientHttpRequest delegate = new SimpleClientHttpRequestFactory().createRequest(newUri, httpMethod);
        delegate.getHeaders().add("authorization", req.getUserId());
        delegate.getHeaders().add("accept", "application/json");
        delegate.getHeaders().add("Content-Type", "application/json");
        delegate.getBody().write(jsonStr.getBytes(StandardCharsets.UTF_8));
        delegate.execute();
        log.info("========发送请求成功========");
        return toAjax(flag);
    }

    /**
     * 修改动态内容
     */
    @GetMapping("/edit/{dynamicId}")
    public String edit(@PathVariable("dynamicId") String dynamicId, ModelMap mmap) {
        DynamicInfoBackstage dynamicInfoBackstage = dynamicInfoBackstageService.getById(dynamicId);
        List<DynamicMedia> dynamicMediaList = dynamicMediaService.list(new QueryWrapper<DynamicMedia>().eq("dynamic_id", dynamicId).orderByAsc("sort_no"));
        //动态媒体url List
        List<String> dyMediaStrList = dynamicMediaList.stream().map(DynamicMedia::getMediaUrl).collect(Collectors.toList());

        String circleId = null;
        String circleName = null;
        mmap.put("circleId", circleId);
        mmap.put("circleName", circleName);
        mmap.put("dynamicInfoBackstage", dynamicInfoBackstage);
        mmap.put("dynamicMediaList", dynamicMediaList);
        mmap.put("dyMediaStrList", JSONObject.toJSONString(dyMediaStrList));
        return prefix + "/edit_new";
    }

    /**
     * 修改保存动态内容
     */
    @RequiresPermissions("cat:dynamicBackstage:edit")
    @Log(title = "动态内容", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(EditDynamicReq reqVo) {
        return toAjax(dynamicInfoBackstageService.updateInfo(reqVo));
    }


    /**
     * 批量修改动态分类
     */
    @RequiresPermissions("cat:dynamicBackstage:batchEditCategory")
    @Log(title = "批量修改动态分类", businessType = BusinessType.UPDATE)
    @PostMapping("/batch/edit/category")
    @ResponseBody
    public AjaxResult batchEditCategory(@RequestBody @Validated BatchEditDynamicCategoryReq req) {
        return toAjax(dynamicInfoBackstageService.update(new LambdaUpdateWrapper<DynamicInfoBackstage>()
                .set(DynamicInfoBackstage::getDynamicCategoryId, req.getCategoryId())
                .in(DynamicInfoBackstage::getDynamicId, req.getDynamicIdList())));
    }

    /**
     * 删除动态内容
     */
    @RequiresPermissions("cat:dynamicBackstage:remove")
    @Log(title = "动态内容", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        boolean falg = dynamicInfoBackstageService.delete(Arrays.asList(Convert.toStrArray(ids)));
        return toAjax(falg);
    }


    @RequiresPermissions(value = {"cat:dynamicBackstage:edit","cat:dynamicBackstage:audit:manual"},logical = Logical.OR)
    @Log(title = "驳回动态", businessType = BusinessType.OTHER)
    @PostMapping("/reasonsSub")
    @ResponseBody
    public AjaxResult reasonsSub(String dynamicId, String reasons) {
        return toAjax(dynamicInfoBackstageService.reasonsSub(dynamicId, reasons));
    }

    @RequiresPermissions("cat:dynamicBackstage:edit")
    @Log(title = "置顶", businessType = BusinessType.OTHER)
    @PostMapping("/pinned/{dynamicId}")
    @ResponseBody
    public AjaxResult pinned(@PathVariable("dynamicId") String dynamicId) {
        return toAjax(dynamicInfoBackstageService.pinned(dynamicId));
    }


    @RequiresPermissions("cat:dynamicBackstage:edit")
    @Log(title = "取消置顶", businessType = BusinessType.OTHER)
    @PostMapping("/unPinned/{dynamicId}")
    @ResponseBody
    public AjaxResult unPinned(@PathVariable("dynamicId") String dynamicId) {
        return toAjax(dynamicInfoBackstageService.unPinned(dynamicId));
    }

    @RequiresPermissions(value = {"cat:dynamicBackstage:edit","cat:dynamicBackstage:audit:manual"},logical = Logical.OR)
    @Log(title = "审核通过动态", businessType = BusinessType.OTHER)
    @PostMapping("/audit/pass")
    @ResponseBody
    public AjaxResult passDynamicId(String dynamicId, String reasons) {
        return toAjax(dynamicInfoBackstageService.passDynamicId(dynamicId, reasons));
    }

    @RequiresPermissions("cat:dynamicBackstage:add:page")
    @GetMapping("/new/addPage")
    public String newAdd(String circleId, String topicId, ModelMap mmap) {
        mmap.put("circleId", circleId);
        mmap.put("topicId", topicId);
        mmap.put("userId", ShiroUtils.getUserId());
        mmap.put("roles", ShiroUtils.getSysUser().getRoles());
        return prefix + "/addPage";
    }

    @RequiresPermissions("cat:dynamicBackstage:drafts")
    @GetMapping("/drafts")
    public String drafts(String circleId, String topicId, ModelMap mmap) {
        mmap.put("circleId", circleId);
        mmap.put("topicId", topicId);
        mmap.put("userId", ShiroUtils.getUserId());
        mmap.put("isCheck", YesNoEnum.NO.getCode());
        mmap.put("roles", ShiroUtils.getSysUser().getRoles());
        return prefix + "/drafts";
    }

    @RequiresPermissions("cat:dynamicBackstage:audit:ai")
    @GetMapping("/audit/ai")
    public String auditAi(String circleId,String topicId,ModelMap mmap) {
        mmap.put("circleId",circleId);
        mmap.put("topicId",topicId);
        mmap.put("userId", ShiroUtils.getUserId());
        mmap.put("isCheck", YesNoEnum.YES.getCode());
        mmap.put("auditStatus", AuditEnum.WAIT_AUDIT.getCode());
        return prefix + "/drafts";
    }

    @RequiresPermissions("cat:dynamicBackstage:audit:manual")
    @GetMapping("/audit/manual")
    public String auditManual(String circleId,String topicId,ModelMap mmap) {
        mmap.put("circleId",circleId);
        mmap.put("topicId",topicId);
        mmap.put("userId", ShiroUtils.getUserId());
        mmap.put("isCheck", YesNoEnum.YES.getCode());
        mmap.put("auditStatus", AuditEnum.TO_RE_AUDIT.getCode());
        return prefix + "/drafts";
    }

    @RequiresPermissions("cat:dynamicBackstage:audit:approved")
    @GetMapping("/audit/approved")
    public String auditApproved(String circleId,String topicId,ModelMap mmap) {
        mmap.put("circleId",circleId);
        mmap.put("topicId",topicId);
        mmap.put("userId", ShiroUtils.getUserId());
        mmap.put("isCheck", YesNoEnum.YES.getCode());
        mmap.put("auditStatus", AuditEnum.PASS.getCode());
        return prefix + "/drafts";
    }

    @GetMapping("/audit/audit")
    public String auditAudit(String circleId,String topicId,ModelMap mmap) {
        mmap.put("userId", ShiroUtils.getUserId());
        return prefix + "/audit";
    }

    /**
     * 新增保存动态内容
     */
    @RequiresPermissions(value = {"cat:dynamicBackstage:add:page","cat:dynamicBackstage:add"},logical = Logical.OR)
    @Log(title = "新增动态内容", businessType = BusinessType.INSERT)
    @PostMapping("/new/add")
    @ResponseBody
    public AjaxResult newAddSave(@RequestBody NewAddOrEditDynamicReq req) throws IOException, URISyntaxException {
        boolean flag = dynamicInfoBackstageService.newAddSave(req);
        String targetAddr = redisUtil.get(TARGET_ADDR);
        log.info("发送请求地址：{}", targetAddr);
        String target = targetAddr + "/proxy/recommend/recommend/add";
        URI newUri = new URI(target);
        HttpMethod httpMethod = HttpMethod.resolve("POST");
        Map<String, Object> reqData = new HashMap<>();
        reqData.put("dynamic_id", req.getDynamicId());
        reqData.put("title", req.getTitle());
        reqData.put("content", req.getContent());
        ObjectMapper mapper = new ObjectMapper();
        String jsonStr = mapper.writeValueAsString(reqData);
        ClientHttpRequest delegate = new SimpleClientHttpRequestFactory().createRequest(newUri, httpMethod);
        delegate.getHeaders().add("authorization", req.getUserId());
        delegate.getHeaders().add("accept", "application/json");
        delegate.getHeaders().add("Content-Type", "application/json");
        delegate.getBody().write(jsonStr.getBytes(StandardCharsets.UTF_8));
        delegate.execute();
        log.info("========发送请求成功========");
        return toAjax(flag);
    }


    /**
     * 获取动态内容
     */
    @GetMapping("/new/get/{dynamicId}")
    @ResponseBody
    public AjaxResult getById(@PathVariable("dynamicId") String dynamicId) {
        DynamicInfoBackstage dynamicInfoBackstage = dynamicInfoBackstageService.getById(dynamicId);
        List<DynamicMedia> dynamicMediaList = dynamicMediaService.list(new QueryWrapper<DynamicMedia>().eq("dynamic_id", dynamicId).orderByAsc("sort_no"));

        List<TopicInfo> topicInfoList = topicInfoService.getByBusinessTopicList(dynamicId, "1");
        UserInfo userInfo = userInfoService.getById(dynamicInfoBackstage.getUserId());
        if (ObjectUtil.isNotNull(userInfo)) {
            dynamicInfoBackstage.setUserName(userInfo.getUserName());
        }
        //动态媒体url List
//        List<String> dyMediaStrList = dynamicMediaList.stream().map(DynamicMedia::getMediaUrl).collect(Collectors.toList());
        String circleId = null;
        String circleName = null;
        Map<String, Object> mmap = new HashMap<>();
        mmap.put("circleId", circleId);
        mmap.put("circleName", circleName);
        mmap.put("dynamicInfoBackstage", dynamicInfoBackstage);
        mmap.put("dynamicMediaList", dynamicMediaList);
        mmap.put("topicInfoList", topicInfoList);
//        mmap.put("dyMediaStrList", JSONObject.toJSONString(dyMediaStrList));
        return AjaxResult.success(mmap);
    }

    /**
     * 修改保存动态内容
     */
    @RequiresPermissions("cat:dynamicBackstage:edit")
    @Log(title = "修改动态内容", businessType = BusinessType.UPDATE)
    @PostMapping("/new/edit")
    @ResponseBody
    public AjaxResult newEditSave(@RequestBody NewAddOrEditDynamicReq reqVo) {
        return toAjax(dynamicInfoBackstageService.newUpdateInfo(reqVo));
    }
}
