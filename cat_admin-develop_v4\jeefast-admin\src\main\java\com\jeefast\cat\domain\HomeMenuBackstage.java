package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                    import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 首页菜单表 home_menu
 *
 * <AUTHOR>
 * @date 2020-11-08
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("home_menu")
public class HomeMenuBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 菜单Id */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /** 菜单名称 */
    @Excel(name = "菜单名称")
    private String name;
    /** 图片地址 */
    @Excel(name = "图片地址")
    private String imageUrl;
    /** 连接地址 */
    private String href;
    /** 链接类型（0：内链、1：外链） */
    @Excel(name = "链接类型", readConverterExp = "0=：内链、1：外链")
    private String hrefType;
    /** 类别状态1-正常,2-已废弃 */
    @Excel(name = "类别状态1-正常,2-已废弃")
    private Integer status;
    /** 排序值 */
    @Excel(name = "排序值")
    private Integer sortOn;
    /** 业务类型:1=社区，2=商城 */
    @Excel(name = "业务类型:1=社区，2=商城")
    private String businessType;

    /**
     * 是否需要授权：0=否，1=是
     */
    @Excel(name = "是否需要授权：0=否，1=是")
    private String isAuth;
}
