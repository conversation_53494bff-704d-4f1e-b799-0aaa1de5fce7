<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.order.mapper.OrderMapper">

    <!-- 通用查询映射结果  配置了autoMapping=true,其他的无需配置，将会自动映射-->
    <resultMap id="BaseResultMap" type="com.cat.modules.order.entity.OrderInfo" autoMapping="true" />

    <select id="listExt" resultType="cn.hutool.core.map.CamelCaseMap">
        select t1.*,t2.user_name
        from order_info t1
        left join user_info t2 on t1.user_id=t2.user_id
        ${ew.customSqlSegment}
    </select>

    <select id="selectByPrimaryKey" resultType="com.cat.modules.order.thread.entity.OrderPrimary">
        select id,user_id,order_no,out_trade_no,business_id,business_type,payment,payment_type,status
        from order_info
        where id=#{orderId}
    </select>
</mapper>
