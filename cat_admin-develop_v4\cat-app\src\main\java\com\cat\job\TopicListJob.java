package com.cat.job;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cat.job.entity.TopicList;
import com.cat.modules.topic.entity.TopicInfo;
import com.cat.modules.topic.service.ITopicInfoService;
import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Component("topicListJob")
public class TopicListJob {

    @Autowired
    private ITopicInfoService topicInfoService;

    @Autowired
    private RedisUtil redisUtil;

    private String recommendKey = "we_chat:topicList";

    
    public void refreshRedis(){
        
        List<TopicInfo> topicInfoList = topicInfoService.list(new QueryWrapper<TopicInfo>().ge("cite_count",2).orderByDesc("cite_count"));
        List<TopicList> list = new ArrayList<>();
        for (TopicInfo topicInfo : topicInfoList) {
            String topicId = topicInfo.getTopicId();
            
            List<String> imageList = topicInfoService.imageList(topicId);
            
            if(imageList.size()<2){
                continue;
            }
            TopicList topicList = new TopicList();
            topicList.setTopicId(topicId);
            topicList.setJoinCount(topicInfo.getJoinCount());
            topicList.setCiteCount(topicInfo.getCiteCount());
            topicList.setTopicName(topicInfo.getTopicName());
            topicList.setImageList(imageList);
            list.add(topicList);
        }
        log.info("=====db获取话题列表完成======");
        redisUtil.del(recommendKey);
        for (int i = 0; i < list.size(); i++) {
            redisUtil.rpush(recommendKey, JSONObject.toJSONString(list.get(i)));
        }
        log.info("=====刷新话题redis缓存完成======");
    }









}
