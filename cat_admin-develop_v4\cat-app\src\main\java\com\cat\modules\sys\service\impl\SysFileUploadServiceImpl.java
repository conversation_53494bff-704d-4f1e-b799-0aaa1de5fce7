package com.cat.modules.sys.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.sys.entity.AppUpdateMessage;
import com.cat.modules.sys.mapper.AppUpdateMessageMapper;
import com.cat.modules.sys.mapper.SysFileUploadMapper;
import com.cat.modules.sys.service.IAppUpdateMessageService;
import com.cat.modules.sys.service.ISysFileUploadService;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.system.domain.SysFileUpload;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service

public class SysFileUploadServiceImpl extends ServiceImpl<SysFileUploadMapper, SysFileUpload> implements ISysFileUploadService {

    @Autowired
    private SysFileUploadMapper fileUploadMapper;

    @Override
    public int insertData(SysFileUpload reqVo) {
        return fileUploadMapper.insert(reqVo);
    }
}