package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;

import com.jeefast.cat.domain.Channel;
import com.jeefast.cat.domain.Hospital;
import com.jeefast.cat.service.IChannelService;
import com.jeefast.cat.service.IHospitalService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.BToB;
import com.jeefast.cat.service.IBToBService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 外部调用Controller
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@Controller
@RequestMapping("/cat/b2b")
public class BToBController extends BaseController {
    private String prefix = "cat/b2b";

    @Autowired
    private IBToBService bToBService;

    @Resource
    private IHospitalService hospitalService;

    @Resource
    private IChannelService channelService;

    @RequiresPermissions("cat:b2b:view")
    @GetMapping()
    public String b2b() {
        return prefix + "/b2b";
    }

    /**
     * 查询外部调用列表
     */
    @RequiresPermissions("cat:b2b:view")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(BToB bToB) {
        QueryWrapper<BToB> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        // 需要根据页面查询条件进行组装
    	/*if(StringUtils.isNotEmpty(sysDemo.getLoginName())) {
    		queryWrapper.like("login_name", sysDemo.getLoginName());
    	} 
    	if(StringUtils.isNotEmpty(sysDemo.getUserName())) {
    		queryWrapper.like("user_name", sysDemo.getUserName());
    	}*/
        // 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        return getDataTable(bToBService.list(queryWrapper));
    }

    /**
     * 导出外部调用列表
     */
    @RequiresPermissions("cat:b2b:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BToB bToB) {
        List<BToB> list = bToBService.list(new QueryWrapper<>());
        ExcelUtil<BToB> util = new ExcelUtil<BToB>(BToB.class);
        return util.exportExcel(list, "b");
    }

    /**
     * 新增外部调用
     */
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        mmap.put("channel_list", channelService.list(new QueryWrapper<Channel>().eq("is_delete", 0)));
        mmap.put("hospital_list", hospitalService.list(new QueryWrapper<Hospital>().eq("is_delete", 0)));
        return prefix + "/add";
    }

    /**
     * 新增保存外部调用
     */
    @RequiresPermissions("cat:b2b:add")
    @Log(title = "外部调用", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(BToB bToB) {
        return toAjax(bToBService.createInfo(bToB));
    }

    /**
     * 修改外部调用
     */
    @GetMapping("/edit/{userId}")
    public String edit(@PathVariable("userId") String userId, ModelMap mmap) {
        BToB bToB = bToBService.getById(userId);
        mmap.put("bToB", bToB);
        mmap.put("channel_list", channelService.list(new QueryWrapper<Channel>().eq("is_delete", 0)));
        mmap.put("hospital_list", hospitalService.list(new QueryWrapper<Hospital>().eq("is_delete", 0)));
        return prefix + "/edit";
    }

    /**
     * 修改保存外部调用
     */
    @RequiresPermissions("cat:b2b:edit")
    @Log(title = "外部调用", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(BToB bToB) {
        return toAjax(bToBService.updateInfo(bToB));
    }

    /**
     * 删除外部调用
     */
    @RequiresPermissions("cat:b2b:remove")
    @Log(title = "外部调用", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(bToBService.softDelete(ids));
    }
}
