package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                            import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 可兑换商品表 exchange_goods
 *
 * <AUTHOR>
 * @date 2020-08-09
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("exchange_goods")
public class ExchangeGoodsBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "exchange_goods_id", type = IdType.UUID)
    private String exchangeGoodsId;
    /** 商品名称 */
    @Excel(name = "商品名称")
    private String goodsName;
    /** 商品实际价格 */
    @Excel(name = "商品实际价格")
    private String goodsMoney;
    /** 商品兑换条件 */
    @Excel(name = "商品兑换条件")
    private Integer canNumber;
    /** 商品描述 */
    private String summary;
    /** 商品详情 */
    private String content;
    /** 已兑换人数 */
    @Excel(name = "已兑换人数")
    private Integer exchangeCount;
    /** 最大兑换数 */
    @Excel(name = "最大兑换数")
    private Integer maxCount;
    /** 商品图片 */
    @Excel(name = "商品图片")
    private String image;
    /** 排序值 */
    @Excel(name = "排序值")
    private Integer sort;
}
