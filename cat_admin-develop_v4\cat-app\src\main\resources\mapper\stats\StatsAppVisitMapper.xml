<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.stats.mapper.StatsAppVisitMapper">

    <!-- 通用查询映射结果  配置了autoMapping=true,其他的无需配置，将会自动映射-->
    <resultMap id="BaseResultMap" type="com.cat.modules.stats.entity.StatsAppVisit" autoMapping="true" />


    <select id="nowVisit" resultType="com.cat.modules.stats.entity.StatsAppVisit">
        select count(DISTINCT device_id) uv,count(*) pv
        from user_mobile_device
        where create_date>='2021-10-25 00:00:00' and create_date <![CDATA[ <= ]]> '2021-10-25 23:59:59'
    </select>

    <select id="realActiveBetweenDay" resultType="com.cat.util.DataRow">
        select count(DISTINCT device_id) uv,count(*) pv
        from user_mobile_device
        where event=#{event} and create_date <![CDATA[ >= ]]> #{start} and create_date  <![CDATA[ <= ]]>  #{end}
    </select>

    <select id="platformPieChart" resultType="com.cat.util.DataRow">
        select platform name,count(1) value
        from user_mobile_device
        where `event`='start'
        and create_date <![CDATA[ >= ]]> #{start} and create_date <![CDATA[ <= ]]> #{end}
        group by platform
    </select>

    <select id="versionPieChart" resultType="com.cat.util.DataRow">
        select version name,count(1) value
        from user_mobile_device
        where `event`='start'
        and create_date <![CDATA[ >= ]]> #{start} and create_date <![CDATA[ <= ]]> #{end}
        group by version
    </select>
    <select id="brandPieChart" resultType="com.cat.util.DataRow">
        select brand name,count(1) value
        from user_mobile_device
        where `event`='start'
        and create_date <![CDATA[ >= ]]> #{start} and create_date <![CDATA[ <= ]]> #{end}
        group by brand
    </select>
    <select id="channelPieChart" resultType="com.cat.util.DataRow">
        select channel name,count(1) value
        from user_mobile_device
        where `event`='start'
        and create_date <![CDATA[ >= ]]> #{start} and create_date <![CDATA[ <= ]]> #{end}
        group by channel
    </select>

    <select id="userTrendChart" resultType="com.cat.util.DataRow">
        select DATE_FORMAT(create_date, '%Y-%m-%d') date,count(DISTINCT device_id) count
        from user_mobile_device
        where `event`='start'
        and create_date <![CDATA[ >= ]]> #{start} and create_date <![CDATA[ <= ]]> #{end}
        group by DATE_FORMAT(create_date, '%Y-%m-%d')
    </select>

    <select id="registerUser" resultType="com.cat.util.DataRow">
        select DATE_FORMAT(create_date, '%Y-%m-%d') date,count(DISTINCT user_id) count
        from user_info
        where create_date <![CDATA[ >= ]]> #{start} and create_date <![CDATA[ <= ]]> #{end}
        group by DATE_FORMAT(create_date, '%Y-%m-%d')
    </select>
</mapper>
