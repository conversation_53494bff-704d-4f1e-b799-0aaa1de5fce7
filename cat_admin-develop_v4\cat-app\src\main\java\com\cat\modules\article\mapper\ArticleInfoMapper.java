package com.cat.modules.article.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cat.modules.article.entity.ArticleInfo;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Update;

import java.util.List;


public interface ArticleInfoMapper extends BaseMapper<ArticleInfo> {

    @Update("update article_info set weight = (2880 - TIMESTAMPDIFF(MINUTE,create_date, now()) + praise_count*10 + comment_count*16) where is_delete='0' and is_check='1'")
    int calculateWeight();

    List<CamelCaseMap> recommendList();
}
