package com.jeefast.cat.controller;

import com.jeefast.cat.service.IHotSearchService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;

/**
 * 热搜词条更新，给app端使用
 *
 * <AUTHOR>
 * @date 2025-03-11
 */
@RestController
@RequestMapping("/open/hot/search")
public class HotSearchRpcController extends BaseController {

    @Autowired
    private IHotSearchService hotSearchService;

    /**
     * 修改热搜词条
     */
    @GetMapping("/refresh/{delDynamicId}")
    @PermitAll
    public AjaxResult refreshTopTen(@PathVariable("delDynamicId") String delDynamicId) {
        hotSearchService.refreshTopTen(delDynamicId);
        return toAjax(true);
    }

}
