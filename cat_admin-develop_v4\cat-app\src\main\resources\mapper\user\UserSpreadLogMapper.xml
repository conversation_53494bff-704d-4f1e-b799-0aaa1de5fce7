<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.user.mapper.UserSpreadLogMapper">

    <!-- 通用查询映射结果  配置了autoMapping=true,其他的无需配置，将会自动映射-->
    <resultMap id="BaseResultMap" type="com.cat.modules.user.entity.UserSpreadLog" autoMapping="true" />


    <select id="logList" resultType="cn.hutool.core.map.CamelCaseMap">
        select t1.*,t2.user_name user_name,t3.user_name to_user_name
        from user_spread_log t1
        left join user_info t2 on t1.parent_user_id=t2.user_id
        left join user_info t3 on t1.user_id=t3.user_id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

</mapper>
