package com.cat.modules.user.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cat.modules.user.entity.UserSpreadLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface UserSpreadLogMapper extends BaseMapper<UserSpreadLog> {

    List<CamelCaseMap<String, Object>> logList(@Param("ew") Wrapper queryWrapper);
    
}
