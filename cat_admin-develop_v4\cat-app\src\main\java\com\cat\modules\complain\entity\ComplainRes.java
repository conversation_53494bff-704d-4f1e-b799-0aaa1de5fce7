package com.cat.modules.complain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ComplainRes extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "complain_res_id",type = IdType.UUID)
    @TableField("complain_res_id")
    private String complainResId;

    
    @TableField("complain_id")
    private String complainId;

    
    @TableField("media_url")
    private String mediaUrl;



}
