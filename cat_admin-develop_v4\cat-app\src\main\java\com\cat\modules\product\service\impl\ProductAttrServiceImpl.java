package com.cat.modules.product.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.product.entity.ProductAttr;
import com.cat.modules.product.mapper.ProductAttrMapper;
import com.cat.modules.product.service.IProductAttrService;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;


@Service

public class ProductAttrServiceImpl extends ServiceImpl<ProductAttrMapper, ProductAttr> implements IProductAttrService {

}