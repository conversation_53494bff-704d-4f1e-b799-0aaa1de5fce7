package com.cat.job.entity;

import cn.hutool.core.map.CamelCaseMap;
import com.cat.modules.topic.entity.TopicInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
public class PushDynamicInfoList {

    private String dynamicId;
    private String userId;
    private String userName;
    private String sex;
    
    private String petId;

    
    private int canNumber;
    
    private String avatarUrl;
    private Integer level;
    
    private String approve;
    private String content;
    private String addr;
    private Integer praiseCount;
    private Integer commentCount;
    
    private Integer mediaCount;
    private Date createDate;
    
    private String type;

    
    private String isAttention;

    
    private String isPraise="0";

    
    private int height;

    
    private int width;

    
    private String coverImage;

    
    private String source;
    private List<CamelCaseMap> imageList;
    private List<CamelCaseMap> videoList;
    private List<TopicInfo> topicList;

}
