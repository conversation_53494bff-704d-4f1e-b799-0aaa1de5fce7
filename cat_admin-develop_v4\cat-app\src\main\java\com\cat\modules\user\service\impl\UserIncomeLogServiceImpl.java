package com.cat.modules.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.user.entity.UserIncomeLog;
import com.cat.modules.user.mapper.UserIncomeLogMapper;
import com.cat.modules.user.service.IUserIncomeLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class UserIncomeLogServiceImpl extends ServiceImpl<UserIncomeLogMapper, UserIncomeLog> implements IUserIncomeLogService {
    @Autowired
    private UserIncomeLogMapper userIncomeLogMapper;


    @Override
    public boolean addLog(String userId, int updateValue, String type, String remark) {
        int sort = 0;
        if(updateValue<0){
            sort = 1;
        }
        UserIncomeLog incomeLog = new UserIncomeLog();
        incomeLog.setTitle("系统");
        incomeLog.setUserId(userId);
        incomeLog.setSort(sort);
        incomeLog.setMoney(updateValue);
        incomeLog.setRemark(remark);
        
        incomeLog.setType(type);
        incomeLog.setStatus("3");
        boolean flag = this.save(incomeLog);
        return flag;
    }


}
