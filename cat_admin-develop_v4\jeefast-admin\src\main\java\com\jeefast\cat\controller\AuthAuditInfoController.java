package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cat.modules.audit.entity.AuthAuditInfo;
import com.cat.modules.audit.service.IAuthAuditInfoService;
import com.cat.modules.auth.entity.AuthInfo;
import com.cat.modules.auth.service.IAuthInfoService;
import com.cat.modules.user.entity.User;
import com.jeefast.cat.domain.UserInfo;
import com.jeefast.cat.service.IUserInfoService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 认证审核Controller
 * 
 * <AUTHOR>
 * @date 2020-08-26
 */
@Controller
@RequestMapping("/cat/audit")
public class AuthAuditInfoController extends BaseController {
    private String prefix = "cat/audit";

    @Autowired
    private IAuthAuditInfoService authAuditInfoService;


    @Autowired
    private IUserInfoService userInfoService;
    @Autowired
    private IAuthInfoService authInfoService;

    @RequiresPermissions("cat:audit:view")
    @GetMapping()
    public String audit() {
        return prefix + "/audit";
    }

    /**
     * 查询认证审核列表
     */
    @RequiresPermissions("cat:audit:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(AuthAuditInfo authAuditInfo) {
        QueryWrapper<AuthAuditInfo> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	/*if(StringUtils.isNotEmpty(sysDemo.getLoginName())) {
    		queryWrapper.like("login_name", sysDemo.getLoginName());
    	} 
    	if(StringUtils.isNotEmpty(sysDemo.getUserName())) {
    		queryWrapper.like("user_name", sysDemo.getUserName());
    	}*/
		// 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        return getDataTable(authAuditInfoService.list(queryWrapper));
    }

    /**
     * 导出认证审核列表
     */
    @RequiresPermissions("cat:audit:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(AuthAuditInfo authAuditInfo) {
        List<AuthAuditInfo> list = authAuditInfoService.list(new QueryWrapper<>());
        ExcelUtil<AuthAuditInfo> util = new ExcelUtil<AuthAuditInfo>(AuthAuditInfo.class);
        return util.exportExcel(list, "audit");
    }

    /**
     * 新增认证审核
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }


    /**
     * 新增保存认证审核
     */
    @RequiresPermissions("cat:audit:add")
    @Log(title = "认证审核", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(AuthAuditInfo authAuditInfo) {
        return toAjax(authAuditInfoService.save(authAuditInfo));
    }

    /**
     * 修改认证审核
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        AuthAuditInfo authAuditInfo = authAuditInfoService.getById(id);
        mmap.put("authAuditInfo", authAuditInfo);
        return prefix + "/edit";
    }

    /**
     * 修改保存认证审核
     */
    @RequiresPermissions("cat:audit:edit")
    @Log(title = "认证审核", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(AuthAuditInfo authAuditInfo) {
        if (StrUtil.isBlank(authAuditInfo.getAuthIds())) {
            return toAjax(false);
        }
        QueryWrapper<AuthAuditInfo> wrap = new QueryWrapper<>();
        wrap.in("auth_id", authAuditInfo.getAuthIds().split(","));
        boolean flag = authAuditInfoService.update(authAuditInfo,wrap);
        if(flag){
            //查询用户id集合
            QueryWrapper<AuthInfo> authQw = new QueryWrapper<AuthInfo>();
            authQw.in("id", authAuditInfo.getAuthIds().split(","));
            List<AuthInfo> authList = authInfoService.list(authQw);
            //用户user_id集合
            List<String> getApplyUserIdList = authList.stream().map(AuthInfo::getApplyUserId).collect(Collectors.toList());

            //用户id更新大V标识字段
            UpdateWrapper<UserInfo> userUw = new UpdateWrapper<UserInfo>();
            userUw.set("approve",authAuditInfo.getStatus());
            userUw.in("user_id",getApplyUserIdList);
            userInfoService.update(userUw);
        }
        return toAjax(true);
    }

    /**
     * 删除认证审核
     */
    @RequiresPermissions("cat:audit:remove")
    @Log(title = "认证审核", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(authAuditInfoService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
