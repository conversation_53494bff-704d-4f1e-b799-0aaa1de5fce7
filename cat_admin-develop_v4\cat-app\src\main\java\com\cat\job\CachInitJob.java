package com.cat.job;

import com.cat.modules.sys.service.IAppUpdateMessageService;
import com.jeefast.common.enums.CachEnum;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.SpringContextHolder;
import com.jeefast.system.domain.SysConfig;
import com.jeefast.system.service.ISysConfigService;
import com.jeefast.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component("cachInitJob")
public class CachInitJob {

    
    public void initSysConfig(){
        RedisUtil redisUtil = SpringContextHolder.getBean(RedisUtil.class);
        ISysConfigService sysConfigService = SpringContextHolder.getBean(ISysConfigService.class);
        
        sysConfigService.initCachData();

        
        ISysDictDataService sysDictDataService = SpringContextHolder.getBean(ISysDictDataService.class);
        sysDictDataService.initCachData();

        
        IAppUpdateMessageService appUpdateService = SpringContextHolder.getBean(IAppUpdateMessageService.class);
        appUpdateService.refreshVersionCache();

    }


}
