package com.cat.modules.article.service.impl;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.article.entity.ArticleInfo;
import com.cat.modules.article.mapper.ArticleInfoMapper;
import com.cat.modules.article.service.IArticleInfoService;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service

public class ArticleInfoServiceImpl extends ServiceImpl<ArticleInfoMapper, ArticleInfo> implements IArticleInfoService {

    @Autowired
    private ArticleInfoMapper articleInfoMapper;

    @Override
    public int calculateWeight() {
        return articleInfoMapper.calculateWeight();
    }

    @Override
    public List<CamelCaseMap> recommendList() {
        return articleInfoMapper.recommendList();
    }
}
