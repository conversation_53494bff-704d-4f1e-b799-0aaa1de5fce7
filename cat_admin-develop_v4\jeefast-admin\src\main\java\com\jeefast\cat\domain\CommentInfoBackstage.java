package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                        import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 评论管理表 comment_info
 *
 * <AUTHOR>
 * @date 2020-08-17
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("comment_info")
public class CommentInfoBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** comment_id */
    @TableId(value = "comment_id", type = IdType.UUID)
    private String commentId;
    /** 父评论id */
    private String parentId;
    /** 父评论ids */
    private String parentIds;
    /** 评论内容 */
    @Excel(name = "评论内容")
    private String content;
    /** 评论对象ID */
    private String businessId;
    /** 评论对象类型（1：动态、2：文章） */
    @Excel(name = "评论对象类型", readConverterExp = "1=：动态、2：文章")
    private String businessType;
    /** 评论者ID */
    private String userId;
    /** 点赞数 */
    @Excel(name = "点赞数")
    private Integer praiseCount;
    /** 回复数 */
    @Excel(name = "回复数")
    private Integer replyCount;
    /** 是否后台审核通过 */
    private String isCheck;
    /** 是否删除 */
    private String isDelete;

}
