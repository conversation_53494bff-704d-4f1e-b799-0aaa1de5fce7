package com.cat.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jeefast.common.core.entity.CatBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class UserLogoutLog extends CatBaseEntity {

    private static final long serialVersionUID = 1L;

    
    @TableId("id")
    @TableField("id")
    private String id;

    
    @TableField("user_id")
    private String userId;

    
    @TableField("status")
    private Integer status;

    
    @TableField("apply_reason")
    private String applyReason;

    
    @TableField("reject_reason")
    private String rejectReason;

    
    @TableField("remark")
    private String remark;



}
