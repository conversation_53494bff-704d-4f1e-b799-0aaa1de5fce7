<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.product.mapper.ProductGroupValMapper">
    <!--
    <resultMap type="ProductGroupVal" id="ProductGroupValResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="value"    column="value"    />
        <result property="sortOn"    column="sort_on"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
        <result property="shopId"    column="shop_id"    />
    </resultMap>
    -->


    <select id="listExt" resultType="cn.hutool.core.map.CamelCaseLinkedMap">
        select t1.id,t1.product_id,t1.value,t1.sort_on,t1.key_id,t1.create_date,t1.update_date,t2.name as keyName
        from product_group_val t1
        left join product_group_key t2 on t2.id=t1.key_id
        ${ew.customSqlSegment}
    </select>
</mapper>

