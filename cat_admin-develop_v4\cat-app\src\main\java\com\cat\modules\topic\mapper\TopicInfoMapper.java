package com.cat.modules.topic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cat.modules.topic.entity.TopicInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface TopicInfoMapper extends BaseMapper<TopicInfo> {
    List<TopicInfo> getByBusinessTopicList(@Param("businessId") String businessId, @Param("type") String type);
    List<String> imageList(String topicId);
}
