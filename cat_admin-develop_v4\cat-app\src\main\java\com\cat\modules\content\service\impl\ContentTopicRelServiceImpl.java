package com.cat.modules.content.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.content.entity.ContentTopicRel;
import com.cat.modules.content.mapper.ContentTopicRelMapper;
import com.cat.modules.content.service.IContentTopicRelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class ContentTopicRelServiceImpl extends ServiceImpl<ContentTopicRelMapper, ContentTopicRel> implements IContentTopicRelService {
    @Autowired
    private ContentTopicRelMapper contentTopicRelMapper;



}
