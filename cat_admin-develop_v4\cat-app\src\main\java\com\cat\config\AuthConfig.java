package com.cat.config;

import cn.hutool.core.util.StrUtil;
import com.cat.base.enums.CachEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.SpringContextHolder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "weixin")
@ToString
@Slf4j
public class AuthConfig {

    
    private static String appid;
    private static String secret;
    private static String accessToken;

    
    private static String appidApp;
    private static String secretApp;
    private static String appAccessToken;

    public void setAppid(String appid) {
        AuthConfig.appid = appid;
    }

    public void setSecret(String secret) {
        AuthConfig.secret = secret;
    }

    public void setAppidApp(String appidApp) {
        AuthConfig.appidApp = appidApp;
    }

    public void setSecretApp(String secretApp) {
        AuthConfig.secretApp = secretApp;
    }


    public static String getAppid() {
        return appid;
    }

    public static String getSecret() {
        return secret;
    }

    public static String getAppidApp() {
        return appidApp;
    }

    public static String getSecretApp() {
        return secretApp;
    }


    private static void setAccessToken(String accessToken){
        AuthConfig.accessToken = accessToken;
    }

    public static String getAccessToken() {
        if(StrUtil.isEmpty(AuthConfig.accessToken)){
            RedisUtil redisUtil = SpringContextHolder.getBean(RedisUtil.class);
            return redisUtil.get(CachEnum.WE_CHAT_MINI_ACCESSTOKEN.getCode());
        }
        return AuthConfig.accessToken;
    }

    public static String getAppAccessToken() {
        return appAccessToken;
    }


}
