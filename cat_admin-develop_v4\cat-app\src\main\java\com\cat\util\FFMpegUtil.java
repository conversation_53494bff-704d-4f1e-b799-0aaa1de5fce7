package com.cat.util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.ArrayList;
import java.util.List;


@Component
@Slf4j
public class FFMpegUtil {

    
    @Value("${ffmpegEXE}")
    private String ffmpegEXE;

    
    


    
    public static void convetor(int time, String start, String inputPath, String outPath) throws Exception {
        List<String> command = new ArrayList<String>();
        command.add("/Users/<USER>/Documents/develop/tools/ffmpeg-4.2.1-macos64-static/bin/ffmpeg");
        if(0!=time){
            command.add("-t");
            command.add(String.valueOf(time));
        }
        if(start!=null&&!"00:00:00".equals(start)){
            command.add("-ss");
            command.add(start);
        }
        command.add("-i");
        command.add(inputPath);
        command.add("-b:v");
        command.add("50k");
        command.add("-r");
        command.add("2");
        
        
        command.add(outPath);
        ProcessBuilder builder = new ProcessBuilder(command);
        Process process = null;
        try {
            process = builder.start();
        } catch (IOException e) {
            e.printStackTrace();
        }
        
        InputStream errorStream = process.getErrorStream();
        InputStreamReader inputStreamReader = new InputStreamReader(errorStream);
        BufferedReader br = new BufferedReader(inputStreamReader);

        String line = "";
        while ((line = br.readLine()) != null) {
        }
        if (br != null) {
            br.close();
        }
        if (inputStreamReader != null) {
            inputStreamReader.close();
        }
        if (errorStream != null) {
            errorStream.close();
        }
    }


    
    public String processImg(String videoPath,int frame) throws IOException, InterruptedException {
        File file = new File(videoPath);
        if (!file.exists()) {
            log.error("路径[" + videoPath + "]对应的视频文件不存在!");
            System.err.println("路径[" + videoPath + "]对应的视频文件不存在!");
            return null;
        }
        List<String> commands = new ArrayList<String>();
        
        
        

        commands.add(ffmpegEXE);
        commands.add("-i");
        commands.add(videoPath);
        commands.add("-ss");
        commands.add("0.001");
        commands.add("-f");
        commands.add("image2");
        commands.add("-frames:v");
        commands.add(String.valueOf(frame));	
        commands.add("-y");
        String imgPath = videoPath.substring(0, videoPath.lastIndexOf(".")).replaceFirst("vedio", "file") + ".jpg";
        commands.add(imgPath);
        InputStream stderr =null;
        InputStreamReader isr =null;
        BufferedReader br=null;
        try {
            ProcessBuilder builder = new ProcessBuilder();
            builder.command(commands);
            Process process = builder.start();
            stderr = process.getErrorStream();
            isr = new InputStreamReader(stderr);
            br = new BufferedReader(isr);
            String line;
            while ((line = br.readLine()) != null);
            process.waitFor();
            System.out.println("截取成功");
            return imgPath;
        }finally {
            if (br != null)
                br.close();
            if (isr != null)
                isr.close();
            if (stderr != null)
                stderr.close();
        }
    }


    public static void main(String[] args) {
        String videoInputPath = "/Users/<USER>/Documents/文档/08.猫社/图片压缩下载临时目录/002E1Jm7lx07y2LBqysM010412003Fqj0E010.mp4";
        String coverOutputPath = "/Users/<USER>/Documents/文档/08.猫社/图片压缩下载临时目录/4.jpg";
        try {
            convetor(2,"00:00:01",videoInputPath,coverOutputPath);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}

