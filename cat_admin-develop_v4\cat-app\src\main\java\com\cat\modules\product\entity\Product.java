package com.cat.modules.product.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                                                                            import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;


 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product")
public class Product extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    
    @Excel(name = "类别id")
    private String categoryId;
    
    @Excel(name = "商品名称")
    private String name;
    
    private String subtitle;
    
    @Excel(name = "产品主图")
    private String mainimage;
    
    private String subimages;
    
    private String detail;
    
    @Excel(name = "价格")
    private Double price;
    
    @Excel(name = "原价格")
    private Double originalPrice;
    
    @Excel(name = "会员价格")
    private Double vipPrice;
    
    @Excel(name = "库存数量")
    private Integer stock;
    
    @Excel(name = "总销量")
    private Integer totalSales;
    
    @Excel(name = "推荐")
    private Integer isRecommend;
    
    @Excel(name = "状态")
    private Integer status;
    
    
    
    private Integer postage;
    
    
    
    private Date updateDate;
    
    private String shopId;
}
