package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.cat.modules.product.entity.ProductGroupKey;
import com.cat.modules.product.entity.ProductGroupVal;
import com.cat.modules.product.service.IProductGroupKeyService;
import com.cat.modules.product.service.IProductGroupValService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 商品sku_valController
 * 
 * <AUTHOR>
 * @date 2020-12-19
 */
@Controller
@RequestMapping("/cat/productGroupVal")
public class ProductGroupValController extends BaseController {
    private String prefix = "cat/productGroupVal";

    @Autowired
    private IProductGroupValService productGroupValService;
    @Autowired
    private IProductGroupKeyService productGroupKeyService;


    @RequiresPermissions("cat:productGroupVal:view")
    @GetMapping()
    public String productGroupVal(ModelMap mmap){
        mmap.put("groupKeyList",productGroupKeyService.list(new QueryWrapper<ProductGroupKey>()));
        return prefix + "/productGroupVal";
    }

    /**
     * 查询商品sku_val列表
     */
    @RequiresPermissions("cat:productGroupVal:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(ProductGroupVal req) {
        QueryWrapper<ProductGroupVal> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
        if(StringUtils.isNotEmpty(req.getProductId())) {
            queryWrapper.eq("product_id", req.getProductId());
        }
        if(StringUtils.isNotEmpty(req.getValue())) {
    		queryWrapper.like("value", req.getValue());
    	} 
        if(StringUtils.isNotEmpty(req.getShopId())) {
            queryWrapper.eq("shop_id", req.getShopId());
        }
        if(StringUtils.isNotEmpty(req.getKeyId())) {
            queryWrapper.eq("key_id", req.getKeyId());
        }
		// 特殊查询时条件需要进行单独组装
        Map<String, Object> params = req.getParams();
        if (StringUtils.isNotEmpty(params)) {
            queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginCreateDate")), "create_date", params.get("beginCreateDate"));
            queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endCreateDate")), "create_date", params.get("endCreateDate"));
        }
        startPage();
        return getDataTable(productGroupValService.listExt(queryWrapper));
    }

    /**
     * 导出商品sku_val列表
     */
    @RequiresPermissions("cat:productGroupVal:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(ProductGroupVal productGroupVal) {
        List<ProductGroupVal> list = productGroupValService.list(new QueryWrapper<>());
        ExcelUtil<ProductGroupVal> util = new ExcelUtil<ProductGroupVal>(ProductGroupVal.class);
        return util.exportExcel(list, "productGroupVal");
    }

    /**
     * 新增商品sku_val
     */
    @GetMapping("/add")
    public String add(ModelMap mmap) {
        mmap.put("groupKeyList",productGroupKeyService.list(new QueryWrapper<ProductGroupKey>()));
        return prefix + "/add";
    }

    /**
     * 新增保存商品sku_val
     */
    @RequiresPermissions("cat:productGroupVal:add")
    @Log(title = "商品sku_val", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(ProductGroupVal productGroupVal) {
        productGroupVal.setUpdateDate(new Date());
        return toAjax(productGroupValService.save(productGroupVal));
    }

    /**
     * 修改商品sku_val
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        ProductGroupVal productGroupVal = productGroupValService.getById(id);
        mmap.put("productGroupVal", productGroupVal);
        mmap.put("groupKeyList",productGroupKeyService.list(new QueryWrapper<ProductGroupKey>()));
        return prefix + "/edit";
    }

    /**
     * 修改保存商品sku_val
     */
    @RequiresPermissions("cat:productGroupVal:edit")
    @Log(title = "商品sku_val", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(ProductGroupVal productGroupVal) {
        productGroupVal.setUpdateDate(new Date());
        return toAjax(productGroupValService.updateById(productGroupVal));
    }

    /**
     * 删除商品sku_val
     */
    @RequiresPermissions("cat:productGroupVal:remove")
    @Log(title = "商品sku_val", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(productGroupValService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
