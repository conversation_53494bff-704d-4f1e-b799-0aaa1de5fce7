package com.jeefast.cat.controller;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.jeefast.common.config.Global;
import com.jeefast.common.enums.RedisEnum;
import com.jeefast.common.utils.RedisUtil;
import com.jeefast.common.utils.file.FileUploadUtils;
import com.jeefast.common.utils.file.UploadCloudFileUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.BannerInfoBackstage;
import com.jeefast.cat.service.IBannerInfoBackstageService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 首页banner图Controller
 * 
 * <AUTHOR>
 * @date 2020-08-09
 */
@Controller
@RequestMapping("/cat/bannerBackstage")
public class BannerInfoBackstageController extends BaseController {
    private String prefix = "cat/bannerBackstage";

    @Autowired
    private IBannerInfoBackstageService bannerInfoBackstageService;
    @Autowired
    private UploadCloudFileUtil uploadCloudFileUtil;
    @Autowired
    private RedisUtil redisUtil;


    @RequiresPermissions("cat:bannerBackstage:view")
    @GetMapping()
    public String bannerBackstage() {
        return prefix + "/bannerBackstage";
    }

    /**
     * 查询首页banner图列表
     */
    @RequiresPermissions("cat:bannerBackstage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(BannerInfoBackstage bannerInfoBackstage) {
        QueryWrapper<BannerInfoBackstage> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(bannerInfoBackstage.getHrefType())) {
    		queryWrapper.like("href_type", bannerInfoBackstage.getHrefType());
    	} 
		// 特殊查询时条件需要进行单独组装
		/*Map<String, Object> params = sysDemo.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("beginTime")), "create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "create_time", params.get("endTime"));
		}*/
        startPage();
        return getDataTable(bannerInfoBackstageService.list(queryWrapper));
    }

    /**
     * 导出首页banner图列表
     */
    @RequiresPermissions("cat:bannerBackstage:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(BannerInfoBackstage bannerInfoBackstage) {
        List<BannerInfoBackstage> list = bannerInfoBackstageService.list(new QueryWrapper<>());
        ExcelUtil<BannerInfoBackstage> util = new ExcelUtil<BannerInfoBackstage>(BannerInfoBackstage.class);
        return util.exportExcel(list, "bannerBackstage");
    }

    /**
     * 新增首页banner图
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存首页banner图
     */
    @RequiresPermissions("cat:bannerBackstage:add")
    @Log(title = "首页banner图", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@RequestParam("file") MultipartFile multipartFile, BannerInfoBackstage bannerInfoBackstage) throws IOException {
        if(multipartFile!=null&& StrUtil.isNotEmpty(multipartFile.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            bannerInfoBackstage.setImageUrl(cloudPath);
        }
        return toAjax(bannerInfoBackstageService.save(bannerInfoBackstage));
    }

    /**
     * 修改首页banner图
     */
    @GetMapping("/edit/{bannerId}")
    public String edit(@PathVariable("bannerId") String bannerId, ModelMap mmap) {
        BannerInfoBackstage bannerInfoBackstage = bannerInfoBackstageService.getById(bannerId);
        mmap.put("bannerInfoBackstage", bannerInfoBackstage);
        return prefix + "/edit";
    }

    /**
     * 修改保存首页banner图
     */
    @RequiresPermissions("cat:bannerBackstage:edit")
    @Log(title = "首页banner图", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@RequestParam("file") MultipartFile multipartFile, BannerInfoBackstage bannerInfoBackstage) throws IOException {

        if(multipartFile!=null&& StrUtil.isNotEmpty(multipartFile.getOriginalFilename())){
            //上传文件支持本地云端
            String cloudPath = FileUploadUtils.uploadExt(multipartFile);
            bannerInfoBackstage.setImageUrl(cloudPath);
        }

        return toAjax(bannerInfoBackstageService.updateById(bannerInfoBackstage));
    }

    /**
     * 删除首页banner图
     */
    @RequiresPermissions("cat:bannerBackstage:remove")
    @Log(title = "首页banner图", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(bannerInfoBackstageService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }
}
