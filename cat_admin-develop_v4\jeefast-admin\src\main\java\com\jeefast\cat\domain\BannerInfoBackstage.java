package com.jeefast.cat.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
                            import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 首页banner图表 banner_info
 *
 * <AUTHOR>
 * @date 2020-08-09
 */
 @Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("banner_info")
public class BannerInfoBackstage extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "banner_id", type = IdType.UUID)
    private String bannerId;
    /** 图片地址 */
    @Excel(name = "图片地址")
    private String imageUrl;
    /** 连接地址 */
    private String href;
    /** 链接类型（0：内链、1：外链） */
    @Excel(name = "链接类型", readConverterExp = "0=：内链、1：外链")
    private String hrefType;
    /** 排序值 */
    @Excel(name = "排序值")
    private Integer sortNo;
    /** 备注 */
    @Excel(name = "备注")
    private String remark;
    /** 业务类型:1=社区，2=商城 */
    @Excel(name = "业务类型:1=社区，2=商城")
    private String businessType;
}
