package com.jeefast.cat.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.DynamicContentSecurity;
import com.jeefast.cat.domain.DynamicInfoBackstage;
import com.jeefast.cat.domain.req.*;
import com.jeefast.cat.service.IDynamicContentSecurityAsyncService;
import com.jeefast.cat.service.IDynamicContentSecurityService;
import com.jeefast.cat.service.IDynamicInfoBackstageService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.core.page.TableDataInfo;
import com.jeefast.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 动态内容安全审核RPC接口
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@RestController
@RequestMapping("/open/cat/security")
public class DynamicContentSecurityRpcController extends BaseController {

    @Autowired
    private IDynamicContentSecurityAsyncService dynamicContentSecurityAsyncService;
    @Autowired
    private IDynamicInfoBackstageService dynamicInfoBackstageService;

    /**
     * 动态内容安全审核
     *
     * @param req 请求参数
     * @return 审核结果
     */
    @PostMapping("/dynamic")
    public AjaxResult syncDynamicContentSecurityCheck(@Valid @RequestBody DynamicContentCheckReq req) {
        DynamicInfoBackstage dynamic = dynamicInfoBackstageService.getById(req.getDynamicId());
        Boolean result = dynamicContentSecurityAsyncService
                .syncDynamicContentSecurityCheck(dynamic, req.getTitle(), req.getContent(),
                        req.getAddDynamicMediaId(), req.getDelDynamicMediaId());
        return AjaxResult.success(result);
    }

    /**
     * 查询动态内容安全审核记录列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@Valid @RequestBody DynamicContentCheckReq req) {
        QueryWrapper<DynamicContentSecurity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dynamic_id",req.getDynamicId());
        startPage();
        return getDataTable(dynamicContentSecurityAsyncService.list(queryWrapper));
    }
} 