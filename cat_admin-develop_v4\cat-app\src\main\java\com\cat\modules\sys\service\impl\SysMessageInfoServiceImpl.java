package com.cat.modules.sys.service.impl;

import cn.hutool.core.map.CamelCaseMap;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.job.entity.WxMessage;
import com.cat.modules.article.entity.ArticleInfo;
import com.cat.modules.article.service.IArticleInfoService;
import com.cat.modules.comment.entity.CommentInfo;
import com.cat.modules.comment.service.ICommentInfoService;
import com.cat.modules.dynamic.entity.DynamicInfo;
import com.cat.modules.dynamic.mapper.DynamicMediaMapper;
import com.cat.modules.dynamic.service.IDynamicInfoService;
import com.cat.modules.pet.entity.PetInfo;
import com.cat.modules.pet.service.IPetInfoService;
import com.cat.modules.push.entity.PushPayloadVo;
import com.cat.modules.push.entity.PushVo;
import com.cat.modules.sys.dto.SysEndMessageDTO;
import com.cat.modules.sys.entity.SysMessageInfo;
import com.cat.modules.sys.mapper.SysMessageInfoMapper;
import com.cat.modules.sys.service.ISysMessageInfoService;
import com.cat.modules.user.service.UserService;
import com.cat.util.DataRow;
import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class SysMessageInfoServiceImpl extends ServiceImpl<SysMessageInfoMapper, SysMessageInfo> implements ISysMessageInfoService {

    
    private String MESSAGE_KEY = "message:wxQueue";
    
    private String APP_MESSAGE_KEY = "message:AppQueue";

    @Autowired
    private SysMessageInfoMapper sysMessageInfoMapper;

    @Autowired
    private IDynamicInfoService dynamicInfoService;
    @Autowired
    private DynamicMediaMapper dynamicMediaMapper;

    @Autowired
    private IArticleInfoService articleInfoService;
    @Autowired
    private ICommentInfoService commentInfoService;
    @Autowired
    private IPetInfoService petInfoService;
    @Autowired
    private UserService userService;
    @Autowired
    private RedisUtil redisUtil;

    @Override
    @Async("asyncServiceExecutor")
    public void endMessage(String userId,String content, String businessId, String businessType,String type) {



    }

    @Override
    @Async("asyncServiceExecutor")
    public void endMessage(SysEndMessageDTO dto) {
        String bizId = dto.getBizId();
        String bizType = dto.getBizType();
        String userId = dto.getUserId();
        String content = dto.getContenet();
        String title = dto.getTitle();
        String type = dto.getType();
        
        String toUserId = null;
        String coverImage = null;
        String setCoverText = null;
        
        String busContent = null;
        
        if("1".equals(bizType)){
            
            DynamicInfo dynamicInfo = dynamicInfoService.getOne(new QueryWrapper<DynamicInfo>()
                    .eq("dynamic_id",bizId)
                    .select("user_id","type","cover_image","content")
            );
            toUserId = dynamicInfo.getUserId();
            coverImage = dynamicInfo.getCoverImage();
            if(dynamicInfo.getContent().length()>10){
                busContent = dynamicInfo.getContent().substring(0,10);
            }else{
                busContent = dynamicInfo.getContent();
            }
            
        }else if("2".equals(bizType)){
            
            ArticleInfo articleInfo = articleInfoService.getOne(new QueryWrapper<ArticleInfo>()
                    .eq("article_id",bizId)
                    .select("user_id","cover_image","title")
            );
            toUserId = articleInfo.getUserId();
            coverImage = articleInfo.getCoverImage();
            busContent = articleInfo.getTitle();
        }else if("3".equals(bizType)){
            
            CommentInfo commentInfo = commentInfoService.getOne(new QueryWrapper<CommentInfo>()
                    .eq("comment_id",bizId)
                    .select("user_id","content")
            );
            toUserId = commentInfo.getUserId();
            if(commentInfo.getContent().length()>10){
                setCoverText = commentInfo.getContent().substring(0,10);
            }else{
                setCoverText = commentInfo.getContent();
            }
            busContent = commentInfo.getContent();
        }
        SysMessageInfo sysMessageInfo = new SysMessageInfo();
        sysMessageInfo.setToUserId(toUserId);
        sysMessageInfo.setUserId(userId);
        sysMessageInfo.setContent(content);
        sysMessageInfo.setBusinessId(bizId);
        sysMessageInfo.setBusinessType(bizType);
        sysMessageInfo.setType(type);
        sysMessageInfo.setCoverImage(coverImage);
        sysMessageInfo.setCoverText(setCoverText);
        
        sysMessageInfoMapper.insert(sysMessageInfo);
        
        DataRow pushInfo = userService.getByIdPushInfo(toUserId);
        String opentId = pushInfo.getString("open_id");
        String platform = pushInfo.getString("platform");
        String pushId = pushInfo.getString("push_id");
        
        
        if(StrUtil.isNotBlank(opentId)){
            WxMessage wxMessage = new WxMessage();
            
            wxMessage.setType(type);
            wxMessage.setToOpenId(opentId);
            wxMessage.setToUserId(toUserId);
            wxMessage.setContent(content);
            wxMessage.setBusContent(busContent);
            wxMessage.setUserName("管理员");
            wxMessageSend(wxMessage);
        }
        
        if(StrUtil.isNotEmpty(pushId)){
            
            content = String.format("@%s %s","管理员",content);
            PushVo pushVo = new PushVo();
            pushVo.setCid(pushId);
            pushVo.setClientType(platform);
            pushVo.setContent(content);
            pushVo.setTitle(title);
            pushVo.setPayload(new PushPayloadVo(bizId,bizType,type));
            appMeesgaSend(pushVo);
        }
    }

    
    public boolean wxMessageSend(WxMessage wxMessage){
        
        long i = redisUtil.lpush(MESSAGE_KEY, JSONObject.toJSONString(wxMessage));
        if(i>0){
            return true;
        }
        return false;
    }

    
    @Override
    public boolean appMeesgaSend(PushVo pushVo){
        
        long i = redisUtil.lpush(APP_MESSAGE_KEY, JSONObject.toJSONString(pushVo));
        if(i>0){
            return true;
        }
        return false;
    }

}
