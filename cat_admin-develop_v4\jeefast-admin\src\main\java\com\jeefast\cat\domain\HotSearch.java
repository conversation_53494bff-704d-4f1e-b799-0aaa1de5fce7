package com.jeefast.cat.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jeefast.common.annotation.Excel;
import com.jeefast.common.annotation.Excel.ColumnType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.jeefast.common.core.entity.CatBaseEntity;
import com.jeefast.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 热搜词条表 hot_search
 *
 * <AUTHOR>
 * @date 2025-03-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("hot_search")
public class HotSearch extends CatBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * null
     */
    @TableId(value = "id", type = IdType.UUID)
    private String id;
    /**
     * 动态内容id
     */
    private String dynamicId;
    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;
    /**
     * 是否广告0否1是
     */
    @Excel(name = "是否广告", readConverterExp = "0=否,1=是")
    private String beAdvertising;
    /**
     * 热版类型 爆 热 新
     */
    @Excel(name = "热版类型", readConverterExp = "1=爆,2=热,3=新")
    private String hotType;
    /**
     * 最新阅读量
     */
    @Excel(name = "最新阅读量")
    private Integer totalReadNo;
    /**
     * 最新阅读量更新时间
     */
    @Excel(name = "最新阅读量更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date totalReadNoUpdateDate;
    /**
     * 实际阅读量
     */
    @Excel(name = "实际阅读量")
    private Integer realReadNo;
    /**
     * 虚拟阅读量
     */
    @Excel(name = "虚拟阅读量")
    private Integer inventedReadNo;
    /**
     * 排序值
     */
    @Excel(name = "排序值")
    private Integer sortNo;
    /**
     * 时效开始时间
     */
    @Excel(name = "时效开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date beginDate;
    /**
     * 时效结束时间
     */
    @Excel(name = "时效结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;
    /**
     * 跳转url
     */
    @Excel(name = "跳转链接")
    private String routeUrl;
    /**
     * 是否删除0否1是
     */
    private String isDelete;
    /**
     * null
     */
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField(value = "update_date",fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
}
