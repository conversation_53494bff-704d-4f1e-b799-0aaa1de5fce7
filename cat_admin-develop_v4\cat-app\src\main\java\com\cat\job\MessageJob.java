package com.cat.job;

import com.jeefast.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;


@Slf4j
@Component
public class MessageJob {

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private AppMessageJob appMessageJob;
    @Autowired
    private MediaFileCompress mediaFileCompress;

    
    private String APP_MESSAGE_KEY = "message:AppQueue";
    
    private String WX_MESSAGE_KEY = "message:wxQueue";
    
    private String MEDIA_MESSAGE_KEY="message:MediaQueue";

    public void execute(){
        try{
            while (true){
                
                List<String> mediaQueue = redisUtil.brpop(0,WX_MESSAGE_KEY,APP_MESSAGE_KEY,MEDIA_MESSAGE_KEY);
                String messageKey = mediaQueue.get(0);
                String messageSrt = mediaQueue.get(1);
                if(WX_MESSAGE_KEY.equals(messageKey)){
                    
                    
                }else if(APP_MESSAGE_KEY.equals(messageKey)){
                    
                    appMessageJob.run(messageSrt);
                }else if(MEDIA_MESSAGE_KEY.equals(messageKey)){
                    
                    mediaFileCompress.run(messageSrt);
                }
            }
        }catch (Exception e){
            log.error("轮循推送消息失败:",e);
        }
    }


    public void run() {
        Thread thread = new Thread(){
            public void run(){
                execute();
            }
        };
        thread.start();
    }

}
