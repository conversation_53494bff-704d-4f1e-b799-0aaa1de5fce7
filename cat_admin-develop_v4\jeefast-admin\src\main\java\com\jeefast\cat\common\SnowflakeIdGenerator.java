package com.jeefast.cat.common;


public class SnowflakeIdGenerator {

    // 起始时间戳，这里设置为2024-01-01 00:00:00 UTC的时间戳
    private static final long START_TIMESTAMP = 1704067200000L;

    // 机器ID所占的位数
    private static final int MACHINE_ID_BITS = 5;

    // 数据中心ID所占的位数
    private static final int DATA_CENTER_ID_BITS = 5;

    // 序列号所占的位数
    private static final int SEQUENCE_BITS = 12;

    // 机器ID的最大值
    private static final long MAX_MACHINE_ID = ~(-1L << MACHINE_ID_BITS);

    // 数据中心ID的最大值
    private static final long MAX_DATA_CENTER_ID = ~(-1L << DATA_CENTER_ID_BITS);

    // 序列号的最大值
    private static final long MAX_SEQUENCE = ~(-1L << SEQUENCE_BITS);

    // 机器ID向左移的位数
    private static final int MACHINE_ID_SHIFT = SEQUENCE_BITS;

    // 数据中心ID向左移的位数
    private static final int DATA_CENTER_ID_SHIFT = SEQUENCE_BITS + MACHINE_ID_BITS;

    // 时间戳向左移的位数
    private static final int TIMESTAMP_SHIFT = SEQUENCE_BITS + MACHINE_ID_BITS + DATA_CENTER_ID_BITS;

    // 数据中心ID
    private long dataCenterId;

    // 机器ID
    private long machineId;

    // 序列号
    private long sequence = 0L;

    // 上一次生成ID的时间戳
    private long lastTimestamp = -1L;

    public SnowflakeIdGenerator(long dataCenterId, long machineId) {
        if (dataCenterId > MAX_DATA_CENTER_ID || dataCenterId < 0) {
            throw new IllegalArgumentException("数据中心ID不合法");
        }
        if (machineId > MAX_MACHINE_ID || machineId < 0) {
            throw new IllegalArgumentException("机器ID不合法");
        }
        this.dataCenterId = dataCenterId;
        this.machineId = machineId;
        this.lastTimestamp = START_TIMESTAMP;
    }

    public synchronized long generateId() {
        long timestamp = getCurrentTimestamp();
        if (timestamp < lastTimestamp) {
            throw new RuntimeException("时间戳倒退，不合法");
        }

        if (timestamp == lastTimestamp) {
            sequence = (sequence + 1) % MAX_SEQUENCE;
            if (sequence == 0) {
                timestamp = waitNextMilliSecond(lastTimestamp);
            }
        } else {
            sequence = 0L;
        }

        lastTimestamp = timestamp;

        return ((timestamp - START_TIMESTAMP) << TIMESTAMP_SHIFT)
                | (dataCenterId << DATA_CENTER_ID_SHIFT)
                | (machineId << MACHINE_ID_SHIFT)
                | sequence;
    }

    private long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }

    private long waitNextMilliSecond(long lastTimestamp) {
        long timestamp = getCurrentTimestamp();
        while (timestamp <= lastTimestamp) {
            timestamp = getCurrentTimestamp();
        }
        return timestamp;
    }
}
