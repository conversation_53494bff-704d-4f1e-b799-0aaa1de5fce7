package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jeefast.common.constant.Constants;
import com.jeefast.common.enums.YesNoEnum;
import com.jeefast.common.exception.BusinessException;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.HotSearch;
import com.jeefast.cat.service.IHotSearchService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 热搜词条Controller
 *
 * <AUTHOR>
 * @date 2025-03-11
 */
@Controller
@RequestMapping("/cat/search")
public class HotSearchController extends BaseController {
    private String prefix = "cat/search";

    @Autowired
    private IHotSearchService hotSearchService;

    @RequiresPermissions("cat:search:view")
    @GetMapping()
    public String search() {
        return prefix + "/search";
    }

    /**
     * 查询热搜词条列表
     */
    @RequiresPermissions("cat:search:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(HotSearch hotSearch) {
        QueryWrapper<HotSearch> queryWrapper = getHotSearchQueryWrapper(hotSearch);
        startPage();
        return getDataTable(hotSearchService.list(queryWrapper));
    }

    /**
     * 导出热搜词条列表
     */
    @RequiresPermissions("cat:search:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(HotSearch hotSearch) {
        QueryWrapper<HotSearch> queryWrapper = getHotSearchQueryWrapper(hotSearch);
        List<HotSearch> list = hotSearchService.list(queryWrapper);
        ExcelUtil<HotSearch> util = new ExcelUtil<HotSearch>(HotSearch.class);
        return util.exportExcel(list, "热搜词条");
    }

    private static QueryWrapper<HotSearch> getHotSearchQueryWrapper(HotSearch hotSearch) {
        QueryWrapper<HotSearch> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", YesNoEnum.NO.getCode());
        queryWrapper.eq(StringUtils.isNotEmpty(hotSearch.getBeAdvertising()), "be_advertising", hotSearch.getBeAdvertising());
        queryWrapper.eq(StringUtils.isNotEmpty(hotSearch.getHotType()), "hot_type", hotSearch.getHotType());
        queryWrapper.like(StringUtils.isNotEmpty(hotSearch.getTitle()), "title", hotSearch.getTitle());
        queryWrapper.orderByAsc("sort_no");
        return queryWrapper;
    }

    /**
     * 新增热搜词条
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存热搜词条
     */
    @RequiresPermissions("cat:search:add")
    @Log(title = "热搜词条", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(HotSearch hotSearch) {
        if (hotSearch.getSortNo() > 10 || hotSearch.getSortNo() < 1) {
            throw new BusinessException("排序号只能是1-10的正整数");
        }
        int sameSortNO = hotSearchService.count(new LambdaQueryWrapper<HotSearch>()
                .eq(HotSearch::getBeAdvertising, YesNoEnum.YES.getCode())
                .eq(HotSearch::getIsDelete, YesNoEnum.NO.getCode())
                .eq(HotSearch::getSortNo, hotSearch.getSortNo()));
        if (sameSortNO > 0) {
            throw new BusinessException("广告排序号重复，请检查");
        }
        //添加的默认是广告
        hotSearch.setBeAdvertising(YesNoEnum.YES.getCode());
        hotSearch.setTotalReadNoUpdateDate(DateUtil.beginOfDay(new Date()));
        //初始等于设置的虚拟阅读量
        hotSearch.setTotalReadNo(hotSearch.getInventedReadNo());
        return toAjax(hotSearchService.save(hotSearch));
    }

    /**
     * 修改热搜词条
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        HotSearch hotSearch = hotSearchService.getById(id);
        mmap.put("hotSearch", hotSearch);
        return prefix + "/edit";
    }

    /**
     * 修改保存热搜词条
     */
    @RequiresPermissions("cat:search:edit")
    @Log(title = "热搜词条", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(HotSearch hotSearch) {
        if (hotSearch.getSortNo() > 10 || hotSearch.getSortNo() < 1) {
            throw new BusinessException("排序号只能是1-10的正整数");
        }
        int sameSortNO = hotSearchService.list(new LambdaQueryWrapper<HotSearch>()
                .eq(HotSearch::getBeAdvertising, hotSearch.getBeAdvertising())
                .eq(HotSearch::getIsDelete, YesNoEnum.NO.getCode())
                .eq(HotSearch::getSortNo, hotSearch.getSortNo())
                .ne(HotSearch::getId, hotSearch.getId())).size();
        if (sameSortNO > 0) {
            throw new BusinessException("同类型排序号重复，请检查");
        }
        HotSearch dbData = hotSearchService.getById(hotSearch.getId());
        hotSearch.setTotalReadNo(hotSearch.getInventedReadNo() + dbData.getRealReadNo());
        return toAjax(hotSearchService.updateById(hotSearch));
    }

    /**
     * 删除热搜词条
     */
    @RequiresPermissions("cat:search:remove")
    @Log(title = "热搜词条", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        boolean update = hotSearchService.update(new LambdaUpdateWrapper<HotSearch>()
                .in(HotSearch::getId, Arrays.asList(Convert.toStrArray(ids)))
                .set(HotSearch::getIsDelete, YesNoEnum.YES.getCode()));
        return toAjax(update);
    }
}
