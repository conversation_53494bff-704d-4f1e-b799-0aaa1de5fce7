package com.cat.modules.dynamic.service.impl;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.dynamic.entity.DynamicInfo;
import com.cat.modules.dynamic.mapper.DynamicInfoMapper;
import com.cat.modules.dynamic.resp.DynamicViewRespVo;
import com.cat.modules.dynamic.service.IDynamicInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class DynamicInfoServiceImpl extends ServiceImpl<DynamicInfoMapper, DynamicInfo> implements IDynamicInfoService {
    @Autowired
    private DynamicInfoMapper dynamicInfoMapper;

    @Override
    
    public int calculateWeight() {
        return dynamicInfoMapper.calculateWeight();
    }

    @Override
    
    public List<CamelCaseMap> recommendList() {
        return dynamicInfoMapper.recommendList();
    }

    @Override
    public List<DynamicViewRespVo> viewCount(List<String> dynamicIds, String startTime, String endTime) {
        return dynamicInfoMapper.viewCount(dynamicIds, startTime, endTime);
    }

    /**
     * 根据浏览量获取前十的动态信息
     *
     * @return
     */
    @Override
    public List<DynamicInfo> getTopTen() {
        return dynamicInfoMapper.getTopTen();
    }
}
