package com.cat.modules.dynamic.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cat.modules.dynamic.entity.DynamicMedia;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface DynamicMediaMapper extends BaseMapper<DynamicMedia> {

    List<CamelCaseMap> getDynamicMedia(@Param("dynamicId") String dynamicId, @Param("type") String type);

    int updateSmallImage(@Param("mediaId") String mediaId, @Param("smallPath") String smallPath);
}
