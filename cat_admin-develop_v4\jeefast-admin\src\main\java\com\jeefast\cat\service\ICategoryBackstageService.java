package com.jeefast.cat.service;

import com.jeefast.cat.domain.CategoryBackstage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jeefast.common.core.domain.Ztree;
import com.jeefast.common.core.domain.ZtreeExt;

import java.util.List;

/**
 * 商品类别 服务层
 *
 * <AUTHOR>
 * @date 2020-11-09
 */
public interface ICategoryBackstageService extends IService<CategoryBackstage> {

    CategoryBackstage selectByCategoryId(String categoryId);

    List<ZtreeExt> selectDeptTree(CategoryBackstage categoryBackstage);
}