package com.cat.modules.product.service.impl;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.product.entity.Product;
import com.cat.modules.product.mapper.ProductMapper;
import com.cat.modules.product.service.IProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.dynamic.datasource.annotation.DS;

import java.util.List;


@Service

public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements IProductService {
    @Autowired
    private ProductMapper productMapper;

    @Override
    public List<CamelCaseMap> listExt(QueryWrapper<Product> qw) {
        return productMapper.listExt(qw);
    }
}