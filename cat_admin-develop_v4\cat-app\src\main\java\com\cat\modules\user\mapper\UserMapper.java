package com.cat.modules.user.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cat.modules.user.entity.User;
import com.cat.util.DataRow;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface UserMapper extends BaseMapper<User> {


    List<User> queryUserInfo();

    String getUserId();

    List<String> randUserAvatar(int i);

    String getRandPetUserId();

    String getByOpenId(String toUserId);

    DataRow getByIdPushInfo(String userId);

    Integer updateFamilyUserNewDynamicNum(@Param("familyUserId") String familyUserId, @Param("diffNum") Integer diffNum, @Param("dynamicDate") Date dynamicDate);
}
