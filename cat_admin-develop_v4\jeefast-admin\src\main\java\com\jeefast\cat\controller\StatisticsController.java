package com.jeefast.cat.controller;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cat.modules.sys.entity.RuleInfo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jeefast.cat.domain.Channel;
import com.jeefast.cat.domain.Hospital;
import com.jeefast.cat.req.AICountReq;
import com.jeefast.cat.req.AIDeptCountReq;
import com.jeefast.cat.resp.AIDeptCountResp;
import com.jeefast.cat.service.IChannelService;
import com.jeefast.cat.service.IHospitalService;
import com.jeefast.cat.service.IStatisticsService;
import com.jeefast.common.annotation.Log;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.core.page.TableDataInfo;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.common.json.JSONObject;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.framework.util.ShiroUtils;
import com.jeefast.system.domain.SysUser;
import com.jeefast.system.service.ISysUserService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Controller
@RequestMapping("/cat/statistics")
public class StatisticsController extends BaseController {

    private String prefix = "cat/statistics";

    @Resource
    private IStatisticsService statisticsService;

    @RequiresPermissions("cat:statistics:view")
    @GetMapping()
    public String countInfo(ModelMap mmap) {
        statisticsService.getModelMap(mmap);
        return prefix + "/index";
    }

    @RequiresPermissions("cat:statistics:dept")
    @GetMapping("dept")
    public String deptCountInfo(ModelMap mmap) {
        statisticsService.getModelMap(mmap);
        return prefix + "/dept";
    }

    @RequiresPermissions("cat:statistics:total")
    @GetMapping("total")
    public String deptTotalInfo(ModelMap mmap) {
        statisticsService.getModelMap(mmap);
        return prefix + "/total";
    }

    /**
     * AI分诊按时间统计
     */
    @RequiresPermissions("cat:statistics:view")
    @PostMapping("/count")
    @ResponseBody
    public AjaxResult count(AICountReq req) throws JsonProcessingException {
        JSONArray jsonArray = statisticsService.count(req);
        return AjaxResult.success(jsonArray);
    }

    /**
     * AI分诊按 时间/科室 统计
     */
    @RequiresPermissions("cat:statistics:view")
    @PostMapping("/dept-count")
    @ResponseBody
    public TableDataInfo deptCount(AIDeptCountReq req) throws JsonProcessingException {
        AIDeptCountResp resp = statisticsService.deptCount(req);
        return getDataTable(resp.getJsonArray(), resp.getTotal());
    }

    /**
     * AI分诊按 时间/科室 统计
     */
    @RequiresPermissions("cat:statistics:view")
    @PostMapping("/dept-total")
    @ResponseBody
    public AjaxResult deptTotal(AIDeptCountReq req) throws JsonProcessingException {
        JSONArray jsonArray = statisticsService.deptTotal(req);
        return AjaxResult.success(jsonArray);
    }
}
