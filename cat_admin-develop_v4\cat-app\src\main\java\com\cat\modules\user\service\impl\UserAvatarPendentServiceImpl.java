package com.cat.modules.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cat.modules.user.entity.UserAvatarPendent;
import com.cat.modules.user.mapper.UserAvatarPendentMapper;
import com.cat.modules.user.service.IUserAvatarPendentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class UserAvatarPendentServiceImpl extends ServiceImpl<UserAvatarPendentMapper, UserAvatarPendent> implements IUserAvatarPendentService {
    @Autowired
    private UserAvatarPendentMapper userAvatarPendentMapper;



}
