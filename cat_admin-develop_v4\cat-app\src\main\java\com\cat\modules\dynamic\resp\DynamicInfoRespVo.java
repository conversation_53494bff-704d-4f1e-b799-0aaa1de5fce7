package com.cat.modules.dynamic.resp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


@Slf4j
@Data
@Accessors(chain = true)
public class DynamicInfoRespVo {
    
    @TableId(value = "dynamic_id")
    @TableField("dynamic_id")
    private String dynamicId;
    
    @TableField("title")
    private String title;

    @TableField("article_type")
    private String articleType;
    
    @TableField("content")
    private String content;
    
    @TableField("source")
    private String source="0";
    
    @TableField("cover_image")
    private String coverImage;

    private String userName;

    private List<String> questionList;

}
