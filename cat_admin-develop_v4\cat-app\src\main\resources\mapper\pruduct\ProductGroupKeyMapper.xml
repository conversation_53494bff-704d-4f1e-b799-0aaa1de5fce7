<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.product.mapper.ProductGroupKeyMapper">
    <!--
    <resultMap type="ProductGroupKey" id="ProductGroupKeyResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="name"    column="name"    />
        <result property="sortOn"    column="sort_on"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateDate"    column="update_date"    />
        <result property="shopId"    column="shop_id"    />
    </resultMap>
    -->

</mapper>

