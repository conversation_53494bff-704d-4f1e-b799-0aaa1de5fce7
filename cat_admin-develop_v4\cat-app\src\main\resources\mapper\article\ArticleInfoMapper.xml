<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cat.modules.article.mapper.ArticleInfoMapper">

    <!-- 通用查询映射结果  配置了autoMapping=true,其他的无需配置，将会自动映射-->
    <resultMap id="BaseResultMap" type="com.cat.modules.article.entity.ArticleInfo" autoMapping="true" />


    <select id="recommendList" resultType="cn.hutool.core.map.CamelCaseMap">
        select t2.user_id,t2.sex,t2.avatar_url,t2.user_name,t2.level,t2.approve
        ,t1.title,t1.is_recommend,t1.praise_count,t1.comment_count,t1.create_date
        ,t1.type,t1.article_id,t1.cover_image,t1.weight,t1.width,t1.height,'article' as 'group'
        from article_info t1
        left join user_info t2 on t2.user_id=t1.user_id
        where t1.type!='9' and t1.state='2' and t1.is_check='1' and t1.is_delete='0'
    </select>

</mapper>
