package com.jeefast.cat.mapper;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jeefast.cat.domain.CommentInfoBackstage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 评论管理 数据层
 *
 * <AUTHOR>
 * @date 2020-08-17
 */
public interface CommentInfoBackstageMapper extends BaseMapper<CommentInfoBackstage> {

    List<CamelCaseMap> getList(@Param("ew") QueryWrapper qw);
}