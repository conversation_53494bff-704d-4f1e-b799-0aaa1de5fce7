package com.jeefast.cat.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.jeefast.common.annotation.Log;
import com.jeefast.common.enums.BusinessType;
import com.jeefast.cat.domain.UserExtractLogBackstage;
import com.jeefast.cat.service.IUserExtractLogBackstageService;
import com.jeefast.common.core.controller.BaseController;
import com.jeefast.common.core.domain.AjaxResult;
import com.jeefast.common.utils.StringUtils;
import com.jeefast.common.utils.poi.ExcelUtil;
import com.jeefast.common.core.text.Convert;
import com.jeefast.common.core.page.TableDataInfo;

/**
 * 用户提现记录Controller
 * 
 * <AUTHOR>
 * @date 2020-11-08
 */
@Controller
@RequestMapping("/cat/UserExtractLogBackstage")
public class UserExtractLogBackstageController extends BaseController {
    private String prefix = "cat/UserExtractLogBackstage";

    @Autowired
    private IUserExtractLogBackstageService userExtractLogBackstageService;

    @RequiresPermissions("cat:UserExtractLogBackstage:view")
    @GetMapping()
    public String UserExtractLogBackstage() {
        return prefix + "/UserExtractLogBackstage";
    }

    /**
     * 查询用户提现记录列表
     */
    @RequiresPermissions("cat:UserExtractLogBackstage:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(UserExtractLogBackstage req) {
        QueryWrapper<UserExtractLogBackstage> queryWrapper = new QueryWrapper<>();
    	// 需要根据页面查询条件进行组装
    	if(StringUtils.isNotEmpty(req.getUserId())) {
    		queryWrapper.eq("t1.user_id", req.getUserId());
    	} 
    	if(StringUtils.isNotEmpty(req.getStatus())) {
    		queryWrapper.eq("t1.status", req.getStatus());
    	}
        if(StringUtils.isNotEmpty(req.getExtractForm())) {
            queryWrapper.eq("t1.extract_form", req.getExtractForm());
        }
		// 特殊查询时条件需要进行单独组装
		Map<String, Object> params = req.getParams();
		if (StringUtils.isNotEmpty(params)) {
			queryWrapper.ge(StringUtils.isNotEmpty((String)params.get("startTime")), "t1.create_time", params.get("beginTime"));
			queryWrapper.le(StringUtils.isNotEmpty((String)params.get("endTime")), "t1.create_time", params.get("endTime"));
		}
        startPage();
        return getDataTable(userExtractLogBackstageService.infoList(queryWrapper));
    }

    /**
     * 导出用户提现记录列表
     */
    @RequiresPermissions("cat:UserExtractLogBackstage:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(UserExtractLogBackstage userExtractLogBackstage) {
        List<UserExtractLogBackstage> list = userExtractLogBackstageService.list(new QueryWrapper<>());
        ExcelUtil<UserExtractLogBackstage> util = new ExcelUtil<UserExtractLogBackstage>(UserExtractLogBackstage.class);
        return util.exportExcel(list, "UserExtractLogBackstage");
    }

    /**
     * 新增用户提现记录
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存用户提现记录
     */
    @RequiresPermissions("cat:UserExtractLogBackstage:add")
    @Log(title = "用户提现记录", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(UserExtractLogBackstage userExtractLogBackstage) {
        return toAjax(userExtractLogBackstageService.save(userExtractLogBackstage));
    }

    /**
     * 修改用户提现记录
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") String id, ModelMap mmap) {
        UserExtractLogBackstage userExtractLogBackstage = userExtractLogBackstageService.getById(id);
        mmap.put("userExtractLogBackstage", userExtractLogBackstage);
        return prefix + "/edit";
    }

    /**
     * 修改保存用户提现记录
     */
    @RequiresPermissions("cat:UserExtractLogBackstage:edit")
    @Log(title = "用户提现记录", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(UserExtractLogBackstage userExtractLogBackstage) {
        return toAjax(userExtractLogBackstageService.updateById(userExtractLogBackstage));
    }

    /**
     * 删除用户提现记录
     */
    @RequiresPermissions("cat:UserExtractLogBackstage:remove")
    @Log(title = "用户提现记录", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(userExtractLogBackstageService.removeByIds(Arrays.asList(Convert.toStrArray(ids))));
    }

    /**
     * 审核提现
     */
    @GetMapping("/auth/{id}")
    public String auth(@PathVariable("id") String id, ModelMap mmap) {
        UserExtractLogBackstage userExtractLogBackstage = userExtractLogBackstageService.getById(id);
        mmap.put("userExtractLogBackstage", userExtractLogBackstage);
        return prefix + "/auth";
    }


    /**
     * 审核提现
     */
    @RequiresPermissions("cat:UserExtractLogBackstage:edit")
    @Log(title = "用户提现记录", businessType = BusinessType.UPDATE)
    @PostMapping("/auth")
    @ResponseBody
    public AjaxResult authSave(String id,String status,String reason,String remark) {
        boolean flag = userExtractLogBackstageService.authSave(id,status,reason,remark);
        return toAjax(flag);
    }

}
