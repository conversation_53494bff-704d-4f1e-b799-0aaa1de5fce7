package com.cat.modules.article.service;

import cn.hutool.core.map.CamelCaseMap;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cat.modules.article.entity.ArticleInfo;
import com.jeefast.common.annotation.DataSource;
import com.jeefast.common.enums.DataSourceType;

import java.util.List;


public interface IArticleInfoService extends IService<ArticleInfo> {

    
    int calculateWeight();

    
    List<CamelCaseMap> recommendList();
}
